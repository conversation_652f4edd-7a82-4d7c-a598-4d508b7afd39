package com.covet.profile.dto.CapRxDto.pharmacy;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

@JsonSerialize(as = ImmutableCapLocationResDto.class)
@JsonDeserialize(as = ImmutableCapLocationResDto.class, builder = ImmutableCapLocationResDto.Builder.class)
@Value.Immutable
public interface CapLocationResDto {

    @Value.Parameter
    @JsonProperty("latitude")
    String getLatitude();

    @Value.Parameter
    @JsonProperty("longitude")
    String getLongitude();

    @Value.Parameter
    @JsonProperty("search_address")
    @JsonAlias("address")
    String getSearchAddress();

}
