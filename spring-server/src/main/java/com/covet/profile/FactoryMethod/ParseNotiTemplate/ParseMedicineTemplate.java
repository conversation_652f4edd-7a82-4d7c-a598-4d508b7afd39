package com.covet.profile.FactoryMethod.ParseNotiTemplate;

import com.covet.profile.dto.reminder.template.MedicineTemplateDto;
import com.covet.profile.service.PrescriptionService;
import com.covet.profile.systemEnum.EDefaultForm;
import com.covet.profile.systemEnum.ETemplateType;
import com.covet.profile.utils.JsonUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

@Service
public class ParseMedicineTemplate implements IParseNotiTemplate {
    @Override
    public MedicineTemplateDto parse(String json) {
        var defaultImage = PrescriptionService.defaultImageMap;
        var medicineDto = JsonUtils.toObj(json, MedicineTemplateDto.class);
        if(Objects.nonNull(medicineDto)) {

            var form = Optional.ofNullable(medicineDto.getForm()).orElse("");
            var imgUrl = Optional.ofNullable(defaultImage.get(form))
                    .orElse(defaultImage.get(EDefaultForm.OTHERS.getValue()));

            medicineDto.setImgUrl(imgUrl.toString());
            return medicineDto;
        }
        return new MedicineTemplateDto();
    }

    @Override
    public ETemplateType getTemplateType() {
        return ETemplateType.MEDICINE;
    }
}
