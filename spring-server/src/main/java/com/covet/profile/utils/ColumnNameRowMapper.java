package com.covet.profile.utils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

public class ColumnNameRowMapper<T> implements RowMapper<T> {
    private final Class<T> mappedClass;

    public ColumnNameRowMapper(Class<T> mappedClass) {
        this.mappedClass = mappedClass;
    }

    @Override
    public T mapRow(ResultSet rs, int rowNum) throws SQLException {
        T obj;
        try {
            obj = mappedClass.getDeclaredConstructor().newInstance();
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException
                | NoSuchMethodException e) {
            throw new SQLException("Failed to create object instance", e);
        }
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();
        for (int i = 1; i <= columnCount; i++) {
            String columnName = metaData.getColumnName(i);
            try {
                Field field = mappedClass.getDeclaredField(columnName);
                field.setAccessible(true);
                Object value = rs.getObject(i);
                field.set(obj, value);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                // ignore columns that don't have matching fields in the object
            }
        }
        return obj;
    }
}