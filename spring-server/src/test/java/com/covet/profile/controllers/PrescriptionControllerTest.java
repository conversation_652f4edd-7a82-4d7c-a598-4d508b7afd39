package com.covet.profile.controllers;

import com.covet.profile.clients.vba.VBADtos.Member.SubscriberMemberDto;
import com.covet.profile.dto.CapRxDto.member.MemberDto;
import com.covet.profile.dto.covet.pharmacy.ImmutablePharmacyDetailDto;
import com.covet.profile.dto.covet.pharmacy.PharmacyDetailDto;
import com.covet.profile.dto.covet.prescription.ImmutablePrescriptionDetailDto;
import com.covet.profile.dto.covet.prescription.ImmutablePrescriptionRequestDto;
import com.covet.profile.dto.covet.prescription.PrescriptionDetailDto;
import com.covet.profile.dto.covet.prescription.PrescriptionRequestDto;
import com.covet.profile.persistence.compositeKey.PrescriptionID;
import com.covet.profile.controller.PrescriptionController;
import com.covet.profile.dto.CapRxDto.pharmacy.CapPharmacyDto;
import com.covet.profile.dto.CapRxDto.plan.CapRxGroupDto;
import com.covet.profile.dto.CapRxDto.plan.CapRxMemberDto;
import com.covet.profile.dto.CapRxDto.plan.CapRxMemberListResponseDto;
import com.covet.profile.dto.CapRxDto.plan.CapRxPlanDto;
import com.covet.profile.dto.ProviderDto.LocationDto;
import com.covet.profile.exception.ResourceNotFoundException;
import com.covet.profile.persistence.model.PharmacyListWithMessage;
import com.covet.profile.persistence.model.Prescription;
import com.covet.profile.service.*;
import com.covet.profile.service.interfaces.ICapitalRxService;
import com.covet.profile.systemEnum.SysEnum;
import com.covet.profile.clients.vba.VBAService.VBAUserService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultMatcher;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@RunWith(SpringRunner.class)
@WebMvcTest(PrescriptionController.class)
@AutoConfigureMockMvc(addFilters = false)
public class PrescriptionControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PrescriptionService prescriptionService;

    @MockBean
    private PharmacyService pharmacyService;
    @MockBean
    private ProfileService profileService;

    @MockBean
    private VBAUserService vbaUserService;

    @MockBean
    private ICapitalRxService capitalRxService;

    @MockBean
    private Auth0Service auth0Service;

    @MockBean
    private PrescriptionPharmacyService prescriptionPharmacyService;

    private static final UUID profileUUID = UUID.randomUUID();

    private static final UUID patientID = UUID.fromString("e6ee7e75-4381-47ef-95e7-b3179d0e7ab7");

    private static final UUID physicianID = UUID.fromString("e6ee7e75-4381-47ef-95e7-b3179d0e7ab7");

    @Before
    public void setUp() throws Exception {

        // Mock the behavior of getAuth0Id to return a test-specific value
        // Mock the behavior of AuthUtils.getAuth0Id() to return a valid Auth0 user ID
        when(auth0Service.getAuth0Id()).thenReturn("valid-jwt-token");

        // Mock the behavior of AuthUtils.getCognitoId() to return a valid UUID
        when(auth0Service.getCognitoId()).thenReturn(profileUUID);

        // Mock the behavior of auth0Service.getUserEmail to return a valid email address
        when(auth0Service.getUserEmail("valid-jwt-token")).thenReturn("<EMAIL>");

        var mockSubscriberMemberDto = createSampleSubscriberMemberDto();
        var mockMember= createSampleCapRxMemberDto();
        CapRxMemberListResponseDto memberList = new CapRxMemberListResponseDto();

        memberList.setMemberList(Arrays.asList(mockMember));

        when(capitalRxService.getMember(any())).thenReturn(memberList);

        when(vbaUserService.getSubscriberMemberInfo("<EMAIL>")).thenReturn(mockSubscriberMemberDto); //mock subscriberID
    }

    @WithMockUser
    @Test
    public void findDrugsPharmacies_ValidRequest_PharmaciesNotFound() throws Exception {
        // Arrange
        PrescriptionRequestDto requestDto = createPrescriptionRequestDto();

        PharmacyListWithMessage emptyPharmacyListWithMessage = new PharmacyListWithMessage(new ArrayList<>(), "No pharmacies found");
        when(pharmacyService.getCapRxPharmacyListByPrescriptionToAdd(
                eq(new PrescriptionID(requestDto.getPrescriptionName(), requestDto.getPrescriptionCode(), profileUUID)),
                any(),
                any()))
                .thenReturn(emptyPharmacyListWithMessage);

        // Act and Assert
        performGetDrugPharmacies(requestDto, "No pharmacies found", status().isOk());
    }

    @WithMockUser
    @Test
    public void findDrugsPharmacies_ValidRequest_ResourcesNotFoundException() throws Exception {
        // Arrange
        PrescriptionRequestDto requestDto = createPrescriptionRequestDto();

        when(capitalRxService.getDrugPrice(any())).thenReturn(null);

        when(pharmacyService.getCapRxPharmacyListByPrescriptionToAdd(
                eq(new PrescriptionID(requestDto.getPrescriptionName(), requestDto.getPrescriptionCode(), profileUUID)),
                eq(Optional.empty()),
                any())).thenThrow(new ResourceNotFoundException("No pharmacies prices found for this drug"));

        // Act and Assert
        performGetDrugPharmacies(requestDto, "No pharmacies prices found for this drug", status().isNotFound());
    }

    @WithMockUser
    @Test
    public void getPrescriptionPharmacyDetailById_ValidRequest_Success() throws Exception {
        // Arrange
        String prescriptionName = "metFORMIN HCl";
        String prescriptionCode = "65862000901";
        String pharmacyID = "10888";
        UUID patientID = UUID.randomUUID();
        PrescriptionDetailDto prescriptionDetail = createSamplePrescriptionDetailDto(); // You can initialize with necessary data
        PharmacyDetailDto pharmacyDetail = createSamplePharmacyDetailDto(); // You can initialize with necessary data
        String expectedPharmacyName = "SAGAN AMOT PHARMACY";
        String expectedPharmacyNpi = "**********";
        // Act
        when(auth0Service.getCognitoId()).thenReturn(patientID);

        when(prescriptionService.getPrescriptionDetail(prescriptionName, prescriptionCode, patientID)).thenReturn(prescriptionDetail);
        when(pharmacyService.getPharmacyByIdAndPrescriptionNameAndCode(any(), any(), any())).thenReturn(pharmacyDetail);

        // Assert
        mockMvc.perform(get("/api/prescription/pharmacy/detail")
                        .param("prescriptionName", prescriptionName)
                        .param("prescriptionCode", prescriptionCode)
                        .param("pharmacyID", pharmacyID)
                        .param("location", String.valueOf(new LocationDto("0.0", "0.0")))
                        // Add other parameters as needed
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.pharmacy.pharmacy.name").value(expectedPharmacyName))
                .andExpect(jsonPath("$.pharmacy.pharmacy.npi").value(expectedPharmacyNpi));
    }

    private void performGetDrugPharmacies(PrescriptionRequestDto requestDto, String message, ResultMatcher status) throws Exception {

        int page = 0;
        int pageSize = 10;
        SysEnum.ESortType eSortType = SysEnum.ESortType.ASC;

        // Act and Assert
        mockMvc.perform(get("/api/v2/prescription/drug/pharmacies")
                        .param("prescriptionName", requestDto.getPrescriptionName())
                        .param("prescriptionCode", requestDto.getPrescriptionCode())
                        .param("quantity", requestDto.getQuantity().toString())
                        .param("form", requestDto.getForm())
                        .param("strength", requestDto.getStrength())
                        .param("drugId", requestDto.getDrugId().toString())
                        .param("page", String.valueOf(page))
                        .param("pageSize", String.valueOf(pageSize))
                        .param("eSortType", eSortType.toString())
                        .param("location", String.valueOf(new LocationDto("0.0", "0.0")))
                        // Add other parameters as needed
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status)
                .andExpect(jsonPath("$.message").value(message));
    }

    private CapRxMemberDto createSampleCapRxMemberDto() {
        CapRxMemberDto memberDto = new CapRxMemberDto();

        // Create and set a sample CapRxGroupDto object
        CapRxGroupDto groupDto = new CapRxGroupDto();
        groupDto.setAccumulationPeriodType("Monthly");
        groupDto.setAddressLine1("123 Main St");
        groupDto.setCity("New York");
        // Set other properties as needed
        memberDto.setGroup(groupDto);

        // Create and set a sample MemberDto object
        MemberDto member = new MemberDto();
        member.setFirstName("John");
        member.setLastName("Doe");
        member.setBirthDate(new Date());
        member.setExternalMemberId("639236184");
        // Set other properties as needed
        memberDto.setMember(member);

        // Create and set a sample CapRxPlanDto object
        CapRxPlanDto planDto = new CapRxPlanDto();
        planDto.setAbsenceBoundary("Monthly");
        planDto.setCity("Los Angeles");
        // Set other properties as needed
        memberDto.setPlan(planDto);

        return memberDto;
    }

    private SubscriberMemberDto createSampleSubscriberMemberDto() {
        SubscriberMemberDto subscriberMemberDto = new SubscriberMemberDto();
        subscriberMemberDto.setMemberSequence("01");
        subscriberMemberDto.setSubscriberId("639236184");
        return subscriberMemberDto;
    }

    private PrescriptionRequestDto createPrescriptionRequestDto() {
        return ImmutablePrescriptionRequestDto.builder()
                .prescriptionName("metFORMIN HCl")
                .prescriptionCode("65862000901")
                .quantity(60.0)
                .form("Tablet")
                .strength("850mg")
                .drugId(10888)
                .build();
    }

    private Prescription createSamplePrescription() {
        Prescription prescription = new Prescription();
        PrescriptionID prescriptionID = new PrescriptionID("metFORMIN HCl", "65862000901", patientID);

        prescription.setPrescriptionId(prescriptionID);
        prescription.setForm("Tablet");
        prescription.setQuantity(60);
        prescription.setStrength("850mg");
        prescription.setDrugID(10888);
        prescription.setPhysicianID(physicianID);

        return prescription;
    }

    private PrescriptionDetailDto createSamplePrescriptionDetailDto() {
        var prescription = createSamplePrescription();
        return ImmutablePrescriptionDetailDto.builder()
                .prescription(prescription)
                .isVerified(false)
                .isPArequired(false)
                .isNoPrescription(false)
                .prescriptionImgUrl(Optional.empty())
                .build();
    }

    private PharmacyDetailDto createSamplePharmacyDetailDto() {
        var pharmacy = createSampleCapPharmacyDto();
        return ImmutablePharmacyDetailDto.builder()
                .pharmacy(pharmacy)
                .cost(60.0)
                .distance(99.0)
                .build();
    }

    private CapPharmacyDto createSampleCapPharmacyDto() {
        CapPharmacyDto capPharmacyDto = new CapPharmacyDto();

        capPharmacyDto.setCity("AGAT");
        capPharmacyDto.setAddressLine1("875 N RT 2 STE A-106");
        capPharmacyDto.setAddressLine2("AGAT POINT COMMERCIAL CENTER");
        capPharmacyDto.setState("GU");
        capPharmacyDto.setPhone("**********");
        capPharmacyDto.setZip("96928");
        capPharmacyDto.setNpi("**********");
        capPharmacyDto.setNetworkStatus("N");
        capPharmacyDto.setLongitude(144.6635726);
        capPharmacyDto.setLatitude(13.400294);
        capPharmacyDto.setName("SAGAN AMOT PHARMACY");
        capPharmacyDto.setIs24Hours("N");

        return capPharmacyDto;
    }
}
