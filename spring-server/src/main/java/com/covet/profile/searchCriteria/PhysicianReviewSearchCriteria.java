package com.covet.profile.searchCriteria;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

import java.util.Optional;
import java.util.Set;

@Data
public class PhysicianReviewSearchCriteria {
    @Parameter(description = "Search by Content and Patient Name")
    public String keyword;

    @Parameter(description = "Values:  Hospital(0), Outpatient(1), Virtual(2), UrgentCare(3)")
    public Set<Integer> visitType;

    @Parameter(description = "Values: From 0 to 5")
    public Integer rating;

    public Optional<String> getKeyword() {
        return Optional.ofNullable(keyword);
    }

    public Optional<Set<Integer>> getVisitType() {
        return Optional.ofNullable(visitType);
    }

    public Optional<Integer> getRating() {
        return Optional.ofNullable(rating);
    }
}
