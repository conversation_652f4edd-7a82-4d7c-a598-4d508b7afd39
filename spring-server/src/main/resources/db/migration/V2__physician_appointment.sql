create table physician (
                        cognito_id uuid not null,
                        accept_new_patient boolean default true,

                        address varchar (256),
                        address_line varchar (256),
                        city varchar (256),
                        administrative_area varchar (256),
                        zip_code varchar (256),

                        primary key (cognito_id),
                        constraint fk_physician foreign key (cognito_id) references profile
);

create table appointment (
    patient_id uuid not null,
    physician_id uuid not null,
    start_time timestamp not null,
    end_time timestamp not null,

    is_virtual boolean default false,
    appointment_type varchar not null,
    description varchar(256),
    status varchar(100),

    primary key (patient_id, physician_id, start_time, end_time),
    constraint fk_physician_appointment foreign key (physician_id) references physician,
    constraint fk_patient_appointment foreign key (patient_id) references profile
);
