package com.covet.profile.persistence.repository;

import com.covet.profile.persistence.compositeKey.PrescriptionID;
import com.covet.profile.persistence.compositeKey.PrescriptionPharmacyID;
import com.covet.profile.persistence.model.PrescriptionPharmacy;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface PrescriptionPharmacyRepository
                extends JpaRepository<PrescriptionPharmacy, PrescriptionPharmacyID>,
                JpaSpecificationExecutor<PrescriptionPharmacy> {

    @Query(value = "select sp.*" +
            " from prescription_pharmacy sp" +
            " join prescription p on p.prescription_name = sp.prescription_name and p.prescription_code = sp.prescription_code and p.patient_id = sp.patient_id " +
            " where sp.prescription_name in (:prescriptionNames)" +
            " and p.patient_id = :patientID", nativeQuery = true)
    List<PrescriptionPharmacy> findPrescriptionPharmacies(@Param("prescriptionNames") List<String> prescriptionName, @Param("patientID") UUID patientID);

    Optional<PrescriptionPharmacy> findPrescriptionPharmacyById(PrescriptionPharmacyID id);

    void deletePrescriptionPharmacyById(PrescriptionPharmacyID id);

    @Query(value = "select pp from PrescriptionPharmacy pp " +
            "where pp.id.prescriptionName = :#{#prescriptionId.prescriptionName} and pp.id.prescriptionCode = :#{#prescriptionId.prescriptionCode} and pp.id.patientID = :#{#prescriptionId.patientID}")
    Optional<PrescriptionPharmacy> findPrescriptionPharmacyByPrescriptionId(@Param("prescriptionId") PrescriptionID prescriptionId);
}
