package com.covet.profile.dto.covet.provider;

import com.covet.profile.persistence.model.Review;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.time.LocalTime;
import java.util.List;
import java.util.Optional;

@JsonSerialize(as = ImmutableProviderDetailDto.class)
@JsonDeserialize(as = ImmutableProviderDetailDto.class, builder = ImmutableProviderDetailDto.Builder.class)
@Value.Immutable
public interface ProviderDetailDto {
    @Value.Parameter
    @JsonProperty
    PhysicianDetailDto getPhysician();

    @Value.Parameter
    @JsonProperty
    List<LocalTime> getPhysicianAvailableTimes();

    @Value.Parameter
    @JsonProperty
    Optional<Review> getTopReview();

    @Value.Parameter
    @JsonProperty
    long getTotalReviews();

}
