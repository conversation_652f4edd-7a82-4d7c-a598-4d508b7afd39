package com.covet.profile.clients.vba.VBADtos.Plans;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

@JsonSerialize(as = ImmutablePlanBenefitDto.class)
@JsonDeserialize(as = ImmutablePlanBenefitDto.class, builder = ImmutablePlanBenefitDto.Builder.class)
@Value.Immutable
public interface PlanBenefitDto {
    @Value.Parameter
    @JsonProperty
    String getPlanID();

    @Value.Parameter
    @JsonProperty
    String getBenefitCode();

    @Value.Parameter
    @JsonProperty
    VBAPlaceCoPayItemDto getBenefitDetails();

    @Value.Parameter
    @JsonProperty
    String getBenefitCodeTitle();

    @Value.Parameter
    @JsonProperty
    String getBenefitNote();
}
