package com.covet.profile.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

@JsonSerialize(as = ImmutableConfirmSignUpDto.class)
@JsonDeserialize(as = ImmutableConfirmSignUpDto.class, builder = ImmutableConfirmSignUpDto.Builder.class)
@Value.Immutable
public interface ConfirmSignUpDto {
    @Value.Parameter
    @JsonProperty
    String getUsername();

    @Value.Parameter
    @JsonProperty
    String getNewPassword();

    @Value.Parameter
    @JsonProperty
    String getConfirmPassword();

}
