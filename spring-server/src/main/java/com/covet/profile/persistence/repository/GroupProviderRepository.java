package com.covet.profile.persistence.repository;

import com.covet.profile.persistence.model.GroupProvider;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;
import java.util.UUID;


public interface GroupProviderRepository extends JpaRepository<GroupProvider, UUID>, JpaSpecificationExecutor<GroupProvider> {
    Optional<GroupProvider> findByGroupId(UUID groupId);

    Page<GroupProvider> findAll(Specification<GroupProvider> spec, Pageable pageable);


    @Query("""
            select gp.groupId, gp.npi
            from GroupProvider gp
            where gp.npi is not null
            """)
    List<List<String>> selectIdAndNpi(Pageable pageable);
}
