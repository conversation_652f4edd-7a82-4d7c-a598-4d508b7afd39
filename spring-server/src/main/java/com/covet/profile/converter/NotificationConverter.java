package com.covet.profile.converter;

import java.time.LocalTime;
import java.util.UUID;

import com.covet.profile.dto.PrescriptionSchedulerDto.NotificationDto;
import com.covet.profile.persistence.model.Notification;
import com.covet.profile.persistence.model.NotificationTemplate;
import com.covet.profile.persistence.model.Profile;
import com.covet.profile.persistence.model.Scheduler;
import org.springframework.stereotype.Component;

@Component
public class NotificationConverter {

    public NotificationDto toNotificationDto(Notification notification) {
        UUID notificationId = notification.getNotificationId();
        UUID cognitoId = notification.getCognitoId();
        UUID templateId = notification.getTemplateId();
        UUID schedulerId = notification.getSchedulerId();
        String startTime = notification.getStartTime().toString();
        Boolean isPushed = notification.getIsPushed();
        Boolean isRead = notification.getIsRead();
        Boolean isActive = notification.getIsActive();
        return new NotificationDto(notificationId,
                cognitoId,
                templateId,
                schedulerId,
                startTime,
                isPushed,
                isRead,
                isActive);
    }

    public Notification toNotification(NotificationDto notificationDto, Scheduler scheduler, Profile profile,
            NotificationTemplate template) {
        UUID notificationId = notificationDto.getNotificationId();
        UUID cognitoId = notificationDto.getCognitoId();
        UUID templateId = notificationDto.getTemplateId();
        UUID schedulerId = notificationDto.getSchedulerId();
        String startTime = notificationDto.getStartTime();
        LocalTime realTime = LocalTime.parse(startTime);
        Boolean isPushed = notificationDto.getIsPushed();
        Boolean isRead = notificationDto.getIsRead();
        Boolean isActive = notificationDto.getIsActive();
        return new Notification(notificationId,
                cognitoId,
                templateId,
                schedulerId,
                realTime,
                isPushed,
                isRead,
                isActive,
                scheduler,
                profile,
                template);
    }
}
