package com.covet.profile.clients.vba.VBAModels.Financial;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class VBAPlan {

    @JsonProperty("plan_ID")
    String planID;

    @JsonProperty("annual_Max")
    String annualMax;

    @JsonProperty("annual_Max_Net")
    String annualMaxNet;

    @JsonProperty("annual_Max_OON")
    String annualMaxOON;

    @JsonProperty("auto_Create_Flex")
    String autoCreateFlex;

    @JsonProperty("cobrA_Plan_Type_Desc")
    String cobrAPlanTypeDesc;

    @JsonProperty("coB_Savings_Max")
    String coBSavingsMax;

    @JsonProperty("coB_Type")
    String coBType;

    @JsonProperty("capitated")
    String capitated;

    @JsonProperty("cash_Value_Default")
    String cashValueDefault;

    @JsonProperty("coIns_In_To_Out")
    String coInsInToOut;

    @JsonProperty("coIns_Max_Mem")
    String coInsMaxMem;

    @JsonProperty("coIns_Max_Net_Mem")
    String coInsMaxNetMem;

    @JsonProperty("coIns_Max_Net_Sub")
    String coInsMaxNetSub;

    @JsonProperty("coIns_Max_OON_Mem")
    String coInsMaxOONMem;

    @JsonProperty("coIns_Max_OON_Sub")
    String coInsMaxOONSub;

    @JsonProperty("coIns_Max_Sub")
    String coInsMaxSub;

    @JsonProperty("coIns_Out_To_In")
    String coInsOutToIn;

    @JsonProperty("context4_ClmUcrPct")
    String context4ClmUcrPct;

    @JsonProperty("context4_ClmUcrPct_OON")
    String context4ClmUcrPctOON;

    @JsonProperty("context4_FeeSelector")
    String context4FeeSelector;

    @JsonProperty("context4_FeeSelector_OON")
    String context4FeeSelectorOON;

    @JsonProperty("context4_MedicarePct")
    String context4MedicarePct;

    @JsonProperty("context4_MedicarePct_OON")
    String context4MedicarePctOON;

    @JsonProperty("context4_Password")
    String context4Password;

    @JsonProperty("context4_PayorType")
    String context4PayorType;

    @JsonProperty("context4_PayorType_OON")
    String context4PayorTypeOON;

    @JsonProperty("context4_PctOfChg")
    String context4PctOfChg;

    @JsonProperty("context4_PctOfChg_OON")
    String context4PctOfChgOON;

    @JsonProperty("context4_PlanPriceNotGreaterBilledFg")
    String context4PlanPriceNotGreaterBilledFg;

    @JsonProperty("context4_UserID")
    String context4UserID;

    @JsonProperty("context4_WebURI")
    String context4WebURI;

    @JsonProperty("context4_WebURI_Delete")
    String context4WebURIDelete;

    @JsonProperty("currency_ID")
    String currencyID;

    @JsonProperty("ded_In_To_Out")
    String dedInToOut;

    @JsonProperty("ded_Out_To_In")
    String dedOutToIn;

    @JsonProperty("description")
    String description;

    @JsonProperty("entry_Date")
    String entryDate;

    @JsonProperty("entry_User")
    String entryUser;

    @JsonProperty("extended_Term_Default")
    String extendedTermDefault;

    @JsonProperty("family_Level_Co_Ins")
    String familyLevelCoIns;

    @JsonProperty("family_Level_Co_Ins_Member")
    String familyLevelCoInsMember;

    @JsonProperty("family_Level_Ded")
    String familyLevelDed;

    @JsonProperty("family_Level_Ded_Member")
    String familyLevelDedMember;

    @JsonProperty("family_Level_OOP")
    String familyLevelOOP;

    @JsonProperty("family_Level_OOP_Member")
    String familyLevelOOPMember;

    @JsonProperty("flex_Advance_Fund")
    String flexAdvanceFund;

    @JsonProperty("flex_Type")
    String flexType;

    @JsonProperty("forfeiture_Status_Default")
    String forfeitureStatusDefault;

    @JsonProperty("illness_Ex_Code")
    String illnessExCode;

    @JsonProperty("illness_Waiting_Period")
    String illnessWaitingPeriod;

    @JsonProperty("include_Waiting_Period")
    String includeWaitingPeriod;

    @JsonProperty("injury_Ex_Code")
    String injuryExCode;

    @JsonProperty("injury_Waiting_Period")
    String injuryWaitingPeriod;

    @JsonProperty("ltC_Apply_Elimination_Period")
    String ltCApplyEliminationPeriod;

    @JsonProperty("ltC_Apply_Waiver_Of_Premium")
    String ltCApplyWaiverOfPremium;

    @JsonProperty("ltC_Daily_Benefit")
    String ltCDailyBenefit;

    @JsonProperty("ltC_Daily_Benefit_Range_From")
    String ltCDailyBenefitRangeFrom;

    @JsonProperty("ltC_Daily_Benefit_Range_Increment")
    String ltCDailyBenefitRangeIncrement;

    @JsonProperty("ltC_Daily_Benefit_Range_Thru")
    String ltCDailyBenefitRangeThru;

    @JsonProperty("ltC_Daily_Benefit_Type")
    String ltCDailyBenefitType;

    @JsonProperty("ltC_Elimination_Period")
    String ltCEliminationPeriod;

    @JsonProperty("ltC_Elimination_Period_Per_Claim")
    String ltCEliminationPeriodPerClaim;

    @JsonProperty("ltC_Elimination_Period_Type")
    String ltCEliminationPeriodType;

    @JsonProperty("ltC_Lifetime_Max_Days_From")
    String ltCLifetimeMaxDaysFrom;

    @JsonProperty("ltC_Lifetime_Max_Days_Thru")
    String ltCLifetimeMaxDaysThru;

    @JsonProperty("ltC_Lifetime_Max_From")
    String ltCLifetimeMaxFrom;

    @JsonProperty("ltC_Lifetime_Max_Increment")
    String ltCLifetimeMaxIncrement;

    @JsonProperty("ltC_Lifetime_Max_Joint_Policy")
    String ltCLifetimeMaxJointPolicy;

    @JsonProperty("ltC_Lifetime_Max_Thru")
    String ltCLifetimeMaxThru;

    @JsonProperty("ltC_Lifetime_Max_Type")
    String ltCLifetimeMaxType;

    @JsonProperty("ltC_Lifetime_Max_Years_From")
    String ltCLifetimeMaxYearsFrom;

    @JsonProperty("ltC_Lifetime_Max_Years_Thru")
    String ltCLifetimeMaxYearsThru;

    @JsonProperty("ltC_Monthly_Benefit")
    String ltCMonthlyBenefit;

    @JsonProperty("ltC_Monthly_Benefit_Range_From")
    String ltCMonthlyBenefitRangeFrom;

    @JsonProperty("ltC_Monthly_Benefit_Range_Increment")
    String ltCMonthlyBenefitRangeIncrement;

    @JsonProperty("ltC_Monthly_Benefit_Range_Thru")
    String ltCMonthlyBenefitRangeThru;

    @JsonProperty("ltC_Monthly_Benefit_Type")
    String ltCMonthlyBenefitType;

    @JsonProperty("ltC_Waiver_Of_Premium_Type")
    String ltCWaiverOfPremiumType;

    @JsonProperty("lifetime_Max")
    String lifetimeMax;

    @JsonProperty("lifetime_Max_Net")
    String lifetimeMaxNet;

    @JsonProperty("lifetime_Max_OON")
    String lifetimeMaxOON;

    @JsonProperty("maturity_Age")
    String maturityAge;

    @JsonProperty("maturity_Age_Type")
    String maturityAgeType;

    @JsonProperty("max_Disability_Weeks")
    String maxDisabilityWeeks;

    @JsonProperty("max_Includes_Waiting_Period")
    String maxIncludesWaitingPeriod;

    @JsonProperty("mem_Ded_Credit")
    String memDedCredit;

    @JsonProperty("mem_Ded_Credit_OON")
    String memDedCreditOON;

    @JsonProperty("mem_Ded_Credit_PPO")
    String memDedCreditPPO;

    @JsonProperty("member_Annual_Max")
    String memberAnnualMax;

    @JsonProperty("member_Annual_Max_Net")
    String memberAnnualMaxNet;

    @JsonProperty("member_Annual_Max_OON")
    String memberAnnualMaxOON;

    @JsonProperty("member_Ded_OON")
    BigDecimal memberDedOON;

    @JsonProperty("member_Ded_PPO")
    BigDecimal memberDedPPO;

    @JsonProperty("member_Deductible")
    String memberDeductible;

    @JsonProperty("member_Lifetime_Max")
    String memberLifetimeMax;

    @JsonProperty("member_Lifetime_Max_Net")
    String memberLifetimeMaxNet;

    @JsonProperty("member_Lifetime_Max_OON")
    String memberLifetimeMaxOON;

    @JsonProperty("ooP_Calc_Option")
    String ooPCalcOption;

    @JsonProperty("ooP_In_To_Out")
    String ooPInToOut;

    @JsonProperty("ooP_Max_Mem")
    String ooPMaxMem;

    @JsonProperty("ooP_Max_Net_Mem")
    BigDecimal ooPMaxNetMem;

    @JsonProperty("ooP_Max_Net_Sub")
    BigDecimal ooPMaxNetSub;

    @JsonProperty("ooP_Max_OON_Mem")
    BigDecimal ooPMaxOONMem;

    @JsonProperty("ooP_Max_OON_Sub")
    BigDecimal ooPMaxOONSub;

    @JsonProperty("ooP_Max_Sub")
    String ooPMaxSub;

    @JsonProperty("ooP_Out_To_In")
    String ooPOutToIn;

    @JsonProperty("payment_Interval")
    String paymentInterval;

    @JsonProperty("payor_ID")
    String payorID;

    @JsonProperty("pend_Ex_Code")
    String pendExCode;

    @JsonProperty("pend_Flag")
    String pendFlag;

    @JsonProperty("plan_Document_URL")
    String planDocumentURL;

    @JsonProperty("plan_Type")
    String planType;

    @JsonProperty("policy_Forfeiture_Grace_Period")
    String policyForfeitureGracePeriod;

    @JsonProperty("policy_Forfeiture_Grace_Period_Type")
    String policyForfeitureGracePeriodType;

    @JsonProperty("policy_Status_Default")
    String policyStatusDefault;

    @JsonProperty("preEx_Ex_Code")
    String preExExCode;

    @JsonProperty("preEx_From")
    String preExFrom;

    @JsonProperty("preEx_Range")
    String preExRange;

    @JsonProperty("preEx_Thru")
    String preExThru;

    @JsonProperty("qpA_Method")
    String qpAMethod;

    @JsonProperty("reduced_Paid_Up_Default")
    String reducedPaidUpDefault;

    @JsonProperty("remove_Member_Amount_Edit")
    String removeMemberAmountEdit;

    @JsonProperty("situs_State")
    String situsState;

    @JsonProperty("sub_Ded_Credit")
    String subDedCredit;

    @JsonProperty("sub_Ded_Credit_OON")
    String subDedCreditOON;

    @JsonProperty("sub_Ded_Credit_PPO")
    String subDedCreditPPO;

    @JsonProperty("subscriber_Ded_OON")
    BigDecimal subscriberDedOON;

    @JsonProperty("subscriber_Ded_PPO")
    BigDecimal subscriberDedPPO;

    @JsonProperty("subscriber_Deductible")
    String subscriberDeductible;

    @JsonProperty("survivor_Benefit")
    String survivorBenefit;

    @JsonProperty("update_Date")
    String updateDate;

    @JsonProperty("update_User")
    String updateUser;

    @JsonProperty("use_COB_Savings")
    String useCOBSavings;

}