package com.covet.profile.persistence.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sun.istack.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

import java.time.LocalDate;
import java.util.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "scheduler")
public class Scheduler {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID schedulerId;

    @Column(name = "start_date")
    @NotNull
    private LocalDate startDate;

    @Column(name = "end_date")
    @NotNull
    private LocalDate endDate;

    @Column(name = "day_of_weeks")
    @NotNull
    private String dayOfWeeks;

    @JsonIgnore
    @OneToMany(mappedBy = "scheduler", cascade = CascadeType.REMOVE) // fetch = FetchType.LAZY by default
    private List<Notification> notificationList = new ArrayList<>();
}
