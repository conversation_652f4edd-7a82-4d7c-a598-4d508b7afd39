<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.7.2</version>
		<relativePath/>
		<!-- lookup parent from repository -->
	</parent>
	<groupId>com.covet</groupId>
	<artifactId>profile</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>profile</name>
	<description>profile service</description>

	<properties>
		<java.version>17</java.version>
		<flyway.version>8.5.13</flyway.version>
		<lombok.version>1.18.24</lombok.version>
		<immutables.version>2.9.2</immutables.version>
		<swagger.version>1.6.13</swagger.version>
		<spotless.version>2.36.0</spotless.version>
		<aws.sdk.version>2.20.31</aws.sdk.version>
		<aws.lamda.version>1.2.2</aws.lamda.version>
		<aws.java.version>3.11.1</aws.java.version>
		<gson.version>2.10</gson.version>
		<websocket.version>5.3.10</websocket.version>
		<spring.message.version>5.3.10</spring.message.version>
		<fcm.push.notification.version>9.2.0</fcm.push.notification.version>
		<spring-cloud.version>2021.0.3</spring-cloud.version>
		<okhttp.version>4.9.0</okhttp.version>
		<google.maps.services.version>2.2.0</google.maps.services.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-websocket</artifactId>
			<version>${websocket.version}</version>
		</dependency>
		<dependency>
			<groupId>com.opencsv</groupId>
			<artifactId>opencsv</artifactId>
			<version>4.1</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-messaging</artifactId>
			<version>${spring.message.version}</version>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>s3</artifactId>
			<version>${aws.sdk.version}</version>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>secretsmanager</artifactId>
			<version>${aws.sdk.version}</version>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>cognitoidentityprovider</artifactId>
			<version>${aws.sdk.version}</version>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>acm</artifactId>
			<version>${aws.sdk.version}</version>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>acmpca</artifactId>
			<version>${aws.sdk.version}</version>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>accessanalyzer</artifactId>
			<version>${aws.sdk.version}</version>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>kafka</artifactId>
			<version>${aws.sdk.version}</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-lambda-java-core</artifactId>
			<version>${aws.lamda.version}</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-lambda-java-events</artifactId>
			<version>${aws.java.version}</version>
		</dependency>
		<dependency>
			<groupId>com.google.firebase</groupId>
			<artifactId>firebase-admin</artifactId>
			<version>${fcm.push.notification.version}</version>
		</dependency>

		<!-- Google map -->
		<dependency>
			<groupId>com.google.maps</groupId>
			<artifactId>google-maps-services</artifactId>
			<version>${google.maps.services.version}</version>
		</dependency>
		<!-- Log4j -->
		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.17</version> <!-- Use the latest version -->
		</dependency>
		<!-- Spring -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.retry</groupId>
			<artifactId>spring-retry</artifactId>
			<version>2.0.1</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aspects</artifactId>
			<version>6.0.6</version>
		</dependency>

		<!-- Swagger 3 UI -->
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-ui</artifactId>
			<version>${swagger.version}</version>
		</dependency>

		<!-- Validation -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>

		<!-- Database -->
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
		</dependency>
		<dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-core</artifactId>
			<version>${flyway.version}</version>
		</dependency>
		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>mssql-jdbc</artifactId>
			<version>12.2.0.jre8</version>
		</dependency>
		<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>33.4.0-jre</version>
			</dependency>



		<!-- Utilities -->
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>${gson.version}</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
		</dependency>
		<dependency>
			<groupId>org.immutables</groupId>
			<artifactId>value</artifactId>
			<version>${immutables.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.12.0</version>
		</dependency>
		<!-- Spring cloud -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>${okhttp.version}</version>
		</dependency>
		<!-- TEST -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-test</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.mockito/mockito-core -->
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<version>5.9.0</version>
			<scope>test</scope>
		</dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>

    </dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>2021.0.3</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>com.diffplug.spotless</groupId>
				<artifactId>spotless-maven-plugin</artifactId>
				<version>${spotless.version}</version>
				<configuration>
					<formats>
						<format>
							<includes>
								<include>*.md</include>
								<include>.gitignore</include>
							</includes>
							<trimTrailingWhitespace/>
							<endWithNewline/>
							<indent>
								<tabs>true</tabs>
								<spacesPerTab>4</spacesPerTab>
							</indent>
						</format>
					</formats>
					<java>
						<includes>
							<include>src/main/java/**/*.java</include>
							<include>src/test/java/**/*.java</include>
						</includes>
						<cleanthat />
						<googleJavaFormat />
						<eclipse />
						<prettier>
							<devDependencyProperties>
								<property>
									<name>prettier</name>
									<value>2.8.1</value>
								</property>
								<property>
									<name>prettier-plugin-java</name>
									<value>0.8.0</value>
								</property>
							</devDependencyProperties>
							<config>
								<parser>java</parser>
							</config>
						</prettier>
						<importOrder />
						<importOrder>
						</importOrder>
						<removeUnusedImports />
						<formatAnnotations />
					</java>
				</configuration>
			</plugin>
			<!-- maven enforcer -->
			<!--			<plugin>-->
			<!--				<groupId>org.apache.maven.plugins</groupId>-->
			<!--				<artifactId>maven-enforcer-plugin</artifactId>-->
			<!--				<version>3.1.0</version>-->
			<!--				<executions>-->
			<!--					<execution>-->
			<!--						<id>enforce-versions</id>-->
			<!--						<goals>-->
			<!--							<goal>enforce</goal>-->
			<!--						</goals>-->
			<!--						<configuration>-->
			<!--							<rules>-->
			<!--								<bannedPlugins>-->
			<!--									&lt;!&ndash; will only display a warning but does not fail the build. &ndash;&gt;-->
			<!--									<level>WARN</level>-->
			<!--									<excludes>-->
			<!--										<exclude>org.apache.maven.plugins:maven-verifier-plugin</exclude>-->
			<!--									</excludes>-->
			<!--									<message>Please consider using the maven-invoker-plugin (http://maven.apache.org/plugins/maven-invoker-plugin/)!</message>-->
			<!--								</bannedPlugins>-->
			<!--								<requireMavenVersion>-->
			<!--									<version>2.0.6</version>-->
			<!--								</requireMavenVersion>-->
			<!--								<requireJavaVersion>-->
			<!--									<version>1.5</version>-->
			<!--								</requireJavaVersion>-->
			<!--								<requireOS>-->
			<!--									<family>unix</family>-->
			<!--&lt;!&ndash;									<family>windows</family>&ndash;&gt;-->
			<!--								</requireOS>-->
			<!--							</rules>-->
			<!--						</configuration>-->
			<!--					</execution>-->
			<!--				</executions>-->
			<!--			</plugin>-->
		</plugins>
	</build>

</project>