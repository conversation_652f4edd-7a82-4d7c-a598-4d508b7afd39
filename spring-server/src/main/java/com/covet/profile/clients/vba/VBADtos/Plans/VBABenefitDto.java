package com.covet.profile.clients.vba.VBADtos.Plans;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

@JsonSerialize(as = ImmutableVBABenefitDto.class)
@JsonDeserialize(as = ImmutableVBABenefitDto.class, builder = ImmutableVBABenefitDto.Builder.class)
@Value.Immutable
public interface VBABenefitDto {
    @Value.Parameter
    @JsonProperty
    String getPlanID();

    @Value.Parameter
    @JsonProperty
    String getBenefitCode();
}
