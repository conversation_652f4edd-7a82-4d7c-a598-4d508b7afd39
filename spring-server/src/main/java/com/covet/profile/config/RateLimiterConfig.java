package com.covet.profile.config;

import com.google.common.util.concurrent.RateLimiter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RateLimiterConfig {

    @Bean
    public RateLimiter externalServiceRateLimiter() {
        // Configures a RateLimiter to allow 24 permits (calls) per second.
        //VBA claims to have a 25/second limit
        return RateLimiter.create(24.0);
    }
}

