package com.covet.profile.controller;

import com.covet.profile.converter.PharmacyConverter;
import com.covet.profile.dto.covet.pharmacy.PharmacyDto;
import com.covet.profile.dto.covet.prescription.PrescriptionPharmacyDto;
import com.covet.profile.persistence.model.Pharmacy;
import com.covet.profile.persistence.model.PrescriptionPharmacy;
import com.covet.profile.service.PharmacyService;
import com.covet.profile.utils.AuthUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.net.URL;
import java.util.UUID;

// @PreAuthorize("hasRole('patient') || hasRole('physician')")
@Validated
@RestController
@RequestMapping("/api")
public class PharmacyController {
    public static final Logger log = LoggerFactory.getLogger(PharmacyController.class);
    private final PharmacyService pharmacyService;

    @Autowired
    public PharmacyController(PharmacyService pharmacyService) {
        this.pharmacyService = pharmacyService;
    }

    @PostMapping("/pharmacies")
    public ResponseEntity<Pharmacy> createPharmacy(
            @Valid @RequestBody final PharmacyDto pharmacyDto) {
        Pharmacy targetPharmacy = PharmacyConverter.dtoToPharmacy(pharmacyDto);
        pharmacyService.createOrUpdatePharmacy(targetPharmacy);
        return new ResponseEntity<>(targetPharmacy, HttpStatus.OK);
    }

    @PostMapping("/pharmacies/sale")
    public ResponseEntity<PrescriptionPharmacy> salePrescription(
            @Valid @RequestBody final PrescriptionPharmacyDto prescriptionPharmacyDto) {
        prescriptionPharmacyDto.getPrescriptionPharmacy().ifPresent(prescriptionPharmacy ->
            prescriptionPharmacy.setPatientID(AuthUtils.getCognitoId())
        );
        PrescriptionPharmacy targetPrescriptionPharmacy = pharmacyService.createPrescriptionPharmacy(prescriptionPharmacyDto);
        return new ResponseEntity<>(targetPrescriptionPharmacy, HttpStatus.OK);
    }

    @PostMapping(path = "/pharmacies/images", consumes = { MediaType.MULTIPART_FORM_DATA_VALUE })
    public ResponseEntity<URL> postImageForPharmacy(
            @RequestParam UUID pharmacyId,
            @RequestParam MultipartFile file) {
        URL result = pharmacyService.postImageForPharmacy(file, pharmacyId);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
}
