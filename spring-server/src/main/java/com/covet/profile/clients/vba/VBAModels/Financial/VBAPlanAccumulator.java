package com.covet.profile.clients.vba.VBAModels.Financial;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class VBAPlanAccumulator {

    @JsonProperty("plan_ID")
    String planID;

    @JsonProperty("accum_Name")
    String accumName;

    @JsonProperty("annual_Max")
    String annualMax;

    @JsonProperty("annual_Max_Member")
    String annualMaxMember;

    @JsonProperty("annual_Max_Net")
    BigDecimal annualMaxNet;

    @JsonProperty("annual_Max_Net_Member")
    String annualMaxNetMember;

    @JsonProperty("annual_Max_OON")
    String annualMaxOON;

    @JsonProperty("annual_Max_OON_Member")
    String annualMaxOONMember;

    @JsonProperty("coIns_Max_Mem")
    String coInsMaxMem;

    @JsonProperty("coIns_Max_Net_Mem")
    String coInsMaxNetMem;

    @JsonProperty("coIns_Max_Net_Sub")
    String coInsMaxNetSub;

    @JsonProperty("coIns_Max_OON_Mem")
    String coInsMaxOONMem;

    @JsonProperty("coIns_Max_OON_Sub")
    String coInsMaxOONSub;

    @JsonProperty("coIns_Max_Sub")
    String coInsMaxSub;

    @JsonProperty("lifetime_Max")
    String lifetimeMax;

    @JsonProperty("lifetime_Max_Member")
    String lifetimeMaxMember;

    @JsonProperty("lifetime_Max_Net")
    String lifetimeMaxNet;

    @JsonProperty("lifetime_Max_Net_Member")
    String lifetimeMaxNetMember;

    @JsonProperty("lifetime_Max_OON")
    String lifetimeMaxOON;

    @JsonProperty("lifetime_Max_OON_Member")
    String lifetimeMaxOONMember;

    @JsonProperty("member_Ded_OON")
    BigDecimal memberDedOON;

    @JsonProperty("member_Ded_PPO")
    BigDecimal memberDedPPO;

    @JsonProperty("member_Deductible")
    String memberDeductible;

    @JsonProperty("ooP_Max_Mem")
    String ooPMaxMem;

    @JsonProperty("ooP_Max_Net_Mem")
    BigDecimal ooPMaxNetMem;

    @JsonProperty("ooP_Max_Net_Sub")
    String ooPMaxNetSub;

    @JsonProperty("ooP_Max_OON_Mem")
    BigDecimal ooPMaxOONMem;

    @JsonProperty("ooP_Max_OON_Sub")
    String ooPMaxOONSub;

    @JsonProperty("ooP_Max_Sub")
    String ooPMaxSub;

    @JsonProperty("subscriber_Ded_OON")
    BigDecimal subscriberDedOON;

    @JsonProperty("subscriber_Ded_PPO")
    BigDecimal subscriberDedPPO;

    @JsonProperty("subscriber_Deductible")
    String subscriberDeductible;

}
