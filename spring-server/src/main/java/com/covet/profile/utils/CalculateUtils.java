package com.covet.profile.utils;

import com.covet.profile.ProfileApplication;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;

import java.util.TimeZone;

@Slf4j
@UtilityClass
public class CalculateUtils {
    public static Double getDistance(Double x1, Double y1, Double x2, Double y2) {
        return Math.round(
                Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2)
                ) * 100.0) / 100.0;
    }

    public Double calculateDistance(Double lat, Double lng, double latitude, double longitude) {
        final int R = 6371; // Radius of the earth
    
        double latDistance = Math.toRadians(lat - latitude);
        double lonDistance = Math.toRadians(lng - longitude);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(latitude)) * Math.cos(Math.toRadians(lat))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        double distance = R * c * 0.621371; // convert to miles

        distance = Math.pow(distance, 2);
    
        return Math.round(Math.sqrt(distance)) * 100.0 / 100.0;
    }
}
