import jwt from 'jsonwebtoken';
import axios from 'axios';
import { createHealthieWsConnection } from '../webSocket/healthieWsConnection.js';
import pubsub from '../webSocket/pubsub.js';



const resolvers = {
  Query: {
    zendeskAuth: async (_, { userId }) => {
      const KEY_ID = process.env.KEY_ID;
      const SECRET = process.env.SECRET;

      const token = jwt.sign(
        { scope: 'user', external_id: userId   },
        SECRET,
        { algorithm: 'HS256', header: { kid: KEY_ID } }
      );

      return { token };
    },
    fetchWidgetSessionURL: async (_, __, context) => {

      // Your original headers and request body
      const headers = {
        'dev-id': process.env.TERA_DEV_ID,
        'X-API-Key': process.env.TERA_KEY,
        'Content-Type': 'application/json'
      };
      
      const requestBody = {
        "providers": "GARMIN,WITHINGS,FITBIT,GOOGLE,OURA,WAHOO,PELOTON,ZWIFT,TRAININGPEAKS,FREESTYLELIBRE,DEXCOM,COROS,HUAWEI,OMRON,RENPHO,POLAR,SUUNTO,EIGHT,APPLE,CONCEPT2,WHOOP,IFIT,TEMPO,CRONOMETER,FATSECRET,NUTRACHECK,UNDERARMOUR", 
        "language": "en",
        "show_disconnect": true,
        "reference_id": context.user.sub  // <-- Use the decoded user ID from JWT
      };

      try {
        const response = await axios.post(
          'https://api.tryterra.co/v2/auth/generateWidgetSession',
          requestBody,
          { headers: headers }
        );

        return response.data.url;
      } catch (error) {
        throw new Error('Failed to fetch URL');
      }
  },
  },
  Subscription: {
    openConversation: {
      subscribe: (_, { userId, conversationId}) => {
        // Initialize the WebSocket connection
        createHealthieWsConnection(userId, conversationId);

        // Return the AsyncIterator for this subscription
        return pubsub.asyncIterator(`OPEN_CONVERSATION_${conversationId}`);
      },
    },  
  },
};


export default resolvers;
