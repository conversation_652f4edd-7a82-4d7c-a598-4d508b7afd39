image: maven:3.8.5-openjdk-17-slim


definitions:
  services:
    postgres:
      image: postgres
      environment:
        POSTGRES_HOST_AUTH_METHOD: trust
      variables:
        POSTGRES_DB: 'covet'
        POSTGRES_USER: 'admin'
        POSTGRES_PASSWORD: 'password'
    docker:
      memory: 2048


  steps:
    - step: &increment-version
          name: Increment Version
          script:
            - apt-get update
            - apt-get install -y git
            - chmod +x scripts/increment-version.sh
            - ./scripts/increment-version.sh

    - step: &build-and-test
        name: Build and test
        caches:
          - maven
        services:
          - postgres
        script:
          - cd spring-server
          - mvn -version 
          - mvn --batch-mode verify
        after-script:
          # Collect checkstyle results, if any, and convert to Bitbucket Code Insights.
          - pipe: atlassian/checkstyle-report:0.3.0
        artifacts:
          - 'spring-server/target/**'
    - step: &security-scan
        name: Security Scan
        script:
          # Run a security scan for sensitive data.
          # See more security tools at https://bitbucket.org/product/features/pipelines/integrations?&category=security
          - pipe: atlassian/git-secrets-scan:0.5.1


#TODO: add graphql tests

pipelines:
  default:
    - parallel:
        - step: *build-and-test
        - step: *security-scan
  branches:
    main:
      - parallel:
          - step: *build-and-test
          - step: *security-scan
      - parallel:
        - step:
            name: "Build and Push Spring Boot Image to ECR"
            services:
              - docker
            image: atlassian/pipelines-awscli:latest
            script:
              - export IMAGE_TAG="123903216279.dkr.ecr.us-east-1.amazonaws.com/spring-server:main-$BITBUCKET_BUILD_NUMBER"
              - aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 123903216279.dkr.ecr.us-east-1.amazonaws.com
              - cd spring-server
              - docker build -t $IMAGE_TAG .
              - docker push $IMAGE_TAG
            condition:
              changesets:
                includePaths:
                  - 'spring-server/**'
        - step:
            name: "Build and Push GraphQL Image to ECR"
            services:
              - docker
            image: atlassian/pipelines-awscli:latest
            script:
              - export IMAGE_TAG="123903216279.dkr.ecr.us-east-1.amazonaws.com/covetql:main-$BITBUCKET_BUILD_NUMBER"
              - aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 123903216279.dkr.ecr.us-east-1.amazonaws.com
              - cd covetql
              - docker build -t $IMAGE_TAG .
              - docker push $IMAGE_TAG
            condition:
              changesets:
                includePaths:
                  - 'covetql/**'

      - parallel:
        - step:
            name: Deploy Spring Boot server to prod cluster
            script:
            - apt-get update 
            - apt-get install -y gettext
            - export IMAGE_TAG="123903216279.dkr.ecr.us-east-1.amazonaws.com/spring-server:main-$BITBUCKET_BUILD_NUMBER"
            - envsubst < cluster/deployments/spring-deployment.tpl.yaml > cluster/deployments/spring-deployment.yaml
            - pipe: atlassian/aws-eks-kubectl-run:2.3.0
              variables:
                AWS_DEFAULT_REGION: "us-east-1"
                CLUSTER_NAME: "MemberAppBackend"
                KUBECTL_COMMAND: "apply"
                RESOURCE_PATH: "cluster/deployments/spring-deployment.yaml"
                AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
                AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
            condition:
              changesets:
                includePaths:
                  - 'spring-server/**'
        
        - step:
            name: Deploy GraphQL server to prod cluster
            script:
            - apt-get update 
            - apt-get install -y gettext
            - export IMAGE_TAG="123903216279.dkr.ecr.us-east-1.amazonaws.com/covetql:main-$BITBUCKET_BUILD_NUMBER"
            - envsubst < cluster/deployments/covetql-deployment.tpl.yaml > cluster/deployments/covetql-deployment.yaml
            - pipe: atlassian/aws-eks-kubectl-run:2.3.0
              variables:
                AWS_DEFAULT_REGION: "us-east-1"
                CLUSTER_NAME: "MemberAppBackend"
                KUBECTL_COMMAND: "apply"
                RESOURCE_PATH: "cluster/deployments/covetql-deployment.yaml"
                AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
                AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}

            condition:
              changesets:
                includePaths:
                  - 'covetql/**'

        - step:
            name: Send Slack Notification
            script:
              - >
                curl -X POST -H 'Content-type: application/json' --data '{
                  "text": "*New Backend Version Deployed!* 🚀\n*App:* Covet Health Backend :matrix:\n*Status:* Success ✅",
                  "username": "Bitbucket CI",
                  "icon_emoji": ":rocket:"
                }' ${SLACK_WEBHOOK_URL}
    dev:
      - step: *increment-version
      - parallel:
          - step: *build-and-test
          - step: *security-scan
      - parallel:
        - step:
            name: "Build and Push Spring Boot Image to ECR"
            services:
              - docker
            image: atlassian/pipelines-awscli:latest
            script:
              - export VERSION=$(cat version.txt)
              - export IMAGE_TAG="123903216279.dkr.ecr.us-east-1.amazonaws.com/spring-server:dev-$BITBUCKET_BUILD_NUMBER-$VERSION"
              - aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 123903216279.dkr.ecr.us-east-1.amazonaws.com
              - cd spring-server
              - docker build -t $IMAGE_TAG .
              - docker push $IMAGE_TAG
            condition:
              changesets:
                includePaths:
                  - 'spring-server/**'
        - step:
            name: "Build and Push GraphQL Image to ECR"
            services:
              - docker
            image: atlassian/pipelines-awscli:latest
            script:
              - export IMAGE_TAG="123903216279.dkr.ecr.us-east-1.amazonaws.com/covetql:dev-$BITBUCKET_BUILD_NUMBER"
              - aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 123903216279.dkr.ecr.us-east-1.amazonaws.com
              - cd covetql
              - docker build -t $IMAGE_TAG .
              - docker push $IMAGE_TAG
            condition:
              changesets:
                includePaths:
                  - 'covetql/**'

        
      - parallel:
        - step:
            name: Deploy Spring Boot server to dev cluster
            services:
              - docker
            script:
            - apt-get update 
            - apt-get install -y gettext
            - export VERSION=$(cat version.txt)
            - export IMAGE_TAG="123903216279.dkr.ecr.us-east-1.amazonaws.com/spring-server:dev-$BITBUCKET_BUILD_NUMBER-$VERSION"
            - envsubst < cluster/deployments/spring-deployment.tpl.yaml > cluster/deployments/spring-deployment.yaml
            - pipe: atlassian/aws-eks-kubectl-run:2.3.0
              variables:
                AWS_DEFAULT_REGION: "us-east-1"
                CLUSTER_NAME: "DevBackend"
                KUBECTL_COMMAND: "apply"
                RESOURCE_PATH: "cluster/deployments/spring-deployment.yaml"
                AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
                AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
            condition:
              changesets:
                includePaths:
                  - 'spring-server/**'
        - step:
            name: Deploy GraphQL server to dev cluster
            services:
              - docker
            script:
            - apt-get update 
            - apt-get install -y gettext
            - export IMAGE_TAG="123903216279.dkr.ecr.us-east-1.amazonaws.com/covetql:dev-$BITBUCKET_BUILD_NUMBER"
            - envsubst < cluster/deployments/covetql-deployment.tpl.yaml > cluster/deployments/covetql-deployment.yaml
            - pipe: atlassian/aws-eks-kubectl-run:2.3.0
              variables:
                AWS_DEFAULT_REGION: "us-east-1"
                CLUSTER_NAME: "DevBackend"
                KUBECTL_COMMAND: "apply"
                RESOURCE_PATH: "cluster/deployments/covetql-deployment.yaml"
                AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
                AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}

            condition:
              changesets:
                includePaths:
                  - 'covetql/**'