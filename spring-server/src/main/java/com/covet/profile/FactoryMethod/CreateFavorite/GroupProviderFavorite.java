package com.covet.profile.FactoryMethod.CreateFavorite;

import com.covet.profile.converter.FavoriteConverter;
import com.covet.profile.dto.CreateFavoriteRequest;
import com.covet.profile.persistence.repository.GroupProviderRepository;
import com.covet.profile.persistence.repository.PatientFavoriteRepository;
import com.covet.profile.systemEnum.EFavoriteType;
import com.covet.profile.utils.AuthUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

@Service
@RequiredArgsConstructor
public class GroupProviderFavorite implements IPatientMethod<CreateFavoriteRequest> {
    private final GroupProviderRepository groupProviderRepository;
    private final PatientFavoriteRepository patientFavoriteRepository;

    @Override
    public void createOrUpdate(CreateFavoriteRequest request) {
        if (!groupProviderRepository.existsById(request.favoriteId())) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND,
                    String.format("Not found favorite ID: %s", request.favoriteId()));
        }
        var entity = patientFavoriteRepository.findPatientFavorite(AuthUtils.getCognitoId(), request.favoriteId());
        if (entity.isPresent()) {
            boolean isActive = entity.get().getIsActive();
            entity.get().setIsActive(!isActive);
            patientFavoriteRepository.saveAndFlush(entity.get());
        } else {
            var newEntity = FavoriteConverter.mapToEntity(request);
            newEntity.setPatientId(AuthUtils.getCognitoId());
            patientFavoriteRepository.saveAndFlush(newEntity);
        }
    }

    @Override
    public EFavoriteType getFavoriteType() {
        return EFavoriteType.GROUP_PROVIDER;
    }
}
