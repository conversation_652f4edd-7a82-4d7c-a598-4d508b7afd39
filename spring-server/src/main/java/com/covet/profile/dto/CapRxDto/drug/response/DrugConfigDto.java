package com.covet.profile.dto.CapRxDto.drug.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.net.URL;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DrugConfigDto {

    @JsonProperty
    String dosage;

    @JsonProperty("dosage_form")
    String dosageForm;

    @JsonProperty("drug_descriptor_id")
    String drugDescriptorId;

    @JsonProperty("end_date")
    Date endDate;

    @JsonProperty("start_date")
    Date startDate;

    @JsonProperty
    String ndc;

    @JsonProperty
    List<Double> quantities;

    @JsonProperty("img_url")
    URL imgUrl;

}
