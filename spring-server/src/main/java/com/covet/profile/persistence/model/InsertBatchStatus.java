package com.covet.profile.persistence.model;

import com.sun.istack.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "insert_batch_status")
public class InsertBatchStatus {
    @Id
    @GeneratedValue
    @Column(name = "id")
    private UUID id;

    @NotNull
    @Column(name = "batch_number")
    private Integer batchNumber;

    @Column(name = "is_inserted")
    private Boolean isInserted;

    @Column(name = "source")
    private String source;
}
