package com.covet.profile.service;

import com.covet.profile.converter.ProfileConverter;
import com.covet.profile.converter.notification.PrescriptionSchedulerConverter;
import com.covet.profile.converter.notification.response.GetListMedicineNotificationResponse;
import com.covet.profile.dto.PrescriptionSchedulerDto.UpdateNotificationRequest;
import com.covet.profile.dto.covet.profile.ProfileDto;
import com.covet.profile.dto.covet.profile.ImmutableProfileDto;
import com.covet.profile.dto.common.PageRequestParams;
import com.covet.profile.dto.location.ICoordinate;
import com.covet.profile.persistence.model.Notification;
import com.covet.profile.persistence.model.Picture;
import com.covet.profile.persistence.model.Profile;
import com.covet.profile.persistence.model.RegistrationFCM;
import com.covet.profile.persistence.repository.NotificationRepository;
import com.covet.profile.persistence.repository.PictureRepository;
import com.covet.profile.persistence.repository.ProfileRepository;
import com.covet.profile.persistence.repository.RegistrationFcmRepository;
import com.covet.profile.systemEnum.ETemplateType;
import com.covet.profile.utils.AuthUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.access.AuthorizationServiceException;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.test.util.ReflectionTestUtils;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import java.net.URL;
import java.time.LocalTime;
import java.util.*;
import java.util.Date;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProfileServiceTest {

    @Mock
    private ProfileRepository profileRepository;

    @Mock
    private PictureRepository pictureRepository;

    @Mock
    private S3Service s3Service;

    @Mock
    private RegistrationFcmRepository registrationFcmRepository;

    @Mock
    private NotificationRepository notificationRepository;

    @Mock
    private PrescriptionSchedulerConverter prescriptionSchedulerConverter;

    @Mock
    private EntityManager em;

    @InjectMocks
    private ProfileService profileService;

    // Test Data
    private static final UUID TEST_PROFILE_ID = UUID.randomUUID();
    private Profile testProfile;
    private ProfileDto testProfileDto;
    private Picture testPicture;
    private MultipartFile testFile;
    private URL testUrl;

    @BeforeEach
    void setUp() throws Exception {
        // Initialize test profile
        testProfile = new Profile();
        testProfile.setCognitoId(TEST_PROFILE_ID);
        testProfile.setFirstName("John");
        testProfile.setMiddleName("M");
        testProfile.setLastName("Doe");
        testProfile.setSocialSecurityNumber("123456789");
        testProfile.setAddress("123 Test St, Test City, CA 90210");
        testProfile.setLatitude(34.0522);
        testProfile.setLongitude(-118.2437);

        testProfileDto = ProfileConverter.profileToProfileDto(testProfile, null);

        // Initialize test picture
        testPicture = new Picture();
        testPicture.setCognitoId(TEST_PROFILE_ID);
        testPicture.setImageURL(new URL("https://test-bucket.s3.amazonaws.com/test-image.jpg"));

        // Initialize test file
        testFile = new MockMultipartFile(
                "test-image.jpg",
                "test-image.jpg",
                "image/jpeg",
                "test image content".getBytes()
        );

        testUrl = new URL("https://test-bucket.s3.amazonaws.com/test-image.jpg");
        
        // Set EntityManager in ProfileService
        ReflectionTestUtils.setField(profileService, "em", em);
    }

    @Nested
    @DisplayName("Profile CRUD Operations")
    class ProfileCrudTests {
        
        @Test
        @DisplayName("Should return empty when profile not found")
        void getProfileById_WhenNotFound_ReturnsEmpty() {
            // Arrange
            when(profileRepository.findById(TEST_PROFILE_ID)).thenReturn(Optional.empty());

            // Act
            Optional<ProfileDto> result = profileService.getProfileById(TEST_PROFILE_ID);

            // Assert
            assertThat(result).isEmpty();
            verify(profileRepository).findById(TEST_PROFILE_ID);
        }

        @Test
        @DisplayName("Should return profile when found")
        void getProfileById_WhenFound_ReturnsProfile() {
            // Arrange
            when(profileRepository.findById(TEST_PROFILE_ID)).thenReturn(Optional.of(testProfile));
            when(s3Service.getExpiredDays()).thenReturn(7);

            // Act
            Optional<ProfileDto> result = profileService.getProfileById(TEST_PROFILE_ID);

            // Assert
            assertThat(result).isPresent();
            assertThat(result.get().getFirstName()).isEqualTo(testProfile.getFirstName());
            assertThat(result.get().getLastName()).isEqualTo(testProfile.getLastName());
            verify(profileRepository).findById(TEST_PROFILE_ID);
        }

        @Test
        @DisplayName("Should create new profile successfully")
        void createProfile_Success() {
            // Arrange
            when(profileRepository.save(any(Profile.class))).thenReturn(testProfile);

            // Act
            ProfileDto result = profileService.createOrUpdate(testProfileDto);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getFirstName()).isEqualTo(testProfileDto.getFirstName());
            assertThat(result.getLastName()).isEqualTo(testProfileDto.getLastName());
            verify(profileRepository).save(any(Profile.class));
        }

        @Test
        @DisplayName("Should delete profile successfully")
        void deleteProfile_Success() {
            // Arrange
            doNothing().when(profileRepository).deleteById(TEST_PROFILE_ID);

            // Act
            profileService.deleteProfile(TEST_PROFILE_ID);

            // Assert
            verify(profileRepository).deleteById(TEST_PROFILE_ID);
        }

        @Test
        @DisplayName("Should fetch current user profile successfully")
        void fetchMe_Success() {
            // Arrange
            try (MockedStatic<AuthUtils> authUtils = mockStatic(AuthUtils.class)) {
                authUtils.when(AuthUtils::getCognitoId).thenReturn(TEST_PROFILE_ID);
                when(profileRepository.getReferenceById(TEST_PROFILE_ID)).thenReturn(testProfile);

                // Act
                Profile result = profileService.fetchMe();

                // Assert
                assertThat(result).isNotNull();
                assertThat(result.getCognitoId()).isEqualTo(TEST_PROFILE_ID);
                verify(profileRepository).getReferenceById(TEST_PROFILE_ID);
            }
        }

        @Test
        @DisplayName("Should update existing profile successfully")
        void updateProfile_Success() {
            // Arrange
            ProfileDto existingProfileDto = ImmutableProfileDto.builder()
                .from(testProfileDto)
                .cognitoId(Optional.of(TEST_PROFILE_ID))
                .firstName("UpdatedJohn")
                .build();

            Profile updatedProfile = new Profile();
            updatedProfile.setCognitoId(TEST_PROFILE_ID);
            updatedProfile.setFirstName("UpdatedJohn");
            updatedProfile.setLastName(testProfile.getLastName());
            updatedProfile.setSocialSecurityNumber(testProfile.getSocialSecurityNumber());
            updatedProfile.setAddress(testProfile.getAddress());
            updatedProfile.setLatitude(testProfile.getLatitude());
            updatedProfile.setLongitude(testProfile.getLongitude());

            // Create a picture with proper dates to avoid null date issues
            Picture pictureWithDates = new Picture();
            pictureWithDates.setCognitoId(TEST_PROFILE_ID);
            pictureWithDates.setImageURL(testUrl);
            pictureWithDates.setCreatedUrlDate(new Date()); // Set current date
            pictureWithDates.setCreatedDate(new Date());
            pictureWithDates.setUpdatedDate(new Date());

            when(profileRepository.save(any(Profile.class))).thenReturn(updatedProfile);
            when(s3Service.getExpiredDays()).thenReturn(7);
            when(pictureRepository.findById(TEST_PROFILE_ID)).thenReturn(Optional.of(pictureWithDates));

            // Act
            ProfileDto result = profileService.createOrUpdate(existingProfileDto);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getFirstName()).isEqualTo("UpdatedJohn");
            verify(profileRepository).save(any(Profile.class));
        }

        @Test
        @DisplayName("Should get profiles by IDs successfully")
        void getProfilesByIds_Success() {
            // Arrange
            Profile secondProfile = new Profile();
            secondProfile.setCognitoId(UUID.randomUUID());
            secondProfile.setFirstName("Jane");
            secondProfile.setLastName("Smith");
            secondProfile.setLatitude(0.0);
            secondProfile.setLongitude(0.0);

            List<UUID> ids = Arrays.asList(TEST_PROFILE_ID, UUID.randomUUID());
            List<Profile> expectedProfiles = Arrays.asList(testProfile, secondProfile);
            when(profileRepository.findAllById(ids)).thenReturn(expectedProfiles);

            // Act
            List<Profile> result = profileService.getProfilesByIds(ids);

            // Assert
            assertThat(result).hasSize(2);
            assertThat(result).containsExactlyElementsOf(expectedProfiles);
            verify(profileRepository).findAllById(ids);
        }

    }

    @Nested
    @DisplayName("Avatar Management Tests")
    class AvatarTests {

        @Test
        @DisplayName("Should upload avatar successfully")
        void postAvatar_Success() throws Exception {
            // Arrange
            try (MockedStatic<AuthUtils> authUtils = mockStatic(AuthUtils.class)) {
                authUtils.when(AuthUtils::getCognitoId).thenReturn(TEST_PROFILE_ID);
                when(s3Service.putFile(any(MultipartFile.class), anyString())).thenReturn(null);
                when(s3Service.getPresignedURL(anyString())).thenReturn(testUrl);
                when(pictureRepository.findById(TEST_PROFILE_ID)).thenReturn(Optional.empty());
                when(pictureRepository.save(any(Picture.class))).thenReturn(testPicture);

                // Act
                URL result = profileService.postAvatar(testFile);

                // Assert
                assertThat(result).isNotNull();
                verify(s3Service).putFile(eq(testFile), anyString());
                verify(pictureRepository).save(any(Picture.class));
            }
        }

        @Test
        @DisplayName("Should upload image successfully")
        void uploadImage_Success() {
            // Arrange
            when(pictureRepository.save(testPicture)).thenReturn(testPicture);

            // Act
            profileService.uploadImage(testPicture);

            // Assert
            verify(pictureRepository).save(testPicture);
        }

        @Test
        @DisplayName("Should get image successfully")
        void getImage_Success() {
            // Arrange
            when(pictureRepository.findById(TEST_PROFILE_ID)).thenReturn(Optional.of(testPicture));

            // Act
            Optional<Picture> result = profileService.getImage(TEST_PROFILE_ID);

            // Assert
            assertThat(result).isPresent();
            assertThat(result.get()).isEqualTo(testPicture);
            verify(pictureRepository).findById(TEST_PROFILE_ID);
        }

        @Test
        @DisplayName("Should return empty when image not found")
        void getImage_NotFound() {
            // Arrange
            when(pictureRepository.findById(TEST_PROFILE_ID)).thenReturn(Optional.empty());

            // Act
            Optional<Picture> result = profileService.getImage(TEST_PROFILE_ID);

            // Assert
            assertThat(result).isEmpty();
            verify(pictureRepository).findById(TEST_PROFILE_ID);
        }

        @Test
        @DisplayName("Should handle null file gracefully")
        void postAvatar_NullFile() {
            // Act & Assert
            // The @NonNull annotation throws NullPointerException, not IllegalArgumentException
            assertThrows(NullPointerException.class, () -> {
                profileService.postAvatar(null);
            });
        }

        @Test
        @DisplayName("Should handle empty file gracefully")
        void postAvatar_EmptyFile() {
            // Arrange
            MultipartFile emptyFile = new MockMultipartFile(
                "empty.jpg",
                "empty.jpg",
                "image/jpeg",
                new byte[0]
            );

            // Act & Assert
            assertThrows(IllegalArgumentException.class, () -> {
                profileService.postAvatar(emptyFile);
            });
        }

        @Test
        @DisplayName("Should handle file with valid extension but invalid content type")
        void postAvatar_ValidExtensionInvalidContentType() throws Exception {
            // Arrange
            MultipartFile fileWithValidExtension = new MockMultipartFile(
                "test.jpg",
                "test.jpg",
                "text/plain", // Invalid content type
                "test content".getBytes()
            );

            try (MockedStatic<AuthUtils> authUtils = mockStatic(AuthUtils.class)) {
                authUtils.when(AuthUtils::getCognitoId).thenReturn(TEST_PROFILE_ID);
                when(s3Service.putFile(any(MultipartFile.class), anyString())).thenReturn(null);
                when(s3Service.getPresignedURL(anyString())).thenReturn(testUrl);
                when(pictureRepository.findById(TEST_PROFILE_ID)).thenReturn(Optional.empty());
                when(pictureRepository.save(any(Picture.class))).thenReturn(testPicture);

                // Act
                URL result = profileService.postAvatar(fileWithValidExtension);

                // Assert
                assertThat(result).isNotNull();
                verify(s3Service).putFile(eq(fileWithValidExtension), anyString());
            }
        }
    }

    @Nested
    @DisplayName("Registration Token Tests")
    class RegistrationTokenTests {

        @Test
        @DisplayName("Should set registration token successfully")
        void setRegistrationToken_Success() {
            // Arrange
            String token = "test-token";
            RegistrationFCM expectedRegistration = new RegistrationFCM(TEST_PROFILE_ID, token, testProfile);
            
            try (MockedStatic<AuthUtils> authUtils = mockStatic(AuthUtils.class)) {
                authUtils.when(AuthUtils::getCognitoId).thenReturn(TEST_PROFILE_ID);
                when(profileRepository.getReferenceById(TEST_PROFILE_ID)).thenReturn(testProfile);
                when(registrationFcmRepository.save(any(RegistrationFCM.class))).thenReturn(expectedRegistration);

                // Act
                RegistrationFCM result = profileService.setRegistrationToken(token);

                // Assert
                assertThat(result).isNotNull();
                assertThat(result.getRegistrationToken()).isEqualTo(token);
                verify(registrationFcmRepository).save(any(RegistrationFCM.class));
            }
        }
    }

    @Nested
    @DisplayName("Notification Tests")
    class NotificationTests {

        @Test
        @DisplayName("Should get daily prescription scheduler successfully")
        void getDailyPrescriptionScheduler_Success() {
            // Arrange
            UUID cognitoId = UUID.randomUUID();
            PageRequestParams pageRequest = new PageRequestParams();
            pageRequest.setPageNumber(0);
            pageRequest.setPageSize(10);
            
            Notification notification = Notification.builder()
                .notificationId(UUID.randomUUID())
                .cognitoId(cognitoId)
                .isActive(true)
                .isRead(false)
                .templateId(UUID.randomUUID())
                .schedulerId(UUID.randomUUID())
                .startTime(LocalTime.now())
                .isPushed(false)
                .build();
            
            Page<Notification> notificationPage = new PageImpl<>(
                Collections.singletonList(notification)
            );
            
            try (MockedStatic<AuthUtils> authUtils = mockStatic(AuthUtils.class)) {
                authUtils.when(AuthUtils::getCognitoId).thenReturn(cognitoId);
                
                when(notificationRepository.filterDailyMedicineNotification(
                    eq(cognitoId), 
                    eq(ETemplateType.MEDICINE), 
                    any(Pageable.class)
                )).thenReturn(notificationPage);
                
                when(notificationRepository.countTaken(cognitoId, ETemplateType.MEDICINE)).thenReturn(1L);
                when(notificationRepository.countRemain(cognitoId, ETemplateType.MEDICINE)).thenReturn(2L);
                when(notificationRepository.countOverdue(cognitoId, ETemplateType.MEDICINE)).thenReturn(0L);
                when(prescriptionSchedulerConverter.convertToMedicineTemplateRes(notification))
                    .thenReturn(null);

                // Act
                GetListMedicineNotificationResponse result = profileService.getDailyPrescriptionScheduler(pageRequest);

                // Assert
                assertThat(result).isNotNull();
                assertThat(result.getTotalTaken()).isEqualTo(1L);
                assertThat(result.getTotalRemain()).isEqualTo(2L);
                assertThat(result.getTotalOverdue()).isEqualTo(0L);
                verify(notificationRepository).filterDailyMedicineNotification(
                    eq(cognitoId), 
                    eq(ETemplateType.MEDICINE), 
                    any(Pageable.class)
                );
                verify(prescriptionSchedulerConverter).convertToMedicineTemplateRes(notification);
            }
        }

        @Test
        @DisplayName("Should switch notification mode successfully")
        void switchModeNotificationScheduler_Success() {
            // Arrange
            UUID cognitoId = UUID.randomUUID();
            UUID notificationId = UUID.randomUUID();
            UUID templateId = UUID.randomUUID();

            Notification notification = Notification.builder()
                    .notificationId(notificationId)
                    .cognitoId(cognitoId)
                    .templateId(templateId)
                    .schedulerId(UUID.randomUUID())
                    .startTime(LocalTime.now())
                    .isPushed(false)
                    .isActive(true)
                    .isRead(false)
                    .build();

            UpdateNotificationRequest request = UpdateNotificationRequest.builder()
                    .notificationId(notificationId)
                    .isActive(false)
                    .isRead(true)
                    .build();

            when(notificationRepository.getReferenceById(notificationId)).thenReturn(notification);
            when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

            // Act
            profileService.switchModeNotificationScheduler(request);

            // Assert
            assertThat(notification.getIsActive()).isFalse();
            assertThat(notification.getIsRead()).isTrue();
            verify(notificationRepository).save(notification);
            verify(notificationRepository).getReferenceById(notificationId);
        }

        @Test
        @DisplayName("Should handle partial notification update")
        void switchModeNotificationScheduler_PartialUpdate() {
            // Arrange
            UUID notificationId = UUID.randomUUID();

            Notification notification = Notification.builder()
                    .notificationId(notificationId)
                    .cognitoId(UUID.randomUUID())
                    .templateId(UUID.randomUUID())
                    .schedulerId(UUID.randomUUID())
                    .startTime(LocalTime.now())
                    .isPushed(false)
                    .isActive(true)
                    .isRead(false)
                    .build();

            // Only update isActive, leave isRead as null
            UpdateNotificationRequest request = UpdateNotificationRequest.builder()
                    .notificationId(notificationId)
                    .isActive(false)
                    .isRead(null)
                    .build();

            when(notificationRepository.getReferenceById(notificationId)).thenReturn(notification);
            when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

            // Act
            profileService.switchModeNotificationScheduler(request);

            // Assert
            assertThat(notification.getIsActive()).isFalse();
            assertThat(notification.getIsRead()).isFalse(); // Should remain unchanged
            verify(notificationRepository).save(notification);
        }

        @Test
        @DisplayName("Should handle empty notification page")
        void getDailyPrescriptionScheduler_EmptyPage() {
            // Arrange
            UUID cognitoId = UUID.randomUUID();
            PageRequestParams pageRequest = new PageRequestParams();
            pageRequest.setPageNumber(0);
            pageRequest.setPageSize(10);

            Page<Notification> emptyPage = new PageImpl<>(Collections.emptyList());

            try (MockedStatic<AuthUtils> authUtils = mockStatic(AuthUtils.class)) {
                authUtils.when(AuthUtils::getCognitoId).thenReturn(cognitoId);

                when(notificationRepository.filterDailyMedicineNotification(
                    eq(cognitoId),
                    eq(ETemplateType.MEDICINE),
                    any(Pageable.class)
                )).thenReturn(emptyPage);

                when(notificationRepository.countTaken(cognitoId, ETemplateType.MEDICINE)).thenReturn(0L);
                when(notificationRepository.countRemain(cognitoId, ETemplateType.MEDICINE)).thenReturn(0L);
                when(notificationRepository.countOverdue(cognitoId, ETemplateType.MEDICINE)).thenReturn(0L);

                // Act
                GetListMedicineNotificationResponse result = profileService.getDailyPrescriptionScheduler(pageRequest);

                // Assert
                assertThat(result).isNotNull();
                assertThat(result.getTotalTaken()).isEqualTo(0L);
                assertThat(result.getTotalRemain()).isEqualTo(0L);
                assertThat(result.getTotalOverdue()).isEqualTo(0L);
                assertThat(result.getNotificationPage().getContent()).isEmpty();
            }
        }
    }

    @Nested
    @DisplayName("Coordinate Operations")
    class CoordinateTests {

        @Test
        @DisplayName("Should update coordinates successfully")
        void updateCoordinates_Success() {
            // Arrange
            Double newLat = 37.7749;
            Double newLng = -122.4194;
            doNothing().when(profileRepository).updateCoordinate(TEST_PROFILE_ID, newLat, newLng);

            // Act
            profileService.updateCoordinates(TEST_PROFILE_ID, newLat, newLng);

            // Assert
            verify(profileRepository).updateCoordinate(TEST_PROFILE_ID, newLat, newLng);
        }

        @Test
        @DisplayName("Should calculate distances successfully")
        void calculateDistance_Success() {
            // Arrange
            Double targetLat = 37.7749;
            Double targetLng = -122.4194;
            List<Double> expectedDistances = Arrays.asList(10.5, 20.3, 30.1);

            CriteriaBuilder cb = mock(CriteriaBuilder.class);
            @SuppressWarnings("unchecked")
            CriteriaQuery<Double> cq = mock(CriteriaQuery.class);
            @SuppressWarnings("unchecked")
            Root<Profile> root = mock(Root.class);
            @SuppressWarnings("unchecked")
            Path<Object> latPath = mock(Path.class);
            @SuppressWarnings("unchecked")
            Path<Object> lngPath = mock(Path.class);
            @SuppressWarnings("unchecked")
            Expression<Double> latDiff = mock(Expression.class);
            @SuppressWarnings("unchecked")
            Expression<Double> lngDiff = mock(Expression.class);
            @SuppressWarnings("unchecked")
            Expression<Double> latSquare = mock(Expression.class);
            @SuppressWarnings("unchecked")
            Expression<Double> lngSquare = mock(Expression.class);
            @SuppressWarnings("unchecked")
            Expression<Double> sum = mock(Expression.class);
            Order order = mock(Order.class);
            @SuppressWarnings("unchecked")
            TypedQuery<Double> query = mock(TypedQuery.class);

            when(em.getCriteriaBuilder()).thenReturn(cb);
            when(cb.createQuery(Double.class)).thenReturn(cq);
            when(cq.from(Profile.class)).thenReturn(root);
            when(root.get(Profile.Fields.latitude)).thenReturn(latPath);
            when(root.get(Profile.Fields.longitude)).thenReturn(lngPath);

            // Mock latitude calculations
            when(cb.diff(any(), eq(targetLat))).thenReturn(latDiff);
            when(cb.prod(latDiff, latDiff)).thenReturn(latSquare);

            // Mock longitude calculations
            when(cb.diff(any(), eq(targetLng))).thenReturn(lngDiff);
            when(cb.prod(lngDiff, lngDiff)).thenReturn(lngSquare);

            // Mock sum and ordering
            when(cb.sum(latSquare, lngSquare)).thenReturn(sum);
            when(cb.asc(sum)).thenReturn(order);
            when(cq.select(sum)).thenReturn(cq);
            when(cq.orderBy(order)).thenReturn(cq);

            when(em.createQuery(cq)).thenReturn(query);
            when(query.getResultList()).thenReturn(expectedDistances);

            // Act
            List<Double> result = profileService.calculateDistance(targetLat, targetLng);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).isEqualTo(expectedDistances.stream().limit(10).toList());
            verify(em).createQuery(cq);
            verify(cq).select(sum);
            verify(cq).orderBy(order);
        }

        @Test
        @DisplayName("Should fetch current user coordinates successfully")
        void fetchMeCoordinate_Success() {
            // Arrange
            ICoordinate mockCoordinate = mock(ICoordinate.class);
            when(mockCoordinate.getLatitude()).thenReturn(34.0522);
            when(mockCoordinate.getLongitude()).thenReturn(-118.2437);

            try (MockedStatic<AuthUtils> authUtils = mockStatic(AuthUtils.class)) {
                authUtils.when(AuthUtils::getCognitoId).thenReturn(TEST_PROFILE_ID);
                when(profileRepository.getCoordinate(TEST_PROFILE_ID)).thenReturn(mockCoordinate);

                // Act
                ICoordinate result = profileService.fetchMeCoordinate();

                // Assert
                assertThat(result).isNotNull();
                assertThat(result.getLatitude()).isEqualTo(34.0522);
                assertThat(result.getLongitude()).isEqualTo(-118.2437);
                verify(profileRepository).getCoordinate(TEST_PROFILE_ID);
            }
        }
    }

    @Nested
    @DisplayName("Error Handling Tests")
    class ErrorHandlingTests {

        @Test
        @DisplayName("Should handle null profile data gracefully")
        void handleNullProfile_Gracefully() {
            // Arrange
            ProfileDto nullProfile = null;

            // Act & Assert
            assertThrows(IllegalArgumentException.class, () -> {
                profileService.createOrUpdate(nullProfile);
            });
        }

        @Test
        @DisplayName("Should handle invalid coordinates gracefully")
        void handleInvalidCoordinates_Gracefully() {
            // Arrange
            Double invalidLat = 91.0; // Invalid latitude value
            Double invalidLng = -181.0; // Invalid longitude value

            // Act & Assert
            assertThrows(IllegalArgumentException.class, () -> {
                profileService.updateCoordinates(TEST_PROFILE_ID, invalidLat, invalidLng);
            });
        }

        @Test
        @DisplayName("Should handle null latitude gracefully")
        void handleNullLatitude_Gracefully() {
            // Act & Assert
            assertThrows(IllegalArgumentException.class, () -> {
                profileService.updateCoordinates(TEST_PROFILE_ID, null, -118.2437);
            });
        }

        @Test
        @DisplayName("Should handle null longitude gracefully")
        void handleNullLongitude_Gracefully() {
            // Act & Assert
            assertThrows(IllegalArgumentException.class, () -> {
                profileService.updateCoordinates(TEST_PROFILE_ID, 34.0522, null);
            });
        }

        @Test
        @DisplayName("Should handle latitude boundary values")
        void handleLatitudeBoundaryValues() {
            // Valid boundary values should work
            assertDoesNotThrow(() -> {
                profileService.updateCoordinates(TEST_PROFILE_ID, 90.0, 0.0);
            });
            assertDoesNotThrow(() -> {
                profileService.updateCoordinates(TEST_PROFILE_ID, -90.0, 0.0);
            });

            // Invalid boundary values should throw
            assertThrows(IllegalArgumentException.class, () -> {
                profileService.updateCoordinates(TEST_PROFILE_ID, 90.1, 0.0);
            });
            assertThrows(IllegalArgumentException.class, () -> {
                profileService.updateCoordinates(TEST_PROFILE_ID, -90.1, 0.0);
            });
        }

        @Test
        @DisplayName("Should handle longitude boundary values")
        void handleLongitudeBoundaryValues() {
            // Valid boundary values should work
            assertDoesNotThrow(() -> {
                profileService.updateCoordinates(TEST_PROFILE_ID, 0.0, 180.0);
            });
            assertDoesNotThrow(() -> {
                profileService.updateCoordinates(TEST_PROFILE_ID, 0.0, -180.0);
            });

            // Invalid boundary values should throw
            assertThrows(IllegalArgumentException.class, () -> {
                profileService.updateCoordinates(TEST_PROFILE_ID, 0.0, 180.1);
            });
            assertThrows(IllegalArgumentException.class, () -> {
                profileService.updateCoordinates(TEST_PROFILE_ID, 0.0, -180.1);
            });
        }

        @Test
        @DisplayName("Should handle null registration token gracefully")
        void handleNullRegistrationToken_Gracefully() {
            // Act & Assert
            // The @NonNull annotation throws NullPointerException, not IllegalArgumentException
            assertThrows(NullPointerException.class, () -> {
                profileService.setRegistrationToken(null);
            });
        }

        @Test
        @DisplayName("Should handle empty registration token gracefully")
        void handleEmptyRegistrationToken_Gracefully() {
            // Act & Assert
            // The method calls AuthUtils.getCognitoId() which throws AuthorizationServiceException when no JWT is found
            assertThrows(AuthorizationServiceException.class, () -> {
                profileService.setRegistrationToken("");
            });
        }
    }

    @Nested
    @DisplayName("Bulk Operations Tests")
    class BulkOperationsTests {

        @Test
        @DisplayName("Should save multiple profiles successfully")
        void saveManyProfiles_Success() {
            // Arrange
            List<Profile> profiles = Arrays.asList(
                createTestProfile("John", "Doe"),
                createTestProfile("Jane", "Smith")
            );
            when(profileRepository.saveAll(anyList())).thenReturn(profiles);

            // Act
            List<Profile> result = profileService.saveManyProfiles(profiles);

            // Assert
            assertThat(result).hasSize(2);
            verify(profileRepository).saveAll(profiles);
        }

        @Test
        @DisplayName("Should handle empty profile list")
        void saveManyProfiles_EmptyList() {
            // Arrange
            List<Profile> emptyProfiles = Collections.emptyList();
            when(profileRepository.saveAll(emptyProfiles)).thenReturn(emptyProfiles);

            // Act
            List<Profile> result = profileService.saveManyProfiles(emptyProfiles);

            // Assert
            assertThat(result).isEmpty();
            verify(profileRepository).saveAll(emptyProfiles);
        }

        @Test
        @DisplayName("Should handle null profile list gracefully")
        void saveManyProfiles_NullList() {
            // Arrange
            when(profileRepository.saveAll(null)).thenThrow(new IllegalArgumentException("Profile list cannot be null"));

            // Act & Assert
            assertThrows(IllegalArgumentException.class, () -> {
                profileService.saveManyProfiles(null);
            });
        }

        private Profile createTestProfile(String firstName, String lastName) {
            Profile profile = new Profile();
            profile.setCognitoId(UUID.randomUUID());
            profile.setFirstName(firstName);
            profile.setLastName(lastName);
            profile.setLatitude(0.0);
            profile.setLongitude(0.0);
            return profile;
        }
    }

    @Nested
    @DisplayName("Profile Image Tests")
    class ProfileImageTests {

        @Test
        @DisplayName("Should handle missing avatar gracefully")
        void handleMissingAvatar_Gracefully() {
            // Arrange
            when(profileRepository.findById(TEST_PROFILE_ID)).thenReturn(Optional.of(testProfile));
            when(pictureRepository.findById(TEST_PROFILE_ID)).thenReturn(Optional.empty());
            when(s3Service.getExpiredDays()).thenReturn(7);

            // Act
            Optional<ProfileDto> result = profileService.getProfileById(TEST_PROFILE_ID);

            // Assert
            assertThat(result).isPresent();
            verify(pictureRepository).findById(TEST_PROFILE_ID);
            verify(profileRepository).findById(TEST_PROFILE_ID);
        }

        @Test
        @DisplayName("Should handle invalid image file gracefully")
        void handleInvalidImageFile_Gracefully() {
            // Arrange
            MultipartFile invalidFile = new MockMultipartFile(
                "test-file.txt",
                "test-file.txt",
                "text/plain",
                "invalid image content".getBytes()
            );

            try (MockedStatic<AuthUtils> authUtils = mockStatic(AuthUtils.class)) {
                authUtils.when(AuthUtils::getCognitoId).thenReturn(TEST_PROFILE_ID);

                // Act & Assert
                assertThrows(IllegalArgumentException.class, () -> {
                    profileService.postAvatar(invalidFile);
                });
            }
        }
    }
}
