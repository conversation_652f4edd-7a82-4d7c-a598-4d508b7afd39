package com.covet.profile.dto.CapRxDto.pharmacy;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class CapPharmacyDto {
    @JsonProperty("address_line_1")
    String addressLine1;

    @JsonProperty("address_line_2")
    String addressLine2;

    @JsonProperty("city")
    String city;

    @JsonProperty("distance")
    Double distance;

    @JsonProperty("hours")
    List<String> hours;

    @JsonProperty("is_24_hours")
    String is24Hours;

    @JsonProperty("languages")
    List<String> languages;

    @JsonProperty("latitude")
    Double latitude;

    @JsonProperty("longitude")
    Double longitude;

    @JsonProperty("minority_owned_pharmacy")
    String minorityOwnedPharmacy;

    @JsonProperty("name")
    String name;

    @JsonProperty("network_status")
    String networkStatus;

    @JsonProperty("npi")
    String npi;

    @JsonProperty("phone")
    String phone;

    @JsonProperty("provider_id")
    String providerId;

    @JsonProperty("state")
    String state;

    @JsonProperty("zip")
    String zip;

}
