package com.covet.profile.persistence.compositeKey;

import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

import javax.persistence.Column;
import javax.persistence.Embeddable;

import lombok.Data;
import lombok.NoArgsConstructor;

@Embeddable
@Data
@NoArgsConstructor
public class ClaimPrescriptionID implements Serializable {
    @Column(name = "prescription_name")
    private String prescriptionName;
    @Column(name = "prescription_code")
    private String prescriptionCode;
    @Column(name = "claim_code")
    private String claimCode;

    @Column(name = "patient_id")
    private UUID patientId;

    public ClaimPrescriptionID(String prescriptionName, String prescriptionCode, String claimCode, UUID patientId) {
        this.prescriptionName = prescriptionName;
        this.prescriptionCode = prescriptionCode;
        this.claimCode = claimCode;
        this.patientId = patientId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null) {
            return false;
        }
        if (getClass() != o.getClass()) {
            return false;
        }
        ClaimPrescriptionID salePrescription = (ClaimPrescriptionID) o;
        return Objects.equals(getPrescriptionName(), salePrescription.prescriptionName)
                && Objects.equals(getPrescriptionCode(), salePrescription.prescriptionCode)
                && getClaimCode().equals(salePrescription.claimCode)
                && Objects.equals(getPatientId(), salePrescription.getPatientId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getPrescriptionName(), getPrescriptionCode(), getClaimCode(), getPatientId());

    }
}
