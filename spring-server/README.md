# Step by step

# First make sure postgres is not running locally

# to reset docker locally:
docker-compose down --volumes

# to run docker:
docker-compose --profile local up --build

#to connect to docker postgres in terminal:
docker-compose exec db psql -U admin




# to build the app:
mvn clean install

# to start the app:
mvn spring-boot:run

# to run flyway manually
mvn flyway:migrate
#dev
mvn -Dflyway.configFiles=flyway-dev.conf flyway:migrate
#reset the db 
mvn flyway:clean

# Swagger 3:
http://{env}/covet/swagger-ui/index.html

#Example:
http://localhost:8080/covet/swagger-ui/index.html
