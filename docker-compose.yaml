version: '3.8'
services:

  postgres:
    image: postgres
    environment:
      POSTGRES_HOST_AUTH_METHOD: trust
      POSTGRES_DB: 'covet'
      POSTGRES_USER: 'admin'
      POSTGRES_PASSWORD: 'password'
      
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    deploy:
      replicas: 0 

  spring-server:
    build: ./spring-server
    environment:
      SPRING_DATASOURCE_URL: jdbc:postgresql://${RDS_HOSTNAME}:${RDS_PORT}/${RDS_DB_NAME}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      RDS_HOSTNAME: ${RDS_HOSTNAME}
      RDS_PORT: 5432
      RDS_USERNAME: ${RDS_USERNAME}
      RDS_PASSWORD: ${RDS_PASSWORD}
      RDS_DB_NAME: ${RDS_DB_NAME}
      AWS_REGION: ${AWS_REGION}
      ZENDESK_API_KEY: ${ZENDESK_API_KEY}
      AUTH0_CLIENT_SECRET: ${AUTH0_CLIENT_SECRET}
      AUTH0_CLIENT_ID: ${AUTH0_CLIENT_ID}
      MPX_USERNAME: ${MPX_USERNAME}
      MPX_PASSWORD: ${MPX_PASSWORD}
      METABASE_KEY: ${METABASE_KEY}
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE}
    ports:
      - '8080:5000'
    depends_on:
      - postgres

  covetql:
    build: ./covetql
    ports:
      - '4000:4000'
    depends_on:
      - postgres

volumes:
  postgres_data:
