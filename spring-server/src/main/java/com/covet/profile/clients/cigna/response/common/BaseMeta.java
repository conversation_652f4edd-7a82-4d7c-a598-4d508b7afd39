package com.covet.profile.clients.cigna.response.common;

import com.covet.profile.clients.cigna.response.Security;
import com.covet.profile.clients.cigna.response.Tag;
import lombok.Data;

import java.util.List;

@Data
public class BaseMeta {
    private String source;
    private String versionId;
    private String lastUpdated;
    private List<String> profile;
    private List<Tag> tag;
    private List<Security> security;
}
