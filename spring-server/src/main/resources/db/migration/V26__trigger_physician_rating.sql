CREATE
OR REPLACE FUNCTION init_physician_rating() <PERSON><PERSON><PERSON>NS TRIGGER AS $init_physician_rating$ BEGIN IF NEW.physician_id IS NOT NULL THEN
INSERT INTO
    physician_rating
VALUES
    (NEW.physician_id, 0.0, 0);

RAISE NOTICE 'updated physician average rating';

END IF;

RETURN NULL;

-- result is ignored since this is an AFTER trigger
END;

$init_physician_rating$ LANGUAGE plpgsql;

CREATE
OR REPLACE TRIGGER init_physician_rating
AFTER
INSERT
    OR
UPDATE
    OR DELETE ON physician FOR EACH ROW EXECUTE FUNCTION init_physician_rating();