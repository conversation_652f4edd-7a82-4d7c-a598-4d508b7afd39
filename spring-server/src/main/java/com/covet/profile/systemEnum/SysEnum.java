package com.covet.profile.systemEnum;

public class SysEnum {
    public enum EClaimType {
        DENTAL(0), FLEX(1), LIFE(2), PROF(3), RX(4),
        STD(5), UB92(6), VIS(7);

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }

        private int value;

        EClaimType(int value) {
            this.value = value;
        }
    }

    public enum EGroupClaimType {
        MEDICAL("MEDICAL"), PHARMACY("PHARMACY"), OTHER("OTHER");

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        private String value;

        EGroupClaimType(String value) {
            this.value = value;
        }
    }

    public enum EVbaClaimType {
        Dental("DENTAL"),
        Flex("FLEX"),
        Life("LIFE"),
        Professional("PROF"),
        Pharmacy("RX"),
        Disability("STD"),
        Institutional("UB92"),
        Vision("VIS");

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        private String value;

        private EVbaClaimType(String value) {
            this.value = value;
        }
    }

    public enum EClaimStatus {
        Payable("A"),
        Denied_Partial("AD"),
        Denied("D"),
        Pended("E"),
        Error("ERR"),
        Paid("P"),
        Refund("R"),
        Void("VC"),
        Not_Adjudicated("X");

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        private String value;

        private EClaimStatus(String value) {
            this.value = value;
        }
    }

    public enum EPriorAuthorStatus {
        approved("approved"), pending("pending"), denied("denied");

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        private String value;

        private EPriorAuthorStatus(String value) {
            this.value = value;
        }
    }

    public enum EPharmacy {
        distance("distance"), cost("cost");

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        private String value;

        private EPharmacy(String value) {
            this.value = value;
        }
    }

    public enum EVisitType {
        Hospital(0), Outpatient(1), Virtual(2), UrgentCare(3);

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }

        private int value;

        private EVisitType(int value) {
            this.value = value;
        }
    }

    public enum ESpecialty {
//        Radiology("Radiology"), Cardiology("Cardiology"), Oncology("Oncology"), Psychology("Psychology"),
//        Endocrinology("Endocrinology"), Pediatrics("Pediatrics"), Dentist("Dentist");

        AcuteInpatientHospitals("Acute Inpatient Hospitals"),

        AllergyAndImmunology("Allergy and Immunology"),

        Audiology("Audiology"),

        Cardiology("Cardiology"),

        Chiropractor("Chiropractor"),

        CriticalCareServices("Critical Care Services"),

        IntensiveCareUnits("IntensiveCareUnits"),

        Dental("Dental"),

        Dermatology("Dermatology"),

        Radiology("Radiology"),

        Otolaryngology("ENT/Otolaryngology"),

        EmergencyMedicine("Emergency Medicine"),

        Endocrinology("Endocrinology"),

        Gastroenterology("Gastroenterology"),

        GeneralSurgery("General Surgery"),

        Gynecology("Gynecology, OB/GYN"),

        InfectiousDiseases("Infectious Diseases"),

        InpatientPsychiatricFacilityServices("Inpatient Psychiatric Facility Services"),

        Mammography("Mammography"),

        Nephrology("Nephrology"),

        Neurology("Neurology"),

        Neurosurgery("Neurosurgery"),

        OccupationalTherapy("Occupational Therapy"),

        OncologyMedical("Oncology-Medical,Surgical"),

        RadiationOncology("Oncology-Radiation/RadiationOncology"),

        Ophthalmology("Ophthalmology"),

        OrthopedicSurgery("Orthopedic Surgery"),

        BehavioralHealth("Behavioral Health"),

        Chemotherapy("Outpatient Infusion/ Chemotherapy"),

        Physiatry("Physiatry, Rehabilitative Medicine"),

        PhysicalTherapy("Physical Therapy"),

        PlasticSurgery("Plastic Surgery"),

        Podiatry("Podiatry"),

        PrimaryCare("Primary Care"),

        PrimaryCarePediatric("PrimaryCare – Pediatric"),

        Psychiatry("Psychiatry"),

        Pulmonology("Pulmonology"),

        Rheumatology("Rheumatology"),

        SkilledNursingFacilities("Skilled Nursing Facilities"),

        SpeechTherapy("Speech Therapy"),

        SurgicalServices("Surgical Services (Outpatient or ASC)"),

        UrgentCare("UrgentCare"),

        Urology("Urology"),

        VascularSurgery("Vascular Surgery");
        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        private String value;

        private ESpecialty(String value) {
            this.value = value;
        }
    }

    public enum EGroupSpecialty {
        Hospital("Hospital"),

        EmergencyRoom("Emergency room"),

        UrgentCare("Urgent care"),

        Telehealth("Telehealth"),

        OutpatientPhysicianOffice("Outpatient physician office"),

        PhysicalTherapyCenter("Physical therapy center"),

        Laboratory("Laboratory"),

        Pharmacy("Pharmacy"),

        Imaging("Imaging"),

        MentalHealthClinic("Mental health clinic"),

        AmbulatorySurgeryCenter("Ambulatory surgery center"),

        HomeHealth("Home health"),

        BirthingCenters("Birthing centers"),

        AssistedLiving("Assisted living"),

        PsychiatricFacilities("Psychiatric facilities"),

        Hospice("Hospice"),

        RehabFacility("Rehab facility"),

        PublicHealthClinic("Public health clinic"),

        CommunityHealthCenters("Community health centers"),

        Chiropractic("Chiropractic"),

        DiagnosticImagingAndTesting("Diagnostic Imaging and Testing"),

        HearingAid("Hearing Aid"),

        Lab("Lab"),

        Other("Other"),

        Podiatry("Podiatry"),

        Rehab("Rehab"),

        SurgeryCenter("Surgery Center"),

        Vision("Vision");

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        private String value;

        private EGroupSpecialty(String value) {
            this.value = value;
        }
    }

    public enum ESortType {
        ASC(1), DES(-1);

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }

        private int value;

        private ESortType(int value) {
            this.value = value;
        }
    }

    public enum EPaging {
        PageNumber(0), PageSize(10), PageSizeFroBenefits(25);

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }

        private int value;

        EPaging(int value) {
            this.value = value;
        }
    }

    public enum EFilterOptions {
        MinValue(0),
        MaxValue(100),
        MinDistance(0),
        MaxDistance(1500),
        DefaultRating(0);

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }

        private int value;

        EFilterOptions(int value) {
            this.value = value;
        }
    }

    public enum ENetworkId {
        RetailNetWork(243),
        MailOrderNetWork(38),
        PreferredNetWork(39),
        All(1);

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }

        private int value;

        ENetworkId(int value) {
            this.value = value;
        }
    }

    public enum ESwitch {
        Yes("Y"),
        No("N");

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        private String value;

        ESwitch(String value) {
            this.value = value;
        }
    }

    public enum EAppointmentType {
        CHECKUP(0), PROCEDURE(1), FOLLOWUP(2), SCREENING(3), TELEMEDICINE(4),
        OTHER_VISIT(5);

        public int getValue() {
            return value;
        }

        private int value;

        EAppointmentType(int value) {
            this.value = value;
        }

        public static EAppointmentType fromString(String text) {
            for (EAppointmentType b : EAppointmentType.values()) {
                if (b.name().equalsIgnoreCase(text.replace(" ", "_"))) {
                    return b;
                }
            }
            throw new IllegalArgumentException("No constant with text " + text + " found");
        }

        public static String getStringFromValue(int value) {
            for (EAppointmentType b : EAppointmentType.values()) {
                if (b.value == value) {
                    return b.name();
                }
            }
            throw new IllegalArgumentException("No constant with value " + value + " found");
        }
    }

    public enum EAppointmentStatus {
        PENDING("Pending"), UPCOMING("Upcoming"), COMPLETED("Completed");

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        private String value;

        EAppointmentStatus(String value) {
            this.value = value;
        }
    }
}
