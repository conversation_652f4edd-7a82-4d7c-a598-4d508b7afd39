package com.covet.profile.persistence.model;

import lombok.*;

import javax.persistence.*;
import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "physician_rating")
public class PhysicianRating {
    @Id
    @Column(name = "physician_id")
    private UUID physicianId;

    @Column(name = "average_rating")
    private double averageRating;

    @Column(name = "total_reviews")
    private long totalReviews;
}
