package com.covet.profile.clients.cigna.response.organization;

import com.covet.profile.clients.cigna.response.common.BaseMeta;
import com.covet.profile.clients.cigna.response.common.Link;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

@Data
@NoArgsConstructor
public class OrganizationResponse {
    public String resourceType;
    public BaseMeta meta;
    public String type;
    public int total;
    public List<Link> link = Collections.emptyList();
    public List<OrganizationEntry> entry = Collections.emptyList();
}
