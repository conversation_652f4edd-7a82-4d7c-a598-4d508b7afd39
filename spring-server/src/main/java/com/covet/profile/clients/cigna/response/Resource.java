package com.covet.profile.clients.cigna.response;

import com.covet.profile.clients.cigna.response.common.Address;
import com.covet.profile.clients.cigna.response.common.BaseMeta;
import com.covet.profile.clients.cigna.response.common.BaseExtension;
import com.covet.profile.clients.cigna.response.common.Identifier;
import com.covet.profile.clients.cigna.response.practitioner.Communication;
import com.covet.profile.clients.cigna.response.practitioner.Name;
import com.covet.profile.clients.cigna.response.practitioner.Qualification;
import lombok.Data;

import java.util.List;

@Data
public class Resource {
    private String resourceType;
    private String id;
    private BaseMeta meta;
    private String language;
    private List<BaseExtension> extension;
    private List<Identifier> identifier;
    private boolean active;
    private List<Name> name;
    private String gender;
    private String birthDate;
    private List<Qualification> qualification;
    private List<Communication> communication;
    private List<Telecom> telecom;
    private List<Address> address;
}
