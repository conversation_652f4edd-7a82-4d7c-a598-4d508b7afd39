package com.covet.profile.service;

import com.covet.profile.systemEnum.EDefaultForm;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.*;


@Slf4j
@Service
@Setter
public class S3Service {

    @Value("${spring.cloud.aws.s3.bucket}")
    private String bucketName;
    private Region bucketRegion;

    @Value("${spring.cloud.aws.s3.expire-days}")
    @Getter
    private int expiredDays;

    private static final String PREFIX_FOLDER = "images/";
    private static final String PREFIX_DEFAULT_FOLDER = "defaults/";
    private S3Client s3Client;
    private S3Presigner s3Presigner;

    public S3Service(@Value("${spring.cloud.aws.s3.region}") String regionString) {
        this.bucketRegion = Region.of(regionString);
        this.s3Client = S3Client.builder().region(bucketRegion).build();
        this.s3Presigner = S3Presigner.builder().region(bucketRegion).build();
    }

    public PutObjectResponse putFile(@NonNull MultipartFile file, @NonNull String objectKey) {
        String withPrefix = PREFIX_FOLDER + objectKey;
        PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                                                .bucket(bucketName)
                                                .key(withPrefix)
                                                .contentType(file.getContentType())
                                            .build();
        try {
            return s3Client.putObject(putObjectRequest, RequestBody.fromBytes(file.getBytes()));
        } catch (IOException e) {
            log.error(String.format("Encountered exception while putting request with file: %s and prefix: %s",
                    file.getName(), withPrefix), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * Delete the newest version of file with the provided objectKey
     *
     * @param objectKey - name of file
     * @return
     */
    public DeleteObjectResponse deleteFile(@NonNull String objectKey) {
        DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder()
                                                    .bucket(bucketName)
                                                    .key(PREFIX_FOLDER + objectKey)
                                                .build();
        return s3Client.deleteObject(deleteObjectRequest);
    }

    /**
     * Get the newest version of file
     * @param key - name of file
     */
    public InputStream getFile(@NonNull String key) {
        GetObjectRequest getRequest = GetObjectRequest.builder()
                                            .bucket(bucketName)
                                            .key(key)
                                            .build();
        return s3Client.getObject(getRequest);
    }

    public void getAllFiles(@NonNull String objectKey) {
        ListObjectVersionsRequest listRequest = ListObjectVersionsRequest.builder()
                .bucket(bucketName)
                .prefix(PREFIX_FOLDER + objectKey)
                .maxKeys(3) // fix or pass in?
                .build();
        ListObjectVersionsResponse listResponse = s3Client.listObjectVersions(listRequest);
        for (ObjectVersion version : listResponse.versions()) {
            System.err.println("isLatest: " + version.isLatest() + version.lastModified());
        }
    }

    public boolean doesBucketExist(@NonNull String bucketName) {
        HeadBucketRequest headBucketRequest = HeadBucketRequest.builder().bucket(bucketName).build();
        try {
            s3Client.headBucket(headBucketRequest);
            return true;
        } catch (NoSuchBucketException e) {
            log.error(String.format("No bucket is found for %s at region %s", bucketName, bucketRegion), e);
            return false;
        }
    }

    /**
     * @param objectKey - name of file
     * @return S3 Presigned URL
     */
    public URL getPresignedURL(@NonNull String objectKey) {
        String withPrefix = PREFIX_FOLDER + objectKey;
        try { // check if file exist
            s3Client.headObject(HeadObjectRequest.builder().bucket(bucketName)
                    .key(withPrefix).build());
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(withPrefix).build();
            GetObjectPresignRequest getObjectPresignRequest = GetObjectPresignRequest.builder()
                    .signatureDuration(Duration.ofDays(expiredDays))
                    .getObjectRequest(getObjectRequest)
                    .build();
            PresignedGetObjectRequest presignedGetObjectRequest = s3Presigner.presignGetObject(getObjectPresignRequest);
            return presignedGetObjectRequest.url();
        } catch (NoSuchKeyException e) {
            log.info(String.format("No objectKey %s with bucket is found for %s at region %s",
                    withPrefix, bucketName, bucketRegion), e);
            return null; // logging error is sufficient; it is ok to save null avatarURl to DB
        }
    }

    public URL getDefaultPresignedURL(@NonNull String objectKey) {
        String withPrefix = PREFIX_DEFAULT_FOLDER + objectKey;
        try {
            s3Client.headObject(HeadObjectRequest.builder().bucket(bucketName)
                    .key(withPrefix).build());
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(withPrefix)
                    .build();
            GetObjectPresignRequest getObjectPresignRequest = GetObjectPresignRequest.builder()
                    .signatureDuration(Duration.ofDays(expiredDays))
                    .getObjectRequest(getObjectRequest)
                    .build();
            PresignedGetObjectRequest presignedGetObjectRequest = s3Presigner.presignGetObject(getObjectPresignRequest);
            return presignedGetObjectRequest.url();
        } catch (NoSuchKeyException e) {
            log.info(String.format("No objectKey %s with bucket is found for %s at region %s",
                    withPrefix, bucketName, bucketRegion), e);
            return null;
        }
    }

    public Map<String, URL> getDefaultUrlMap() {
        Map<String, URL> result = new ConcurrentHashMap<>();
        try {

            Map<String, CompletableFuture<URL>> defaultUrlMap = new ConcurrentHashMap<>();
            var fields = Arrays.stream(EDefaultForm.values()).map(Enum::name).toList();
            fields.forEach(f -> {
                defaultUrlMap.computeIfAbsent(f,
                        (__) -> CompletableFuture.supplyAsync(() -> getDefaultPresignedURL(f + ".png")));
            });

            CompletableFuture.allOf(defaultUrlMap.values().toArray(new CompletableFuture[0])).join();

            defaultUrlMap.keySet().stream().parallel().forEach(
                    k -> {
                        result.computeIfAbsent(k, (__) -> defaultUrlMap.get(k).join());
                    }
            );
            return result;
        } catch (Exception e) {
            log.info("Something wrong when getting URL image default");
        }
        return result;
    }

    public Map<UUID, URL> getAvatarMap(List<UUID> cognitoIds) {
        Map<UUID, URL> result = new HashMap<>();
        if (!cognitoIds.isEmpty()) {
            var nThreads = Math.max(Runtime.getRuntime().availableProcessors() * 2, 4);
            ExecutorService executor = Executors.newFixedThreadPool(nThreads);
            try {
                List<CompletableFuture<URL>> futures = cognitoIds.stream().map(id -> CompletableFuture
                        .supplyAsync(() -> getPresignedURL(id.toString()), executor)).toList();
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                allFutures.join();

                for (int i = 0; i < cognitoIds.size(); i++) {
                    result.put(cognitoIds.get(i), futures.get(i).join());
                }

            } catch (Exception e) {
                log.error("FUNC: getAvatarMap with massage: {}", e.getMessage());
            } finally {
                executor.shutdown();
            }
        }
        return result;
    }
}