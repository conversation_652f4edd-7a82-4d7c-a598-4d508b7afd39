package com.covet.profile.persistence.compositeKey;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

@Embeddable
@Data
@NoArgsConstructor
public class PreferredPharmacyID implements Serializable {
    @Column(name = "cognito_id")
    private UUID cognitoId;

    @Column(name = "pharmacy_id")
    private String pharmacyId;

    // default constructor

    public PreferredPharmacyID(UUID patientId,
            String pharmacyId) {
        this.cognitoId = patientId;
        this.pharmacyId = pharmacyId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null) {
            return false;
        }
        if (getClass() != o.getClass()) {
            return false;
        }
        PreferredPharmacyID preferredPharmacy = (PreferredPharmacyID) o;
        return Objects.equals(getCognitoId(), preferredPharmacy.getCognitoId())
                && Objects.equals(getPharmacyId(), preferredPharmacy.getPharmacyId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getCognitoId(), getPharmacyId());
    }
}
