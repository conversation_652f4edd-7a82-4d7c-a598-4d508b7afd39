package com.covet.profile.FactoryMethod.GetFavorite;

import com.covet.profile.dto.covet.provider.PhysicianDetailDto;
import com.covet.profile.persistence.model.Network;
import com.covet.profile.persistence.model.Physician;
import com.covet.profile.persistence.model.Specialty;
import com.covet.profile.persistence.repository.PatientFavoriteRepository;
import com.covet.profile.persistence.repository.PhysicianRepository;
import com.covet.profile.service.NpiService;
import com.covet.profile.service.PhysicianService;
import com.covet.profile.service.ProfileService;
import com.covet.profile.service.S3Service;
import com.covet.profile.systemEnum.EFavoriteType;
import com.covet.profile.utils.AuthUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Root;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class GetProviderFavorite implements IGetFavoriteMethod<GetFavoriteRequest,
        Page<PhysicianDetailDto>> {

    private final S3Service s3Service;

    private final PatientFavoriteRepository patientFavoriteRepository;
    private final NpiService npiService;
    private final PhysicianService physicianService;
    private final ProfileService profileService;
    @Autowired
    @PersistenceContext
    private EntityManager em;

    private List<Physician> getFavoritePhysician(List<UUID> physicianIds) {
        var cb = em.getCriteriaBuilder();
        var cq = cb.createQuery(Physician.class);
        Root<Physician> physicianRoot = cq.from(Physician.class);
        physicianRoot.fetch(Physician.Fields.networks, JoinType.LEFT);
        physicianRoot.fetch(Physician.Fields.specialties, JoinType.LEFT);
        physicianRoot.fetch(Physician.Fields.physicianRating);
        physicianRoot.fetch(Physician.Fields.profile);

        return em.createQuery(
                cq.select(physicianRoot).where(physicianRoot.get(Physician.Fields.physicianId).in(physicianIds))
        ).getResultList();
    }
    @Override
    @Transactional
    public Page<PhysicianDetailDto> getFavorite(GetFavoriteRequest request) {
        Page<UUID> favoriteIds = patientFavoriteRepository.findPatientFavoritesPagination(
                AuthUtils.getCognitoId(), getFavoriteType().getValue(), PageRequest.of(request.pageNumber, request.pageSize)
        );

        var providers = getFavoritePhysician(favoriteIds.getContent());
        List<String> npiList = providers.stream().map(Physician::getNpi).toList();
        var npiInfoList = npiService.getNpiInfoList(npiList);

        var providerAvatarMap = s3Service.getAvatarMap(favoriteIds.getContent());

        Double lat;
        Double lng;
        if (Objects.isNull(request.latitude) || Objects.isNull(request.longitude)) {
            var me = profileService.fetchMeCoordinate();
            lat = me.getLatitude();
            lng = me.getLongitude();
        } else {
            try {
                lat = Double.parseDouble(request.latitude.trim());
                lng = Double.parseDouble(request.longitude.trim());
            } catch (NumberFormatException e) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "lat or lng was wrong format");
            }
        }
        Map<UUID, Set<Network>> networkMap = providers
                .stream()
                .collect(Collectors.toMap(Physician::getPhysicianId, Physician::getNetworks));

        Map<UUID, List<Specialty>> specialtyMap = providers
                .stream()
                .collect(Collectors.toMap(Physician::getPhysicianId, Physician::getSpecialties));
        var physicianResponses = physicianService.convertPhysiciansData(
                lat,
                lng,
                providers,
                npiInfoList,
                favoriteIds.getContent(),
                providerAvatarMap,
                networkMap,
                specialtyMap,
                null,
                false);
        return new PageImpl<>(physicianResponses, favoriteIds.getPageable(), favoriteIds.getTotalElements());
    }

    @Override
    public EFavoriteType getFavoriteType() {
        return EFavoriteType.PROVIDER;
    }
}
