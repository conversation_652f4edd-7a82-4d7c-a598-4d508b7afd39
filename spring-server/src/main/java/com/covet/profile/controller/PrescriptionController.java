package com.covet.profile.controller;

import com.covet.profile.clients.vba.VBADtos.Member.SubscriberMemberDto;
import com.covet.profile.dto.CapRxDto.drug.request.PriceDrugParamsDto;
import com.covet.profile.dto.covet.pharmacy.PharmacyDetailDto;
import com.covet.profile.dto.covet.prescription.*;
import com.covet.profile.dto.covet.profile.ProfileDto;
import com.covet.profile.persistence.compositeKey.PrescriptionID;
import com.covet.profile.persistence.compositeKey.PrescriptionPharmacyID;
import com.covet.profile.dto.CapRxDto.drug.response.CapDrugPriceListResponseDto;
import com.covet.profile.dto.CapRxDto.plan.CapRxMemberDto;
import com.covet.profile.dto.ProviderDto.LocationDto;
import com.covet.profile.dto.location.ICoordinate;
import com.covet.profile.helper.Coordinate;
import com.covet.profile.persistence.model.PharmacyListWithMessage;
import com.covet.profile.responses.ResponseHandler;
import com.covet.profile.responses.prescriptions.PrescriptionPharmaciesResponse;
import com.covet.profile.service.*;
import com.covet.profile.service.interfaces.ICapitalRxService;
import com.covet.profile.systemEnum.SysEnum;
import com.covet.profile.utils.AuthUtils;
import com.covet.profile.clients.vba.VBAService.VBAUserService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CompletableFuture;

// @PreAuthorize("hasRole('patient') || hasRole('physician')")
@Validated
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class PrescriptionController {
    public static final Logger log = LoggerFactory.getLogger(PrescriptionController.class);
    private final PrescriptionService prescriptionService;
    private final PharmacyService pharmacyService;
    private final ProfileService profileService;

    private final VBAUserService vbaUserService;

    private final ICapitalRxService capitalRxService;

    private final Auth0Service auth0Service;

    private final PrescriptionPharmacyService prescriptionPharmacyService;

    private final FirebaseMessagingService firebaseMessagingService;


    @PostMapping("/prescriptions")
    public ResponseEntity<PrescriptionDto> createPrescription(
            @Valid @RequestBody final PrescriptionPharmacyRequestDto prescriptionPharmacyRequestDto) {

        return prescriptionPharmacyRequestDto.getPrescriptionDto().getPrescriptionId()
                .map(prescriptionId -> {
                    prescriptionId.setPatientID(AuthUtils.getCognitoId());

                    return prescriptionService.getPrescriptionById(prescriptionId).map(dto -> {
                        log.error("Prescription Id {} already exist for create", prescriptionId);
                        return new ResponseEntity<PrescriptionDto>(HttpStatus.CONFLICT);
                    }).orElseGet(() -> {
                        prescriptionService.createOrUpdatePrescription(prescriptionPharmacyRequestDto.getPrescriptionDto());

                        // No need to check pharmacy is valid, already checked in get preferred pharmacies and get drug pharmacies endpoints
                        if (prescriptionPharmacyRequestDto.getPharmacyId() != null && !prescriptionPharmacyRequestDto.getPharmacyId().isBlank()) {
                            PrescriptionPharmacyID prescriptionPharmacyID = new PrescriptionPharmacyID(prescriptionId.getPrescriptionName(), prescriptionId.getPrescriptionCode(), prescriptionPharmacyRequestDto.getPharmacyId(), prescriptionId.getPatientID());
                            PrescriptionPharmacyDto prescriptionPharmacyDto = ImmutablePrescriptionPharmacyDto.of(
                                    Optional.ofNullable(prescriptionPharmacyID), prescriptionPharmacyRequestDto.getCost()
                            );

                            pharmacyService.createPrescriptionPharmacy(prescriptionPharmacyDto);
                        }

                        return new ResponseEntity<>(prescriptionPharmacyRequestDto.getPrescriptionDto(), HttpStatus.OK);
                    });
                }).orElse(new ResponseEntity<>(HttpStatus.BAD_REQUEST));
    }

    @Operation(summary = "Get prescription detail with pharmacy info (if any)")
    @GetMapping("/prescription/pharmacy/detail")
    public ResponseEntity<Object> getPrescriptionPharmacyDetailById(String prescriptionName, String prescriptionCode, String pharmacyID, @ParameterObject LocationDto location) {
        UUID patientID = auth0Service.getCognitoId();
        Optional<ProfileDto> profile = profileService.getProfileById(patientID);

        Coordinate coordinate = profile
                .filter(p -> Objects.isNull(location.getLatitude()) || Objects.isNull(location.getLongitude()))
                .map(p -> new Coordinate(p.getLatitude().orElseThrow(() -> new IllegalArgumentException("Latitude is missing")),
                        p.getLongitude().orElseThrow(() -> new IllegalArgumentException("Longitude is missing"))))
                .orElseGet(() -> new Coordinate(
                        Objects.nonNull(location.getLatitude()) ? Double.parseDouble(location.getLatitude()) : 0.0,
                        Objects.nonNull(location.getLongitude()) ? Double.parseDouble(location.getLongitude()) : 0.0));

        var prescriptionDetail = prescriptionService.getPrescriptionDetail(prescriptionName,
                prescriptionCode, patientID);

        PharmacyDetailDto pharmacy = pharmacyService.getPharmacyByIdAndPrescriptionNameAndCode(new PrescriptionID(prescriptionName, prescriptionCode, patientID), coordinate ,pharmacyID);
        return ResponseHandler.generatePrescriptionPharmacyDetailResponse(prescriptionDetail, pharmacy,
                HttpStatus.OK);
    }

    @Operation(summary = "Get prescription list: \n When add new prescription - if user dont have any preferred pharmacy for this prescription -> get list from cap Rx" +
                                                "\n When change pharmacy - show pharmacies contain selected drug from cap RX list (exclude current pharmacy)")
    // @PreAuthorize("hasAnyRole('patient', 'physician')")
    @GetMapping("/prescription/pharmacies")
    public ResponseEntity<PrescriptionPharmaciesResponse> getPrescriptionCapRxPharmacyList(String prescriptionName, String prescriptionCode,
                                                                                         Optional<Integer> page, Optional<Integer> pageSize, String pharmacyID,
                                                                                         @ParameterObject Optional<SysEnum.EPharmacy> ePharmacy, @ParameterObject LocationDto location ) throws Exception {
        String auth0ID = AuthUtils.getAuth0Id();
        UUID patientID = AuthUtils.getCognitoId();

        ProfileDto profile = profileService.getProfileById(patientID).orElseThrow(() -> new Exception("Profile not found"));

        SubscriberMemberDto subscriberID = auth0Service.getSubscriberMemberID(auth0ID);

        PrescriptionDetailDto prescriptionDetail = prescriptionService.getPrescriptionDetail(prescriptionName,
                prescriptionCode, patientID);


        List<CapRxMemberDto> memberList = capitalRxService.getMember(subscriberID).getMemberList();
        Optional<CapRxMemberDto> member = memberList.stream().filter(item -> item.getMember().getExternalMemberId().equals(subscriberID.getSubscriberId())).findFirst();


        PriceDrugParamsDto priceDrugParamsDto = new PriceDrugParamsDto();

        priceDrugParamsDto.setDosage(prescriptionDetail.getPrescription().getStrength());
        priceDrugParamsDto.setDrugId(prescriptionDetail.getPrescription().getDrugID()); //10888


        LocationDto locationDto = new LocationDto("0", "0");

        if (Objects.nonNull(location.getLatitude()) &&
                Objects.nonNull(location.getLongitude())) {
            locationDto = location;
        } else {
            locationDto.setLatitude(profile.getLatitude().orElse(0.0).toString());
            locationDto.setLongitude(profile.getLongitude().orElse(0.0).toString());

        }
        priceDrugParamsDto.setLocation(locationDto);

        priceDrugParamsDto.setQuantity(prescriptionDetail.getPrescription().getQuantity()); //20
        priceDrugParamsDto.setDosageForm(prescriptionDetail.getPrescription().getForm()); //Tablet
        if (member.isPresent()) {
            priceDrugParamsDto.setPlanId(member.get().getPlan().getId());
        }
        priceDrugParamsDto.setExternalMemberId(subscriberID.getSubscriberId());
        priceDrugParamsDto.setPersonCode(subscriberID.getMemberSequence());

        Optional<CapDrugPriceListResponseDto> drugPrice = Optional.ofNullable(capitalRxService.getDrugPrice(priceDrugParamsDto));

        List<PharmacyDetailDto> pharmacies = pharmacyService.getCapRxPharmacyListByPrescription(drugPrice, profile, pharmacyID, ePharmacy);
        Pageable pageable = PageRequest.of(page.orElse(SysEnum.EPaging.PageNumber.getValue()), pageSize.orElse(SysEnum.EPaging.PageSize.getValue()));

        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), pharmacies.size());
        Page<PharmacyDetailDto> pageResponse = new PageImpl<>(pharmacies.subList(start, end), pageable, pharmacies.size());
        Optional<PharmacyDetailDto> preferredPharmacyDetail = pharmacyService.getPreferredPharmacyDetail(patientID);

        PrescriptionPharmaciesResponse response = new PrescriptionPharmaciesResponse();

        response.setPage(pageResponse);
        response.setIsPreferredExist(preferredPharmacyDetail.isPresent());

        return new ResponseEntity<>(response, HttpStatus.OK);
    }


    @Operation(summary = "Get prescription list: \n When add new prescription - if user dont have any preferred pharmacy for this prescription -> get list from cap Rx" +
                                                "\n When change pharmacy - show pharmacies contain selected drug from cap RX list (exclude current pharmacy)")
    // @PreAuthorize("hasAnyRole('patient', 'physician')")
    @GetMapping("/v2/prescription/pharmacies")
    public ResponseEntity<PrescriptionPharmaciesResponse> getPrescriptionCapRxPharmacyList(String prescriptionName, String prescriptionCode,
                                                                                         Optional<Integer> page, Optional<Integer> pageSize, String pharmacyID, Optional<String> latitude, Optional<String> longitude,
                                                                                         @ParameterObject Optional<SysEnum.EPharmacy> ePharmacy ) throws Exception {
        var startTime = System.currentTimeMillis();
        String auth0ID = auth0Service.getAuth0Id();
        UUID patientID = auth0Service.getCognitoId();
        SubscriberMemberDto subscriberID = auth0Service.getSubscriberMemberID(auth0ID);

        //TODO: Remove after android review (my prod user is not in caprx)
        if (subscriberID.getSubscriberId().equals("639236184")){
            subscriberID.setSubscriberId("018534942");
        }

        List<CompletableFuture<?>> futures = new LinkedList<>();

        CompletableFuture<PrescriptionDetailDto> prescriptionDetailFuture = CompletableFuture.supplyAsync(() ->
                prescriptionService.getPrescriptionDetail(prescriptionName, prescriptionCode, patientID)
        );

        CompletableFuture<Optional<CapRxMemberDto>> memberFuture = CompletableFuture.supplyAsync(() -> {
            List<CapRxMemberDto> memberList = capitalRxService.getMember(subscriberID).getMemberList();
            return memberList.stream()
                    .filter(item -> item.getMember().getExternalMemberId().equals(subscriberID.getSubscriberId()))
                    .findFirst();
        });

        futures.add(prescriptionDetailFuture);
        futures.add(memberFuture);

        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allOf.join();
        var prescriptionDetail = prescriptionDetailFuture.join();
        var member = memberFuture.join();

        PriceDrugParamsDto priceDrugParamsDto = new PriceDrugParamsDto();

        priceDrugParamsDto.setDosage(prescriptionDetail.getPrescription().getStrength());
        priceDrugParamsDto.setDrugId(prescriptionDetail.getPrescription().getDrugID()); //10888

        var location = LocationDto.builder();
        if (latitude.isPresent() && longitude.isPresent()) {
            location.latitude(latitude.get());
            location.longitude(longitude.get());
        } else {
            ICoordinate coordinate = profileService.fetchMeCoordinate();
            var lat = Optional.ofNullable(coordinate.getLatitude()).orElse(0.0);
            var lng = Optional.ofNullable(coordinate.getLongitude()).orElse(0.0);

            location.latitude(String.valueOf(lat));
            location.longitude(String.valueOf(lng));
        }

        priceDrugParamsDto.setLocation(location.build());

        priceDrugParamsDto.setQuantity(prescriptionDetail.getPrescription().getQuantity()); //20
        priceDrugParamsDto.setDosageForm(prescriptionDetail.getPrescription().getForm()); //Tablet

        member.ifPresent(capRxMemberDto -> priceDrugParamsDto.setPlanId(capRxMemberDto.getPlan().getId()));
        priceDrugParamsDto.setExternalMemberId(subscriberID.getSubscriberId());
        priceDrugParamsDto.setPersonCode(subscriberID.getMemberSequence());
        var endTime = System.currentTimeMillis();
        log.info("execution time {}", endTime - startTime);
        Optional<CapDrugPriceListResponseDto> drugPrice = Optional.ofNullable(capitalRxService.getDrugPrice(priceDrugParamsDto));
        log.info("DRUG PRICES:::: {}", drugPrice);
        PharmacyListWithMessage pharmacies = pharmacyService.getCapRxPharmacyListByPrescriptionMessage(drugPrice, location.build(), pharmacyID, ePharmacy);

        if (pharmacies.getPharmacyList().isEmpty()) {
            return ResponseEntity
                    .status(HttpStatus.OK)//TODO: align with front end for handling 404/204/empty 200
                .body(new PrescriptionPharmaciesResponse(pharmacies.getMessage()));
        }

        Pageable pageable = PageRequest.of(page.orElse(SysEnum.EPaging.PageNumber.getValue()), pageSize.orElse(SysEnum.EPaging.PageSize.getValue()));

        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), pharmacies.getPharmacyList().size());
        Page<PharmacyDetailDto> pageResponse = new PageImpl<>(pharmacies.getPharmacyList().subList(start, end), pageable, pharmacies.getPharmacyList().size());

        Long numPreferredPharmacy = pharmacyService.countPreferredPharmacy(patientID);

        PrescriptionPharmaciesResponse response = new PrescriptionPharmaciesResponse();
        response.setPage(pageResponse);
        response.setIsPreferredExist(numPreferredPharmacy > 0);

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @GetMapping("/prescription/show-search")
    public ResponseEntity<Boolean> isPrescriptionSearchVisible() {
        try {
            String auth0ID = AuthUtils.getAuth0Id();
            SubscriberMemberDto subscriberID = auth0Service.getSubscriberMemberID(auth0ID);
            var enrollmentSubscriber = vbaUserService.getEnrollmentSubscriber(subscriberID.getSubscriberId());
            var groupId = enrollmentSubscriber.getGroupId();
            boolean hasPrescriptionSearch = vbaUserService.checkForPrescriptionSearch(groupId);
            return ResponseEntity.ok(hasPrescriptionSearch);
        } catch (Exception e) {
            log.error("Failed to get prescription search visibility", e);
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    // @PreAuthorize("hasAnyRole('patient', 'physician')")
    //CH-180: This endpoint will be replaced by /v2/prescription/drug/pharmacies after FE finish implement
    @GetMapping("/prescription/drug/pharmacies")
    public ResponseEntity<PrescriptionPharmaciesResponse> getPrescriptionCapRxPharmacyListToAdd(PrescriptionRequestDto prescriptionRequestDto,
                                                                                                Optional<Integer> page, Optional<Integer> pageSize,
                                                                                                Optional<SysEnum.ESortType> eSortType, @ParameterObject LocationDto location) throws Exception {
        String auth0ID = AuthUtils.getAuth0Id();
        String email = auth0Service.getUserEmail(auth0ID);

        UUID patientID = AuthUtils.getCognitoId();
        SubscriberMemberDto subscriberID = vbaUserService.getSubscriberMemberInfo(email);
        List<CapRxMemberDto> memberList = capitalRxService.getMember(subscriberID).getMemberList();
        Optional<CapRxMemberDto> member = memberList.stream().filter(item -> item.getMember().getExternalMemberId().equals(subscriberID.getSubscriberId())).findFirst();


        PriceDrugParamsDto priceDrugParamsDto = new PriceDrugParamsDto();

        priceDrugParamsDto.setDosage(prescriptionRequestDto.getStrength());
        priceDrugParamsDto.setDrugId(prescriptionRequestDto.getDrugId());
        priceDrugParamsDto.setQuantity(prescriptionRequestDto.getQuantity().intValue()); //Update requested from FE, so they can reuse data type double of quantiy field fetched from caprx
        priceDrugParamsDto.setDosageForm(prescriptionRequestDto.getForm());
        priceDrugParamsDto.setExternalMemberId(subscriberID.getSubscriberId());
        priceDrugParamsDto.setPersonCode(subscriberID.getMemberSequence());

        LocationDto locationDto = new LocationDto("0", "0");

        if (Objects.nonNull(location.getLatitude()) &&
                Objects.nonNull(location.getLongitude())) {
            locationDto = location;
        } else {
            ICoordinate coordinate = profileService.fetchMeCoordinate();
            var lat = coordinate.getLatitude();
            var lng = coordinate.getLongitude();

            locationDto.setLatitude(String.valueOf(lat));
            locationDto.setLongitude(String.valueOf(lng));

        }
        priceDrugParamsDto.setLocation(locationDto);

        member.ifPresent(capRxMemberDto -> priceDrugParamsDto.setPlanId(capRxMemberDto.getPlan().getId()));
        Optional<CapDrugPriceListResponseDto> drugPrice = Optional.ofNullable(capitalRxService.getDrugPrice(priceDrugParamsDto));

        var prescriptionName = prescriptionRequestDto.getPrescriptionName();
        var prescriptionCode = prescriptionRequestDto.getPrescriptionCode();
        var prescriptionId = new PrescriptionID(prescriptionName, prescriptionCode, patientID);

        PharmacyListWithMessage pharmacies = pharmacyService.getCapRxPharmacyListByPrescriptionToAdd(
                prescriptionId, drugPrice, locationDto);

        var pageNumber = page.orElse(SysEnum.EPaging.PageNumber.getValue());
        var targetPageSize = pageSize.orElse(SysEnum.EPaging.PageSize.getValue());
        Pageable pageable = PageRequest.of(pageNumber, targetPageSize);

        var pharmacyList = pharmacies.getPharmacyList();
        // Sort pharmacies by distance if eSortType is present and DESCENDING
        if (eSortType.isPresent() && eSortType.get() == SysEnum.ESortType.DES) {
            pharmacyList.sort(Comparator.comparing(PharmacyDetailDto::getDistance).reversed());
        }
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), pharmacyList.size());
        Page<PharmacyDetailDto> pageResponse = new PageImpl<>(pharmacyList.subList(start, end), pageable, pharmacyList.size());

        Long numPreferredPharmacy = pharmacyService.countPreferredPharmacy(patientID);

        PrescriptionPharmaciesResponse response = new PrescriptionPharmaciesResponse();
        response.setPage(pageResponse);
        response.setIsPreferredExist(numPreferredPharmacy > 0);

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    //CH-180: Rework handling cases of successful but empty requests
    @GetMapping("/v2/prescription/drug/pharmacies")
    public ResponseEntity<PrescriptionPharmaciesResponse> getPrescriptionCapRxPharmaciesToAdd(PrescriptionRequestDto prescriptionRequestDto,
                                                                                                Optional<Integer> page, Optional<Integer> pageSize,
                                                                                                Optional<SysEnum.ESortType> eSortType, @ParameterObject LocationDto location) throws Exception {
        String auth0ID = auth0Service.getAuth0Id();
        String email = auth0Service.getUserEmail(auth0ID);
        UUID patientID = auth0Service.getCognitoId();

        SubscriberMemberDto subscriberID = vbaUserService.getSubscriberMemberInfo(email);

        List<CapRxMemberDto> memberList = capitalRxService.getMember(subscriberID).getMemberList();
        Optional<CapRxMemberDto> member = memberList.stream().filter(item -> item.getMember().getExternalMemberId().equals(subscriberID.getSubscriberId())).findFirst();

        PriceDrugParamsDto priceDrugParamsDto = new PriceDrugParamsDto();
        priceDrugParamsDto.setDosage(prescriptionRequestDto.getStrength());
        priceDrugParamsDto.setDrugId(prescriptionRequestDto.getDrugId());
        priceDrugParamsDto.setQuantity(prescriptionRequestDto.getQuantity().intValue()); //Update requested from FE, so they can reuse data type double of quantiy field fetched from caprx
        priceDrugParamsDto.setDosageForm(prescriptionRequestDto.getForm());
        priceDrugParamsDto.setExternalMemberId(subscriberID.getSubscriberId());
        priceDrugParamsDto.setPersonCode(subscriberID.getMemberSequence());

        LocationDto locationDto = new LocationDto("0", "0");

        if (Objects.nonNull(location.getLatitude()) &&
                Objects.nonNull(location.getLongitude())) {
            locationDto = location;
        } else {
            ICoordinate coordinate = profileService.fetchMeCoordinate();
            var lat = coordinate.getLatitude();
            var lng = coordinate.getLongitude();

            locationDto.setLatitude(String.valueOf(lat));
            locationDto.setLongitude(String.valueOf(lng));

        }
        priceDrugParamsDto.setLocation(locationDto);

        member.ifPresent(capRxMemberDto -> priceDrugParamsDto.setPlanId(capRxMemberDto.getPlan().getId()));

        Optional<CapDrugPriceListResponseDto> drugPrice = Optional.ofNullable(capitalRxService.getDrugPrice(priceDrugParamsDto));

        var prescriptionName = prescriptionRequestDto.getPrescriptionName();
        var prescriptionCode = prescriptionRequestDto.getPrescriptionCode();
        var prescriptionId = new PrescriptionID(prescriptionName, prescriptionCode, patientID);
        PharmacyListWithMessage pharmacies = pharmacyService.getCapRxPharmacyListByPrescriptionToAdd(prescriptionId, drugPrice, locationDto);
        if (pharmacies.getPharmacyList().isEmpty()) {
            return ResponseEntity
                .status(HttpStatus.OK)
                .body(new PrescriptionPharmaciesResponse(pharmacies.getMessage()));
        }

        var pharmacyList = pharmacies.getPharmacyList();
        Pageable pageable = PageRequest.of(page.orElse(SysEnum.EPaging.PageNumber.getValue()),
                pageSize.orElse(SysEnum.EPaging.PageSize.getValue()));

        // Sort pharmacies by distance if eSortType is present and DESCENDING
        if (eSortType.isPresent() && eSortType.get() == SysEnum.ESortType.DES) {
            pharmacyList.sort(Comparator.comparing(PharmacyDetailDto::getDistance).reversed());
        }

        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), pharmacyList.size());
        Page<PharmacyDetailDto> pageResponse = new PageImpl<>(pharmacyList.subList(start, end), pageable, pharmacyList.size());
        Long numPreferredPharmacy = pharmacyService.countPreferredPharmacy(patientID);

        PrescriptionPharmaciesResponse response = new PrescriptionPharmaciesResponse();

        response.setPage(pageResponse);
        response.setIsPreferredExist(numPreferredPharmacy > 0);

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    // @PreAuthorize("hasRole('insurance')")
    @PostMapping(path = "/prescriptions/images", consumes = { MediaType.MULTIPART_FORM_DATA_VALUE })
    public ResponseEntity<URL> postImageForPrescription(
            @RequestParam String prescriptionName, @RequestParam String prescriptionCode,
            @RequestParam MultipartFile file) {
        PrescriptionID prescriptionId = new PrescriptionID(prescriptionName,
                prescriptionCode);
        URL result = prescriptionService.postImageForPrescription(file, prescriptionId);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PutMapping(path = "/prescriptions/update-mapping-pharmacy")
    public ResponseEntity updateMappingPharmacy(@RequestParam String oldPharmacyId, @RequestParam String newPharmacyId, @RequestParam String prescriptionName, @RequestParam String prescriptionCode, @RequestParam Double cost){
        UUID patientId = AuthUtils.getCognitoId();
        PrescriptionPharmacyID currentPrescriptionPharmacyID = new PrescriptionPharmacyID(prescriptionName, prescriptionCode, oldPharmacyId, patientId);


        return prescriptionPharmacyService.findPrescriptionPharmacyById(currentPrescriptionPharmacyID).map(prescriptionPharmacyDto -> {
            PrescriptionPharmacyID prescriptionPharmacyId = prescriptionPharmacyDto.getPrescriptionPharmacy().get();
            PrescriptionPharmacyID newPrescriptionPharmacyID = new PrescriptionPharmacyID(prescriptionPharmacyId.getPrescriptionName(), prescriptionPharmacyId.getPrescriptionCode(), newPharmacyId, prescriptionPharmacyId.getPatientID());
            PrescriptionPharmacyDto newPrescriptionPharmacyDto = ImmutablePrescriptionPharmacyDto.of(Optional.of(newPrescriptionPharmacyID), cost);
            PrescriptionPharmacyDto updatedPrescriptionPharmacyDto = prescriptionPharmacyService.createOrUpdatePrescription(newPrescriptionPharmacyDto);
            prescriptionPharmacyService.deletePrescriptionPharmacyById(currentPrescriptionPharmacyID);
            return new ResponseEntity(updatedPrescriptionPharmacyDto, HttpStatus.OK);
        }).orElseGet(() -> new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    // @PreAuthorize("hasAuthority('read:patientdata')")
    @DeleteMapping("/prescriptions/delete-active-prescription")
    public ResponseEntity deleteActivePrescription(@RequestParam String prescriptionName, @RequestParam String prescriptionCode) {
        UUID patientId = AuthUtils.getCognitoId();
        PrescriptionID prescriptionId = new PrescriptionID(prescriptionName, prescriptionCode, patientId);
        return prescriptionService.getPrescriptionById(prescriptionId).map(prescription -> {
            var prescriptionPharmacy = prescriptionPharmacyService.findPrescriptionPharmacyByPrescriptionId(prescriptionId);
            if (prescriptionPharmacy.isPresent()) {
                prescriptionPharmacyService.deletePrescriptionPharmacyById(prescriptionPharmacy.get().getPrescriptionPharmacy().get());
            }
            prescriptionService.deleteActivePrescription(prescription.getPrescriptionId().get());
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        }).orElseGet(() -> {
            log.error("Prescription Id {} not exist for delete", prescriptionId);
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        });
    }

    @PostMapping("test-push-noti")
    public void testPushNoti(String token, @RequestBody Object body) {
        firebaseMessagingService.testPushNoti(token, body);
    }
}
