package com.covet.profile.dto.CapRxDto.plan;

import com.covet.profile.dto.CapRxDto.member.MemberDto;
import lombok.Data;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;

@Data
public class CapRxMemberDto implements Serializable {
    @JsonProperty("group")
    private CapRxGroupDto group;
    @JsonProperty("member")
    private MemberDto member;
    @JsonProperty("plan")
    private CapRxPlanDto plan;
}
