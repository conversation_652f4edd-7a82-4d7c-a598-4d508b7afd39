package com.covet.profile.controller;

import com.covet.profile.config.SecretMangerConfig;
import com.covet.profile.dto.zendeskDto.ZendeskAppointmentRequestDto;
import com.covet.profile.dto.zendeskDto.ZendeskAppointmentDto;
import com.covet.profile.persistence.repository.WebhookDataRepository;
import com.covet.profile.service.AppointmentService;
import com.covet.profile.service.ProfileService;
import com.covet.profile.service.ZendeskService;
import com.covet.profile.service.interfaces.IPhysicianService;
import com.covet.profile.systemEnum.SysEnum;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Optional;

@Validated
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class ZendeskController {
    public static final Logger log = LoggerFactory.getLogger(ZendeskController.class);

    private final AppointmentService appointmentService;

    private final IPhysicianService physicianService;

    private final ZendeskService zendeskService;

    private final WebhookDataRepository webhookDataRepository;

    private final ProfileService profileService;


    @Autowired
    public ZendeskController(AppointmentService appointmentService, IPhysicianService physicianService, WebhookDataRepository webhookDataRepository, SecretMangerConfig secretMangerConfig, ZendeskService zendeskService, ProfileService profileService) {
        this.appointmentService = appointmentService;
        this.physicianService = physicianService;
        this.webhookDataRepository = webhookDataRepository;
        this.zendeskService = zendeskService;
        this.profileService = profileService;
    }


    @PostMapping("/zendesk/webhook-appointment")
    public ResponseEntity handleZendeskAppointmentWebhook(@RequestBody ZendeskAppointmentRequestDto request) {

        // Convert appointment type string to enum
        SysEnum.EAppointmentType appointmentType = SysEnum.EAppointmentType.fromString(request.getAppointmentType());


        LocalDateTime appointmentDateTime = LocalDateTime.of(request.getAppointmentDate(), request.getAppointmentTime());

        if (appointmentDateTime.isBefore(LocalDateTime.now())) {
            return ResponseEntity.badRequest().body("Appointment date and time must be in the future.");
        }

        // Combine date and time and save to DB
        ZendeskAppointmentDto appointmentData = new ZendeskAppointmentDto(
                request.getAppointmentId(),
                request.getProviderName(),
                request.getNpi(),
                request.getAppointmentDate(),
                request.getAppointmentTime(),
                appointmentType.getValue()
        );

        Optional<ResponseEntity<?>> responseEntity  = physicianService.findPhysicianByNameAndNpi(request.getProviderName(), request.getNpi())
                .flatMap(physician -> {
                    try {
                        appointmentService.updateZendeskAppointment(appointmentData, physician, appointmentDateTime);
                        return Optional.of(new ResponseEntity<>(HttpStatus.OK));
                    } catch (Exception e) {
                        log.error("Error while updating appointment ticket", e);
                        return Optional.of(new ResponseEntity<>("An error occurred while updating appointment ticket", HttpStatus.INTERNAL_SERVER_ERROR));
                    }
                });

        return responseEntity.orElseGet(() -> {
            log.error("Physician {} doesn't exist for updating appointment", request.getProviderName());
            return new ResponseEntity<>("Physician not found", HttpStatus.BAD_REQUEST);
        });
    }

//    @PostMapping("/zendesk/webhook-appointment")
//    public ResponseEntity handleZendeskAppointmentWebhook(@RequestHeader(name = "x-zendesk-webhook-signature", required = false) String signature,
//                                                          @RequestHeader(name = "x-zendesk-webhook-signature-timestamp", required = false) String timestamp, @RequestBody JsonNode jsonData) throws NoSuchAlgorithmException, InvalidKeyException {
//
//        log.info("check validate signature: {}", zendeskService.isValidSignature(signature, jsonData.toString(), timestamp));
//        WebhookData webhookData = new WebhookData();
//        webhookData.setJsonData(jsonData.toString());
//        webhookDataRepository.save(webhookData);
//        appointmentService.triggerNotiAppointment(jsonData.toString());
//
//        return ResponseEntity.ok("Data received and saved");
////        if (zendeskService.isValidSignature(signature, jsonData.toString(), timestamp)) {
////            // Process the valid webhook request
////            WebhookData webhookData = new WebhookData();
////            webhookData.setJsonData(jsonData.toString());
////            webhookDataRepository.save(webhookData);
////
////            return ResponseEntity.ok("Data received and saved");
////        } else {
////            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("zendesk signature is invalid");
////        }
//
//    }

}
