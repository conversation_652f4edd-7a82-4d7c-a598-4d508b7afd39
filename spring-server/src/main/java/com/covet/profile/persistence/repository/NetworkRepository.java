package com.covet.profile.persistence.repository;

import com.covet.profile.persistence.model.Network;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.UUID;

public interface NetworkRepository extends JpaRepository<Network, UUID> {
    @Query(value = "select n.* from network n " +
            "join network_group ng on n.network_code = ng.network_code " +
            "where ng.group_id = :groupId", nativeQuery = true)
    List<Network> findByGroupId(@Param("groupId") String groupId);
}
