package com.covet.profile.persistence.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.BatchSize;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.*;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "specialty")
@FieldNameConstants
public class Specialty extends BaseEntity<UUID> {
    @Id
    @GeneratedValue
    @Column(name = "id")
    private UUID id;

    @Column(name = "specialty")
    @NotNull
    private String name;
}
