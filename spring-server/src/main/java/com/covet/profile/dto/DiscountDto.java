package com.covet.profile.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.util.Date;
import java.util.Optional;
import java.util.UUID;

@JsonSerialize(as = ImmutableDiscountDto.class)
@JsonDeserialize(as = ImmutableDiscountDto.class, builder = ImmutableDiscountDto.Builder.class)
@Value.Immutable
public interface DiscountDto {
    @Value.Parameter
    @JsonProperty
    Optional<UUID> getDiscountId();

    @Value.Parameter
    @JsonProperty
    UUID getMethodId();

    @Value.Parameter
    @JsonProperty
    String getDiscountType();

    @Value.Parameter
    @JsonProperty
    String getDiscountCode();

    @Value.Parameter
    @JsonProperty
    Date getCreatedAt();

    @Value.Parameter
    @JsonProperty
    Date getExpiredAt();

}
