create table if not exists scheduler (
                        id uuid not null,
                        start_date timestamp,
                        end_date timestamp,
                        day_of_weeks varchar(256),
                        primary key (id)
                    );
create table if not exists notification_template (
                        id uuid not null,
                        template_type varchar(32),
                        route varchar(128) not null,
                        notification_info varchar,
                        primary key (id)
                    );
create table if not exists notification (
                        id uuid not null,
                        cognito_id uuid not null,
                        template_id uuid not null,
                        scheduler_id uuid not null,
                        start_time time not null,
                        is_pushed boolean default false,
                        is_read boolean default false,
                        is_active boolean default true,
                        constraint fk_notification_scheduler foreign key (scheduler_id) references scheduler,
                        constraint fk_notification_template foreign key (template_id) references notification_template,
                        constraint fk_notification_profile foreign key (cognito_id) references profile,
                        primary key (id)
                    );