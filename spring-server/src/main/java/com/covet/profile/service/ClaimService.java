package com.covet.profile.service;

import com.covet.profile.converter.ClaimConverter;
import com.covet.profile.dto.covet.claim.ClaimDto;
import com.covet.profile.exception.ResourceNotFoundException;
import com.covet.profile.persistence.model.Claim;
import com.covet.profile.persistence.repository.ClaimRepository;
import com.covet.profile.persistence.repository.PhysicianRepository;
import com.covet.profile.searchCriteria.ClaimSearchCriteria;
import com.covet.profile.persistence.specification.ClaimSpec;
import com.covet.profile.systemEnum.SysEnum;
import com.covet.profile.clients.vba.VBADtos.Claim.VBAClaimDetailDto;
import com.covet.profile.clients.vba.VBADtos.Claim.VBAClaimDto;
import com.covet.profile.clients.vba.VBADtos.Providers.VBAProviderDto;
import com.covet.profile.clients.vba.VBAInterfaces.IVBAClaimService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.AuthorizationServiceException;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Stream;

@Service
public class ClaimService {

    private final ClaimRepository claimRepository;

    private final PhysicianRepository physicianRepository;

    private final IVBAClaimService vbaClaimService;

    @Autowired
    ClaimService(ClaimRepository claimRepository, PhysicianRepository physicianRepository, IVBAClaimService vbaClaimService) {
        this.claimRepository = claimRepository;
        this.physicianRepository = physicianRepository;
        this.vbaClaimService = vbaClaimService;
    }


    public Page<ClaimDto> getAllClaims(UUID cognitoId) {
        boolean isPhysician = physicianRepository.existsById(cognitoId);
        Specification<Claim> conditions;
        if (isPhysician) {
            conditions = ClaimSpec.hasUserRoleIsPhysician(cognitoId);
        } else {
            conditions = Specification
                    .where(ClaimSpec.hasUserRoleIsPatient(cognitoId).or(ClaimSpec.hasUserRoleIsPhysician(cognitoId)));
        }

        return claimRepository.findAll(conditions, Pageable.unpaged()).map(claim -> {
            var physicianProfile = physicianRepository.findByPhysicianId(claim.getPhysicianId());
            if (physicianProfile.isPresent()) {
                return ClaimConverter.claimToDto(claim, physicianProfile.get().getProfile());
            }
            return null;
        });
    }

    public void deleteClaimById(String claimCode) {
        claimRepository.deleteById(claimCode);
    }

    public ClaimDto getClaim(String claimCode, UUID physicianId) {
        return claimRepository.findById(claimCode).map(claim -> {
            if (claim.getPhysicianId().equals(physicianId)) {
                var physicianProfile = physicianRepository.findByPhysicianId(claim.getPhysicianId());
                if (physicianProfile.isPresent()) {
                    return ClaimConverter.claimToDto(claim, physicianProfile.get().getProfile());
                }
            }
            throw new AuthorizationServiceException("Unauthorized");
        }).orElseThrow(() -> new ResourceNotFoundException("Claim not found " + claimCode));
    }

    public Page<ClaimDto> getClaimsWithFilter(int page, int pageSize, UUID cognitoId, ClaimSearchCriteria criteria) {
        Specification<Claim> conditions;
        var keyword = criteria.getKeyword();
        if (keyword.isPresent()) {
            // search and filter at the same time
            conditions = ClaimSpec.hasFilterCriteriaIn(criteria)
                    .and(ClaimSpec.hasClaimIdLike(keyword.get())
                            .or(ClaimSpec.hasPhysicianFirstNameLike(keyword.get()))
                            .or(ClaimSpec.hasPhysicianMiddleNameLike(keyword.get()))
                            .or(ClaimSpec.hasPhysicianLastNameLike(keyword.get())))
                    .and(ClaimSpec.hasUserRoleIsPhysician(cognitoId).or(ClaimSpec.hasUserRoleIsPatient(cognitoId)));
        } else {
            conditions = ClaimSpec.hasFilterCriteriaIn(criteria)
                    .and(ClaimSpec.hasUserRoleIsPhysician(cognitoId).or(ClaimSpec.hasUserRoleIsPatient(cognitoId)));
        }

        return claimRepository.findAll(conditions, PageRequest.of(page, pageSize)).map(claim -> {
                var physicianProfile = physicianRepository.findByPhysicianId(claim.getPhysicianId()).get();
                return ClaimConverter.claimToDto(claim, physicianProfile.getProfile());
        });
    }
    // End section

    //CH-121
    public List<VBAClaimDetailDto> getClaimDetailList(VBAClaimDto claim, List<VBAProviderDto> physicianList, UUID patientId) {
        var claimDetails = vbaClaimService.getVbaClaimDetailList(claim.getClaimNumber());

        List<VBAClaimDetailDto> results = new ArrayList<>();
        if (!claimDetails.isEmpty()) {
            results = claimDetails.stream().map(claimDetail -> {

                //temporary handle for test data - claim status code shouldn't be null
                if (claimDetail.getStatusCode() == null) {
                    claimDetail.setStatusCode("A");
                }
                //end

                Optional<VBAProviderDto> physician = physicianList.stream().filter(item -> item.getProviderId().equals(claim.getProviderId())).findFirst();
                String physicianName = physician.isPresent()
                        ? physician.get().getProviderName()
                        : "";
                boolean isPaid = claimDetail.getStatusCode().equals(SysEnum.EClaimStatus.Paid.getValue());
                List<BigDecimal> values = List.of(claimDetail.getCoPayAmt(), claimDetail.getDeductibleAmt(), claimDetail.getCoInsAmt(), claimDetail.getNotCoveredAmt());
                BigDecimal totalPrice = values.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                return ClaimConverter.vbaClaimDetailToDto(claimDetail, patientId, claim.getProviderId(), physicianName, isPaid, claim.getClaimType(), totalPrice);
            }).toList();
        }

        return results;
    }


    //CH-121
    public List<VBAClaimDetailDto> filterClaimDetailList(List<VBAClaimDetailDto> results, ClaimSearchCriteria criteria) {
        if (hasCriteria(criteria)) {
            Stream<VBAClaimDetailDto> filteredStream = results.stream();
            var claimType = criteria.getClaimType();

            if (criteria.getKeyword().isPresent()) {
                filteredStream = filteredStream.filter(claimDetailDto -> matchesKeyword(claimDetailDto, criteria.getKeyword().get()));
            }

            if (claimType.isPresent()) {
                List<String> valuesToFilter = getClaimTypesToFilter(claimType.get());
                filteredStream = filteredStream.filter(claimDetailDto -> valuesToFilter.contains(claimDetailDto.getClaimType()));
            }

            if (criteria.getIsPaid().isPresent()) {
                filteredStream = filteredStream.filter(claimDetailDto -> claimDetailDto.getIsPaid().equals(criteria.getIsPaid().get()));
            }

            if (criteria.getPrice().isPresent()) {
                filteredStream = filteredStream.filter(claimDetailDto -> claimDetailDto.getClaimTotalPrice().compareTo(BigDecimal.valueOf(criteria.getPrice().get())) <= 0);
            }

            if (criteria.getFromDate().isPresent()) {
                filteredStream = filteredStream.filter(claimDetailDto -> claimDetailDto.getServiceDate().after(criteria.getFromDate().get()) || claimDetailDto.getServiceDate().equals(criteria.getFromDate().get()));
            }

            if (criteria.getToDate().isPresent()) {
                filteredStream = filteredStream.filter(claimDetailDto -> claimDetailDto.getServiceDate().before(criteria.getToDate().get()) || claimDetailDto.getServiceDate().equals(criteria.getToDate().get()));
            }

            return filteredStream.toList();
        } else {
            return results;
        }
    }

    private List<String> getClaimTypesToFilter(Set<String> claimTypes) {
        List<String> result = new ArrayList<>();

        for ( String claimType:
             claimTypes) {
            if (claimType.equalsIgnoreCase(SysEnum.EGroupClaimType.MEDICAL.getValue())) {
                result.addAll(List.of(SysEnum.EVbaClaimType.Professional.getValue(), SysEnum.EVbaClaimType.Institutional.getValue()));
            } else if (claimType.equalsIgnoreCase(SysEnum.EGroupClaimType.PHARMACY.getValue())) {
                result.addAll(List.of(SysEnum.EVbaClaimType.Pharmacy.getValue()));
            } else if (claimType.equalsIgnoreCase(SysEnum.EGroupClaimType.OTHER.getValue())) {
                result.addAll(List.of(  SysEnum.EVbaClaimType.Dental.getValue(),
                                        SysEnum.EVbaClaimType.Flex.getValue(),
                                        SysEnum.EVbaClaimType.Life.getValue(),
                                        SysEnum.EVbaClaimType.Disability.getValue(),
                                        SysEnum.EVbaClaimType.Vision.getValue()));
            }
        }

        return result;
    }

    //CH-121
    public List<VBAClaimDto> getClaimsBySubscriber(String subscriberID, Optional<String> memberSeq) {
        var vbaClaims = vbaClaimService.getVbaClaimsBySubscriber(subscriberID, memberSeq);
        return vbaClaims.stream().map(ClaimConverter::vbaClaimToDto).toList();
    }

    public List<VBAClaimDto> getClaimsBySubscriberAndMemberSequences(String subscriberID, List<String> memberSequences) {
        var vbaClaims = vbaClaimService.getVbaClaimsBySubscriberAndMemberSequences(subscriberID, memberSequences);
        return vbaClaims.stream().map(ClaimConverter::vbaClaimToDto).toList();
    }

    private boolean matchesKeyword(VBAClaimDetailDto claim, String keyword) {
        String keywordLowerCase = keyword.toLowerCase();
        return Long.toString(claim.getClaimId()).contains(keywordLowerCase) || claim.getPhysicianName().toLowerCase().contains(keywordLowerCase);
    }

    public boolean hasCriteria(ClaimSearchCriteria criteria) {
        return criteria.getKeyword().isPresent() ||
                criteria.getClaimType().isPresent() ||
                criteria.getIsPaid().isPresent() ||
                criteria.getPrice().isPresent() ||
                criteria.getFromDate().isPresent() ||
                criteria.getToDate().isPresent();
    }
}
