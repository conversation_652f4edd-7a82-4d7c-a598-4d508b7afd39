package com.covet.profile.FactoryMethod;

import com.covet.profile.FactoryMethod.ParseNotiTemplate.IParseNotiTemplate;
import com.covet.profile.systemEnum.ETemplateType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.EnumMap;
import java.util.Set;


@Service
public class ParseNotiTemplateFactoryMethod {
    private final EnumMap<ETemplateType, IParseNotiTemplate> methodEnumMap;

    @Autowired
    public ParseNotiTemplateFactoryMethod(Set<IParseNotiTemplate> methods) {
        methodEnumMap = new EnumMap<>(ETemplateType.class);
        for (var method : methods) {
            this.methodEnumMap.put(method.getTemplateType(), method);
        }
    }

    public IParseNotiTemplate getMethod(ETemplateType type) {
        return methodEnumMap.get(type);
    }
}
