
package com.covet.profile.converter;

import java.util.Optional;

import com.covet.profile.dto.covet.claim.ClaimPrescriptionDto;
import com.covet.profile.dto.covet.claim.ImmutableClaimPrescriptionDto;
import com.covet.profile.persistence.model.ClaimPrescription;

public class ClaimPrescriptionConvert {
    private ClaimPrescriptionConvert() {
    }

    public static ClaimPrescriptionDto claimPrescriptionToDto(ClaimPrescription claimPrescription) {
        return ImmutableClaimPrescriptionDto.of(
                Optional.ofNullable(claimPrescription.getClaimPrescriptionId()));
    }
}
