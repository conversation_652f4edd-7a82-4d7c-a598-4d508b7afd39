package com.covet.profile.clients.vba.VBAInterfaces;

import com.covet.profile.clients.vba.VBAModels.Claim.VBAClaim;
import com.covet.profile.clients.vba.VBAModels.Claim.VBAClaimDetail;

import java.util.List;
import java.util.Optional;

public interface IVBAClaimService {
    List<VBAClaimDetail> getVbaClaimDetailList(long claimNumber);
    List<VBAClaim> getVbaClaimsBySubscriber(String subscriberID, Optional<String> memberSeq);
    List<VBAClaim> getVbaClaimsBySubscriberAndMemberSequences(String subscriberID, List<String> memberSeqs);
}
