create table profile  (
                        cognito_id uuid not null,
                        first_name varchar (256) not null,
                        middle_name varchar (256),
                        last_name varchar (256) not null,
                        created_date timestamp,
                        updated_date timestamp,
                        social_security_number varchar (256),
                        date_of_birth timestamp,
                        address varchar (256),
                        address_line varchar (256),
                        city varchar (256),
                        administrative_area varchar (256),
                        zip_code varchar (256),


                        primary key (cognito_id)
                      );

create table picture  (
                          cognito_id uuid not null,
                          profile_image bytea,
                          created_date timestamp,
                          updated_date timestamp,
                          primary key (cognito_id)
                      );