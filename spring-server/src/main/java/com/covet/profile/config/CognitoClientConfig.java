package com.covet.profile.config;

import software.amazon.awssdk.services.cognitoidentityprovider.CognitoIdentityProviderAsyncClient;
import software.amazon.awssdk.services.cognitoidentityprovider.CognitoIdentityProviderClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;

@Configuration
public class CognitoClientConfig {
    static Region region = Region.US_EAST_1;

    @Bean
    public CognitoIdentityProviderClient createCognitoClient() {
        CognitoIdentityProviderClient cognitoClient = CognitoIdentityProviderClient.builder()
                .region(region)
                .credentialsProvider(
                        DefaultCredentialsProvider.create())
                .build();
        return cognitoClient;
    }

    @Bean
    public CognitoIdentityProviderAsyncClient createCognitoAsyncClient() {
        CognitoIdentityProviderAsyncClient cognitoAsyncClient = CognitoIdentityProviderAsyncClient.builder().build();
        return cognitoAsyncClient;
    }
}
