package com.covet.profile.FactoryMethod.GetFavorite;

import com.covet.profile.converter.GroupProviderConverter;
import com.covet.profile.dto.covet.provider.GroupProviderDto;
import com.covet.profile.persistence.model.GroupProvider;
import com.covet.profile.persistence.repository.GroupProviderRepository;
import com.covet.profile.persistence.repository.PatientFavoriteRepository;
import com.covet.profile.service.NpiService;
import com.covet.profile.service.ProfileService;
import com.covet.profile.systemEnum.EFavoriteType;
import com.covet.profile.utils.AuthUtils;
import com.covet.profile.utils.CalculateUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@RequiredArgsConstructor
public class GetGroupProviderFavorite implements IGetFavoriteMethod<GetFavoriteRequest, Page<GroupProviderDto>> {
    private final PatientFavoriteRepository patientFavoriteRepository;
    private final GroupProviderRepository groupProviderRepository;
    private final NpiService npiService;
    private final ProfileService profileService;

    @Override
    public Page<GroupProviderDto> getFavorite(GetFavoriteRequest request) {
        Page<UUID> favoriteIds = patientFavoriteRepository.findPatientFavoritesPagination(
                AuthUtils.getCognitoId(), getFavoriteType().getValue(), PageRequest.of(request.pageNumber, request.pageSize)
        );

        Double lat;
        Double lng;
        if (Objects.nonNull(request.getLatitude()) &&
                Objects.nonNull(request.getLongitude())) {
            lat = Double.valueOf(request.getLatitude().trim());
            lng = Double.valueOf(request.getLatitude().trim());
        } else {
            var me = profileService.fetchMeCoordinate();
            lat = me.getLatitude();
            lng = me.getLongitude();
        }

        var groups = groupProviderRepository.findAllById(favoriteIds);
        List<GroupProviderDto> DTOs = new LinkedList<>();
        List<String> npiList = groups.stream().map(GroupProvider::getNpi).toList();
        var npiInfoList = npiService.getNpiInfoList(npiList);
        for (int i = 0; i < npiInfoList.size(); i++) {
            DTOs.add(GroupProviderConverter.groupProviderToDto(groups.get(i),
                    Optional.ofNullable(npiInfoList.get(i)),
                    CalculateUtils.calculateDistance(groups.get(i).getLatitude(), groups.get(i).getLongitude(), lat, lng),
                    true));
        }
        return new PageImpl<>(DTOs, favoriteIds.getPageable(), favoriteIds.getTotalElements());
    }

    @Override
    public EFavoriteType getFavoriteType() {
        return EFavoriteType.GROUP_PROVIDER;
    }
}
