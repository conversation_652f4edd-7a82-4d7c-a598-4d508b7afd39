package com.covet.profile.clients.vba.VBADtos.IDcard.Copays;

import java.math.BigDecimal;

import org.immutables.value.Value;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;


@JsonSerialize(as = ImmutableVBACopayDto.class)
@JsonDeserialize(as = ImmutableVBACopayDto.class, builder = ImmutableVBACopayDto.Builder.class)
@Value.Immutable
public interface VBACopayDto {
    @Value.Parameter
    @JsonProperty
    BigDecimal getPCP();

    @Value.Parameter
    @JsonProperty
    BigDecimal getUrgentCare();

    @Value.Parameter
    @JsonProperty
    BigDecimal getSpecialist();

    @Value.Parameter
    @JsonProperty
    BigDecimal getEmergencyRoom();
}
