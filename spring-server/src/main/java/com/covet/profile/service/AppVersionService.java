package com.covet.profile.service;

import java.util.Optional;

import org.springframework.stereotype.Service;

import com.covet.profile.persistence.model.AppVersion;
import com.covet.profile.persistence.repository.AppVersionRepository;

@Service
public class AppVersionService {
    
    private final AppVersionRepository repository;

    public AppVersionService(AppVersionRepository repository) {
        this.repository = repository;
    }

    public AppVersion saveVersion(AppVersion appVersion) {
        return repository.save(appVersion);
    }

    public Optional<AppVersion> getLatestVersion() {
        return repository.findLatestVersion();
    }
}
