package com.covet.profile.clients.cigna.response.location;

import com.covet.profile.clients.cigna.response.common.Address;
import com.covet.profile.clients.cigna.response.common.BaseResource;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class LocationResource extends BaseResource {
    private List<LocationExtension> extension;
    private Address address;
    private Position position;
    private String status;
    private String name;
}
