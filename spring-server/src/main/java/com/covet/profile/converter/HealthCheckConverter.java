package com.covet.profile.converter;

import com.covet.profile.dto.covet.health_check.HealthCheckDto;
import com.covet.profile.dto.covet.health_check.ImmutableHealthCheckDto;
import com.covet.profile.persistence.model.Claim;
import com.covet.profile.persistence.model.HealthCareMethod;
import com.covet.profile.persistence.model.HealthCheck;

public class HealthCheckConverter {
    private HealthCheckConverter() {
    }

    public static HealthCheckDto healthCheckToDto(HealthCheck healthCheck) {
        return ImmutableHealthCheckDto.of(
                healthCheck.getHealthCheckId());
    }

    public static HealthCheck dtoToHealthCheck(HealthCheckDto healthCheckDto, HealthCareMethod healthCareMethod,
            Claim claim) {
        return new HealthCheck(
                healthCheckDto.getHealthCheckId(), claim, healthCareMethod);
    }
}
