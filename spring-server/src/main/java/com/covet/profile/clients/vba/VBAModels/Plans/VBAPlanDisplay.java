package com.covet.profile.clients.vba.VBAModels.Plans;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class VBAPlanDisplay {
    @Column(name = "Plan_ID")
    private String planId;

    @Column(name = "Display_Type")
    private String displayType;

    @Column(name = "Display")
    private String display;
}