package com.covet.profile.dto.reminder;

import com.covet.profile.dto.PrescriptionSchedulerDto.DosageTimeDto;
import com.covet.profile.systemEnum.EDayOfWeek;
import com.covet.profile.systemEnum.ETemplateType;
import lombok.Data;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@Data
public abstract class BaseCreateReminderDto {
    @NotNull
    private LocalDate startDate;

    @NotNull
    private LocalDate endDate;

    @NotEmpty
    private List<EDayOfWeek> dayOfWeeks;

    @NotEmpty
    private List<DosageTimeDto> dosageTimes;

    private ETemplateType templateType;

    private String route;

    private String note;
}
