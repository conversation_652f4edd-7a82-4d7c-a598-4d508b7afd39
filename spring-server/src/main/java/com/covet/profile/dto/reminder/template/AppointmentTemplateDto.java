package com.covet.profile.dto.reminder.template;

import com.covet.profile.FactoryMethod.ParseNotiTemplate.BaseTemplateDto;
import com.covet.profile.dto.reminder.PhysicianProfileDto;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppointmentTemplateDto extends BaseTemplateDto implements Serializable {
    UUID id;
    UUID patientId;
    UUID physicianId;
    LocalDateTime startTime;
    LocalDateTime endTime;
    Boolean isVirtual;
    Integer appointmentType;
    String description;
    String status;
    String preVisit;
    String postVisit;
    String daysOfWeek;
    LocalDateTime appointmentDate;
    PhysicianProfileDto physicianProfile;
}
