package com.covet.profile.searchCriteria;

import java.util.Date;
import java.util.Optional;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.springframework.format.annotation.DateTimeFormat;

public class PriorAuthorCriteria {

    public Optional<String> keyword;

    @Temporal(TemporalType.TIMESTAMP)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    public Optional<Date> fromDate;

    @Temporal(TemporalType.TIMESTAMP)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    public Optional<Date> toDate;

    public Optional<String> getKeyword() {
        return keyword;
    }

    public void setKeyword(Optional<String> keyword) {
        this.keyword = keyword;
    }

    public Optional<Date> getFromDate() {
        return fromDate;
    }

    public void setFromDate(Optional<Date> fromDate) {
        this.fromDate = fromDate;
    }

    public Optional<Date> getToDate() {
        return toDate;
    }

    public void setToDate(Optional<Date> toDate) {
        this.toDate = toDate;
    }
}
