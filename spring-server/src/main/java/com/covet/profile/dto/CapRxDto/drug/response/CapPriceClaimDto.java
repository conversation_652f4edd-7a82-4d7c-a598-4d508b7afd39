package com.covet.profile.dto.CapRxDto.drug.response;

import com.covet.profile.dto.CapRxDto.claim.CapClaimItemDto;
import com.covet.profile.dto.CapRxDto.claim.CapClaimPricingItemDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

@JsonSerialize(as = ImmutableCapPriceClaimDto.class)
@JsonDeserialize(as = ImmutableCapPriceClaimDto.class, builder = ImmutableCapPriceClaimDto.Builder.class)
@Value.Immutable
public interface CapPriceClaimDto {

    @Value.Parameter
    @JsonProperty("request")
    CapClaimItemDto getRequest();

    @Value.Parameter
    @JsonProperty("response")
    CapClaimPricingItemDto getResponse();

    @Value.Parameter
    @JsonProperty("vendor_data")
    JsonNode getVenderData();
}
