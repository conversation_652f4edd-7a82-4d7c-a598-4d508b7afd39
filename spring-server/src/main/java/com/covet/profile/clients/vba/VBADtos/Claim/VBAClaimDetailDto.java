package com.covet.profile.clients.vba.VBADtos.Claim;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

@JsonSerialize(as = ImmutableVBAClaimDetailDto.class)
@JsonDeserialize(as = ImmutableVBAClaimDetailDto.class, builder = ImmutableVBAClaimDetailDto.Builder.class)
@Value.Immutable
public interface VBAClaimDetailDto {
    @Value.Parameter
    @JsonProperty
    long getClaimId(); // claim number

    @Value.Parameter
    @JsonProperty
    UUID getPatientId();

    @Value.Parameter
    @JsonProperty
    String getPhysicianId();

    @Value.Parameter
    @JsonProperty
    String getClaimStatus();

    @Value.Parameter
    @JsonProperty
    Date getServiceDate();

    @Value.Parameter
    @JsonProperty
    String getPhysicianName();

    @Value.Parameter
    @JsonProperty
    Boolean getIsPaid();

    @Value.Parameter
    @JsonProperty
    String getClaimType();

    @Value.Parameter
    @JsonProperty
    BigDecimal getClaimTotalPrice();
}
