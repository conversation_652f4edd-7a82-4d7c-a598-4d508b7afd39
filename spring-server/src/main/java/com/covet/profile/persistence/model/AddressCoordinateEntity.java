package com.covet.profile.persistence.model;

import lombok.Data;

import javax.persistence.*;
import java.util.UUID;

@Data
@Table(name = "address_coordinates")
@Entity
public class AddressCoordinateEntity {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long id;

    @Column(name = "address_id")
    Long addressId;

    @Column(name = "address_uuid")
    UUID addressUUID;

    @Column(name = "latitude")
    Double latitude;

    @Column(name = "longitude")
    Double longitude;
}
