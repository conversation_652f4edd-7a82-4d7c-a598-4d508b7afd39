package com.covet.profile.config;

import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;

import java.util.concurrent.TimeUnit;

public class CignaClientConfig {
    @Bean
    public OkHttpClient cignaHttpClient() {
        int numOfConnection = 10;
        int keepAliveDuration = 30;
        return new OkHttpClient().newBuilder().connectionPool(new ConnectionPool(numOfConnection,
                keepAliveDuration,
                TimeUnit.SECONDS)
        ).connectTimeout(2 * 60L, TimeUnit.SECONDS).build();
    }
}
