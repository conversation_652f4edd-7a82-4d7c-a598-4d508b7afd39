apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: my-ingress
  annotations:
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:123903216279:certificate/8695b8c3-4342-48e8-bed5-a9cd289c678d
spec:
  ingressClassName: alb
  rules:
  - host: ${HOST_URL}
    http:
      paths:
      - path: /graphql
        pathType: Prefix
        backend:
          service:
            name: covetql-server
            port:
              number: 4000
      - path: /
        pathType: Prefix
        backend:
          service:
            name: spring-server
            port:
              number: 5000
      