package com.covet.profile.clients.vba.VBAService;

import com.covet.profile.clients.vba.VBADtos.Subscriber.EnrollmentSubscriberDto;
import com.covet.profile.clients.vba.VBADtos.Member.VBAMemberDto;
import com.covet.profile.clients.vba.VBADtos.Providers.VBAProviderDto;
import com.covet.profile.clients.vba.VBADtos.Member.SubscriberMemberDto;
import com.covet.profile.clients.vba.VBAModels.EnrollmentSubscriber;
import com.covet.profile.clients.vba.VBAModels.Financial.VBAMemberAccum;
import com.covet.profile.clients.vba.VBAModels.VBAMember;
import com.covet.profile.clients.vba.VBAModels.VBAProvider;
import com.covet.profile.clients.vba.VBARepository.VBAUserRepository;
import com.covet.profile.clients.vba.VBARepository.VBAPlanRepository;
import com.covet.profile.dto.covet.profile.ProfileCheckDto;
import com.covet.profile.dto.VbaDto.ProfileCheckResultDto;
import com.covet.profile.dto.covet.profile.ProfileInfoDto;
import com.covet.profile.dto.ProviderProfileCheckDto;
import com.covet.profile.persistence.model.Physician;
import com.covet.profile.persistence.model.Profile;
import com.covet.profile.service.Auth0Service;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.server.ResponseStatusException;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("VBAUserService Tests")
class VBAUserServiceTest {

    @Mock
    private VBAUserRepository vbaUserRepository;

    @Mock
    private VBAPlanRepository vbaPlanRepository;

    @Mock
    private Auth0Service auth0Service;

    @Mock
    private ObjectMapper objMapper;

    @InjectMocks
    private VBAUserService vbaUserService;

    private static final String TEST_SUBSCRIBER_ID = "SUB123";
    private static final String TEST_MEMBER_SEQ = "01";
    private static final String TEST_EMAIL = "<EMAIL>";
    private static final String TEST_PROVIDER_ID = "PROV123";
    private static final String TEST_GROUP_ID = "GROUP123";

    private JsonNode testJsonData;
    private SubscriberMemberDto testSubscriberMemberDto;
    private VBAMember testVBAMember;
    private VBAProvider testVBAProvider;
    private EnrollmentSubscriber testEnrollmentSubscriber;

    @BeforeEach
    void setUp() {
        // Setup test data only when needed - removed to avoid unnecessary stubbing
        testJsonData = mock(JsonNode.class);
        testSubscriberMemberDto = createTestSubscriberMemberDto();
        testEnrollmentSubscriber = createTestEnrollmentSubscriber();
    }

    @Nested
    @DisplayName("Repository Layer Tests")
    class RepositoryLayerTests {

        @Test
        @DisplayName("Should get subscriber member info successfully")
        void getSubscriberMemberInfo_Success() {
            // Arrange
            when(vbaUserRepository.getSubscriberMemberInfo(TEST_EMAIL)).thenReturn(testSubscriberMemberDto);

            // Act
            SubscriberMemberDto result = vbaUserService.getSubscriberMemberInfo(TEST_EMAIL);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getSubscriberId()).isEqualTo(TEST_SUBSCRIBER_ID);
            assertThat(result.getMemberSequence()).isEqualTo(TEST_MEMBER_SEQ);
            verify(vbaUserRepository).getSubscriberMemberInfo(TEST_EMAIL);
        }

        @Test
        @DisplayName("Should get enrollment subscriber successfully")
        void getEnrollmentSubscriber_Success() {
            // Arrange
            when(vbaUserRepository.getEnrollmentSubscriber(TEST_SUBSCRIBER_ID)).thenReturn(testEnrollmentSubscriber);

            // Act
            EnrollmentSubscriberDto result = vbaUserService.getEnrollmentSubscriber(TEST_SUBSCRIBER_ID);

            // Assert
            assertThat(result).isNotNull();
            verify(vbaUserRepository).getEnrollmentSubscriber(TEST_SUBSCRIBER_ID);
        }

        @Test
        @DisplayName("Should throw exception when enrollment subscriber not found")
        void getEnrollmentSubscriber_NotFound() {
            // Arrange
            when(vbaUserRepository.getEnrollmentSubscriber(TEST_SUBSCRIBER_ID)).thenReturn(null);

            // Act & Assert
            ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> {
                vbaUserService.getEnrollmentSubscriber(TEST_SUBSCRIBER_ID);
            });
            assertThat(exception.getStatus().value()).isEqualTo(404);
            assertThat(exception.getReason()).contains("Enrollment subscriber not found");
        }

        @Test
        @DisplayName("Should check user profile successfully")
        void checkUserProfile_Success() {
            // Arrange
            ProfileCheckDto profileCheckDto = createTestProfileCheckDto();
            ProfileCheckResultDto expectedResult = createTestProfileCheckResultDto();
            when(vbaUserRepository.checkUser(profileCheckDto)).thenReturn(expectedResult);

            // Act
            ProfileCheckResultDto result = vbaUserService.checkUserProfile(profileCheckDto);

            // Assert
            assertThat(result).isEqualTo(expectedResult);
            verify(vbaUserRepository).checkUser(profileCheckDto);
        }

        @Test
        @DisplayName("Should check provider profiles successfully")
        void checkProviderProfiles_Success() {
            // Arrange
            ProviderProfileCheckDto providerCheckDto = createTestProviderProfileCheckDto();
            String expectedClaimId = "CLAIM123";
            when(vbaUserRepository.checkProvider(providerCheckDto)).thenReturn(expectedClaimId);

            // Act
            String result = vbaUserService.checkProviderProfiles(providerCheckDto);

            // Assert
            assertThat(result).isEqualTo(expectedClaimId);
            verify(vbaUserRepository).checkProvider(providerCheckDto);
        }

        @Test
        @DisplayName("Should check for prescription search successfully")
        void checkForPrescriptionSearch_Success() {
            // Arrange
            when(vbaUserRepository.checkForPrescriptionSearch(TEST_GROUP_ID)).thenReturn(true);

            // Act
            boolean result = vbaUserService.checkForPrescriptionSearch(TEST_GROUP_ID);

            // Assert
            assertThat(result).isTrue();
            verify(vbaUserRepository).checkForPrescriptionSearch(TEST_GROUP_ID);
        }

        @Test
        @DisplayName("Should get profile by subscriber ID successfully")
        void getProfileBySubscriberId_Success() {
            // Arrange
            ProfileInfoDto expectedProfile = createTestProfileInfoDto();
            when(vbaUserRepository.getVBAProfile(TEST_SUBSCRIBER_ID)).thenReturn(expectedProfile);

            // Act
            ProfileInfoDto result = vbaUserService.getProfileBySubscriberId(TEST_SUBSCRIBER_ID);

            // Assert
            assertThat(result).isEqualTo(expectedProfile);
            verify(vbaUserRepository).getVBAProfile(TEST_SUBSCRIBER_ID);
        }

        @Test
        @DisplayName("Should get all providers successfully")
        void mappingProviders_Success() {
            // Arrange
            List<Physician> expectedProviders = Arrays.asList(new Physician(), new Physician());
            when(vbaUserRepository.getAllProviders()).thenReturn(expectedProviders);

            // Act
            List<Physician> result = vbaUserService.mappingProviders();

            // Assert
            assertThat(result).hasSize(2);
            assertThat(result).isEqualTo(expectedProviders);
            verify(vbaUserRepository).getAllProviders();
        }

        @Test
        @DisplayName("Should get all subscribers successfully")
        void mappingSubscribers_Success() {
            // Arrange
            List<Profile> expectedSubscribers = Arrays.asList(new Profile(), new Profile());
            when(vbaUserRepository.getAllSubscribers()).thenReturn(expectedSubscribers);

            // Act
            List<Profile> result = vbaUserService.mappingSubscribers();

            // Assert
            assertThat(result).hasSize(2);
            assertThat(result).isEqualTo(expectedSubscribers);
            verify(vbaUserRepository).getAllSubscribers();
        }
    }

    @Nested
    @DisplayName("Business Logic Tests")
    class BusinessLogicTests {

        @Test
        @DisplayName("Should get list member accum successfully")
        void getListMemberAccum_Success() throws ExecutionException, InterruptedException {
            // Arrange
            JsonNode dataNode = mock(JsonNode.class);
            JsonNode responseData = mock(JsonNode.class);
            when(vbaUserRepository.getListMemberAccum(TEST_SUBSCRIBER_ID, TEST_MEMBER_SEQ)).thenReturn(responseData);
            when(responseData.at("/data")).thenReturn(dataNode);
            when(dataNode.isMissingNode()).thenReturn(false);

            List<VBAMemberAccum> expectedAccums = Arrays.asList(new VBAMemberAccum(), new VBAMemberAccum());
            when(objMapper.convertValue(eq(dataNode), any(TypeReference.class))).thenReturn(expectedAccums);

            // Act
            CompletableFuture<List<VBAMemberAccum>> future = vbaUserService.getListMemberAccum(TEST_SUBSCRIBER_ID, TEST_MEMBER_SEQ);
            List<VBAMemberAccum> result = future.get();

            // Assert
            assertThat(result).hasSize(2);
            verify(vbaUserRepository).getListMemberAccum(TEST_SUBSCRIBER_ID, TEST_MEMBER_SEQ);
        }

        @Test
        @DisplayName("Should throw exception when member accum data node is missing")
        void getListMemberAccum_MissingDataNode() {
            // Arrange
            JsonNode missingNode = mock(JsonNode.class);
            JsonNode responseData = mock(JsonNode.class);
            when(vbaUserRepository.getListMemberAccum(TEST_SUBSCRIBER_ID, TEST_MEMBER_SEQ)).thenReturn(responseData);
            when(responseData.at("/data")).thenReturn(missingNode);
            when(missingNode.isMissingNode()).thenReturn(true);

            // Act & Assert
            CompletableFuture<List<VBAMemberAccum>> future = vbaUserService.getListMemberAccum(TEST_SUBSCRIBER_ID, TEST_MEMBER_SEQ);

            ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> {
                future.get();
            });
            assertThat(exception.getStatus().value()).isEqualTo(500);
            assertThat(exception.getReason()).contains("Can't get list member accums!");
        }

        @Test
        @DisplayName("Should filter members by age and relationship successfully")
        void geMemberListBySubscriberId_Success() {
            // Arrange
            JsonNode dataNode = mock(JsonNode.class);
            JsonNode responseData = mock(JsonNode.class);
            when(vbaUserRepository.getListMemberBySubscriberId(TEST_SUBSCRIBER_ID)).thenReturn(responseData);
            when(responseData.at("/data")).thenReturn(dataNode);
            when(dataNode.isMissingNode()).thenReturn(false);

            // Create test members: one primary (01), one adult dependent, one child
            List<VBAMember> memberList = createTestMemberList();
            when(objMapper.convertValue(eq(dataNode), any(TypeReference.class))).thenReturn(memberList);

            // Act
            List<VBAMemberDto> result = vbaUserService.geMemberListBySubscriberId(TEST_SUBSCRIBER_ID);

            // Assert
            assertThat(result).hasSize(2); // Should include primary member and child (under 18)
            verify(vbaUserRepository).getListMemberBySubscriberId(TEST_SUBSCRIBER_ID);
        }

        @Test
        @DisplayName("Should handle empty member list")
        void geMemberListBySubscriberId_EmptyList() {
            // Arrange
            JsonNode dataNode = mock(JsonNode.class);
            JsonNode responseData = mock(JsonNode.class);
            when(vbaUserRepository.getListMemberBySubscriberId(TEST_SUBSCRIBER_ID)).thenReturn(responseData);
            when(responseData.at("/data")).thenReturn(dataNode);
            when(dataNode.isMissingNode()).thenReturn(false);
            when(objMapper.convertValue(eq(dataNode), any(TypeReference.class))).thenReturn(Collections.emptyList());

            // Act
            List<VBAMemberDto> result = vbaUserService.geMemberListBySubscriberId(TEST_SUBSCRIBER_ID);

            // Assert
            assertThat(result).isEmpty();
        }

        @Test
        @DisplayName("Should throw exception when member list data node is missing")
        void geMemberListBySubscriberId_MissingDataNode() {
            // Arrange
            JsonNode missingNode = mock(JsonNode.class);
            JsonNode responseData = mock(JsonNode.class);
            when(vbaUserRepository.getListMemberBySubscriberId(TEST_SUBSCRIBER_ID)).thenReturn(responseData);
            when(responseData.at("/data")).thenReturn(missingNode);
            when(missingNode.isMissingNode()).thenReturn(true);

            // Act & Assert
            ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> {
                vbaUserService.geMemberListBySubscriberId(TEST_SUBSCRIBER_ID);
            });
            assertThat(exception.getStatus().value()).isEqualTo(500);
            assertThat(exception.getReason()).contains("Can't get list member for subscriber!");
        }

        @Test
        @DisplayName("Should filter out members with null birth date")
        void geMemberListBySubscriberId_NullBirthDate() {
            // Arrange
            JsonNode dataNode = mock(JsonNode.class);
            JsonNode responseData = mock(JsonNode.class);
            when(vbaUserRepository.getListMemberBySubscriberId(TEST_SUBSCRIBER_ID)).thenReturn(responseData);
            when(responseData.at("/data")).thenReturn(dataNode);
            when(dataNode.isMissingNode()).thenReturn(false);

            List<VBAMember> memberList = Arrays.asList(createMemberWithNullBirthDate());
            when(objMapper.convertValue(eq(dataNode), any(TypeReference.class))).thenReturn(memberList);

            // Act
            List<VBAMemberDto> result = vbaUserService.geMemberListBySubscriberId(TEST_SUBSCRIBER_ID);

            // Assert
            assertThat(result).isEmpty(); // Should filter out members with null birth date
        }

        @Test
        @DisplayName("Should return empty list for null provider list")
        void fetchVbaProviderList_NullList() {
            // Act
            List<VBAProviderDto> result = vbaUserService.fetchVbaProviderList(null);

            // Assert
            assertThat(result).isEmpty();
            verifyNoInteractions(vbaUserRepository);
        }

        @Test
        @DisplayName("Should return empty list for empty provider list")
        void fetchVbaProviderList_EmptyList() {
            // Act
            List<VBAProviderDto> result = vbaUserService.fetchVbaProviderList(Collections.emptyList());

            // Assert
            assertThat(result).isEmpty();
            verifyNoInteractions(vbaUserRepository);
        }

        @Test
        @DisplayName("Should fetch provider list successfully")
        void fetchVbaProviderList_Success() {
            // Arrange
            List<String> providerIds = Arrays.asList("PROV1", "PROV2");
            JsonNode providerData = mock(JsonNode.class);
            JsonNode responseData = mock(JsonNode.class);
            VBAProvider mockProvider = mock(VBAProvider.class);

            when(vbaUserRepository.getProviderById(anyString())).thenReturn(responseData);
            when(responseData.at("/data")).thenReturn(providerData);
            when(providerData.isMissingNode()).thenReturn(false);
            when(objMapper.convertValue(eq(providerData), any(TypeReference.class))).thenReturn(mockProvider);

            // Act
            List<VBAProviderDto> result = vbaUserService.fetchVbaProviderList(providerIds);

            // Assert
            assertThat(result).hasSize(2);
            verify(vbaUserRepository, times(2)).getProviderById(anyString());
        }

        @Test
        @DisplayName("Should handle provider fetch error gracefully")
        void fetchVbaProviderList_ProviderNotFound() {
            // Arrange
            List<String> providerIds = Arrays.asList("INVALID_PROV");
            JsonNode missingNode = mock(JsonNode.class);
            JsonNode responseData = mock(JsonNode.class);

            when(vbaUserRepository.getProviderById("INVALID_PROV")).thenReturn(responseData);
            when(responseData.at("/data")).thenReturn(missingNode);
            when(missingNode.isMissingNode()).thenReturn(true);

            // Act & Assert
            ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> {
                vbaUserService.fetchVbaProviderList(providerIds);
            });
            assertThat(exception.getStatus().value()).isEqualTo(404);
            assertThat(exception.getReason()).contains("Provider not found");
        }
    }

    // Helper methods for creating test data
    private JsonNode createTestJsonNode() {
        ObjectNode node = JsonNodeFactory.instance.objectNode();
        ObjectNode dataNode = JsonNodeFactory.instance.objectNode();
        node.set("data", dataNode);
        return node;
    }

    private JsonNode createTestMemberAccumData() {
        return JsonNodeFactory.instance.arrayNode();
    }

    private JsonNode createTestMemberListData() {
        return JsonNodeFactory.instance.arrayNode();
    }

    private JsonNode createTestProviderData() {
        return JsonNodeFactory.instance.objectNode();
    }

    private List<VBAMember> createTestMemberList() {
        List<VBAMember> members = new ArrayList<>();

        // Primary member (relationship "01")
        VBAMember primaryMember = mock(VBAMember.class);
        when(primaryMember.getMemberSeq()).thenReturn("01");
        when(primaryMember.getFirstName()).thenReturn("John");
        when(primaryMember.getLastName()).thenReturn("Doe");
        when(primaryMember.getRelationship()).thenReturn("01");
        when(primaryMember.getBirthDate()).thenReturn(new Date(System.currentTimeMillis() - (30L * 365 * 24 * 60 * 60 * 1000))); // 30 years old
        members.add(primaryMember);

        // Child member (under 18)
        VBAMember childMember = mock(VBAMember.class);
        when(childMember.getMemberSeq()).thenReturn("02");
        when(childMember.getFirstName()).thenReturn("Jane");
        when(childMember.getLastName()).thenReturn("Doe");
        when(childMember.getRelationship()).thenReturn("02");
        when(childMember.getBirthDate()).thenReturn(new Date(System.currentTimeMillis() - (10L * 365 * 24 * 60 * 60 * 1000))); // 10 years old
        members.add(childMember);

        // Adult dependent (over 18, not primary)
        VBAMember adultMember = mock(VBAMember.class);
        when(adultMember.getMemberSeq()).thenReturn("03");
        when(adultMember.getFirstName()).thenReturn("Bob");
        when(adultMember.getLastName()).thenReturn("Doe");
        when(adultMember.getRelationship()).thenReturn("03");
        when(adultMember.getBirthDate()).thenReturn(new Date(System.currentTimeMillis() - (25L * 365 * 24 * 60 * 60 * 1000))); // 25 years old
        members.add(adultMember);

        return members;
    }

    private VBAMember createMemberWithNullBirthDate() {
        VBAMember member = mock(VBAMember.class);
        when(member.getMemberSeq()).thenReturn("01");
        when(member.getFirstName()).thenReturn("John");
        when(member.getLastName()).thenReturn("Doe");
        when(member.getRelationship()).thenReturn("01");
        when(member.getBirthDate()).thenReturn(null); // Null birth date
        return member;
    }

    private SubscriberMemberDto createTestSubscriberMemberDto() {
        SubscriberMemberDto dto = new SubscriberMemberDto();
        dto.setSubscriberId(TEST_SUBSCRIBER_ID);
        dto.setMemberSequence(TEST_MEMBER_SEQ);
        return dto;
    }

    private VBAMember createTestVBAMember() {
        VBAMember member = mock(VBAMember.class);
        when(member.getMemberSeq()).thenReturn(TEST_MEMBER_SEQ);
        when(member.getFirstName()).thenReturn("John");
        when(member.getLastName()).thenReturn("Doe");
        when(member.getRelationship()).thenReturn("01");
        when(member.getBirthDate()).thenReturn(new Date());
        return member;
    }

    private VBAProvider createTestVBAProvider() {
        VBAProvider provider = mock(VBAProvider.class);
        when(provider.getProviderId()).thenReturn(TEST_PROVIDER_ID);
        when(provider.getOrgName()).thenReturn("Test Provider");
        return provider;
    }

    private EnrollmentSubscriber createTestEnrollmentSubscriber() {
        EnrollmentSubscriber subscriber = new EnrollmentSubscriber();
        subscriber.setSubscriberId(TEST_SUBSCRIBER_ID);
        subscriber.setPlanId("PLAN123");
        subscriber.setGroupId("GROUP123");
        return subscriber;
    }

    private ProfileCheckDto createTestProfileCheckDto() {
        ProfileCheckDto dto = new ProfileCheckDto();
        dto.setFirstName("John");
        dto.setLastName("Doe");
        dto.setSsn("123456789");
        return dto;
    }

    private ProfileCheckResultDto createTestProfileCheckResultDto() {
        ProfileCheckResultDto dto = new ProfileCheckResultDto();
        dto.setSubscriberID(TEST_SUBSCRIBER_ID);
        dto.setMemberSequence(TEST_MEMBER_SEQ);
        return dto;
    }

    private ProviderProfileCheckDto createTestProviderProfileCheckDto() {
        ProviderProfileCheckDto dto = new ProviderProfileCheckDto();
        dto.setClaimID("CLAIM123");
        dto.setTins("123456789");
        return dto;
    }

    private ProfileInfoDto createTestProfileInfoDto() {
        ProfileInfoDto dto = new ProfileInfoDto();
        dto.setFirstName("John");
        dto.setLastName("Doe");
        dto.setEmail(TEST_EMAIL);
        return dto;
    }
}
