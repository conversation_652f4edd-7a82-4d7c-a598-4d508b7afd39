package com.covet.profile.dto.CapRxDto.plan;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

@JsonSerialize(as = ImmutableCapZipCodeDto.class)
@JsonDeserialize(as = ImmutableCapZipCodeDto.class, builder = ImmutableCapZipCodeDto.Builder.class)
@Value.Immutable
public interface CapZipCodeDto {

    @Value.Parameter
    @JsonProperty("zip_code")
    String getZipCode();

}
