package com.covet.profile.service;

import com.covet.profile.clients.npi.response.NpiProvider;
import com.covet.profile.clients.vba.VBADtos.Member.SubscriberMemberDto;
import com.covet.profile.clients.vba.VBAService.VBAUserService;
import com.covet.profile.converter.*;
import com.covet.profile.data.ProviderAvailableTimes;
import com.covet.profile.dto.PriorAuthorDto;
import com.covet.profile.dto.ProviderDto.LocationDto;
import com.covet.profile.dto.VisitCostDto;
import com.covet.profile.dto.common.ErrorData;
import com.covet.profile.dto.common.ErrorDto;
import com.covet.profile.dto.covet.appointment.AppointmentDto;
import com.covet.profile.dto.covet.claim.ClaimDto;
import com.covet.profile.dto.covet.health_check.HealthCareMethodDto;
import com.covet.profile.dto.covet.health_check.HealthCheckDto;
import com.covet.profile.dto.covet.profile.ProfileDto;
import com.covet.profile.dto.covet.provider.PhysicianDetailDto;
import com.covet.profile.dto.covet.provider.PhysicianDto;
import com.covet.profile.dto.covet.provider.ProviderDetailDto;
import com.covet.profile.dto.covet.provider.ProviderFilterOptionsDto;
import com.covet.profile.exception.ResourceNotFoundException;
import com.covet.profile.persistence.compositeKey.HealthCheckID;
import com.covet.profile.persistence.model.*;
import com.covet.profile.persistence.repository.*;
import com.covet.profile.responses.ResponseHandler;
import com.covet.profile.searchCriteria.PhysicianProviderSearchCriteria;
import com.covet.profile.searchCriteria.PhysicianSearchCriteria;
import com.covet.profile.service.google.GoogleMapService;
import com.covet.profile.service.interfaces.IPhysicianService;
import com.covet.profile.systemEnum.EFavoriteType;
import com.covet.profile.systemEnum.ESortType;
import com.covet.profile.systemEnum.SysEnum;
import com.covet.profile.utils.AuthUtils;
import com.covet.profile.utils.FormatUtils;
import com.covet.profile.utils.RoundingUtils;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AuthorizationServiceException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.EntityNotFoundException;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.*;
import java.net.URL;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.covet.profile.Constant.AllNumbersGroup;
import static com.covet.profile.Constant.InNetworkOnlyGroup;

@Service
@Slf4j
@RequiredArgsConstructor
public class PhysicianService implements IPhysicianService {

    private final NetworkRepository networkRepository;
    @PersistenceContext
    private EntityManager entityManager;
    private final PhysicianRepository physicianRepository;

    private final AppointmentRepository appointmentRepository;
    private final ProfileService profileService;
    private final PriorAuthorService priorAuthorService;
    private final HealthCareMethodRepository healthCareMethodRepository;
    private final ClaimRepository claimRepository;
    private final VisitCostRepository visitCostRepository;
    private final HealthCheckRepository healthCheckRepository;
    private final ReviewRepository reviewRepository;
    private final PatientFavoriteRepository patientFavoriteRepository;
    private final InsertBatchStatusRepository insertBatchStatusRepository;
    private final PhysicianAddressRepository physicianAddressRepository;

    private final NpiService npiService;
    private final GoogleMapService googleMapService;
    private final CignaService cignaService;

    private final S3Service s3Service;
    private final String sourceProvider = "PROVIDER";

    private final Auth0Service auth0Service;
    private final VBAUserService vbaUserService;

    public Optional<PhysicianDto> getPhysicianById(UUID physicianId) {
        return physicianRepository.findById(physicianId)
                .map(physician -> PhysicianConverter.physicianToDto(physician, null, null));
    }

    public PhysicianDetailDto findPhysicianById(@NonNull UUID physicianId) {
        Optional<Physician> physician = physicianRepository.findById(physicianId);
        if (physician.isEmpty()) {
            throw new ResourceNotFoundException("Not found physician id: " + physicianId);
        }
        boolean isFavorite = patientFavoriteRepository.existPatientFavorite(AuthUtils.getCognitoId(), physicianId) > 0;
        Map<UUID, Set<Network>> networkMap = physicianRepository.fetchPhysicianNetworks(List.of(physicianId))
                .stream()
                .collect(Collectors.toMap(Physician::getPhysicianId, Physician::getNetworks));

        Map<UUID, List<Specialty>> specialtyMap = physicianRepository.fetchPhysicianSpecialties(List.of(physicianId))
                .stream()
                .collect(Collectors.toMap(Physician::getPhysicianId, Physician::getSpecialties));
        return ResponseHandler.generatePhysicianItem(physician.get(), getAvatarUrl(physicianId),
                Optional.empty(), 0.0, networkMap,
                specialtyMap, isFavorite, false, false);
    }

    public PhysicianDto createOrUpdatePhysician(PhysicianDto newPhysicianDto) {
        URL avatarUrl = null;
        Optional<UUID> physicianId = newPhysicianDto.getPhysicianId();
        if (physicianId.isPresent()) {
            avatarUrl = getAvatarUrl(physicianId.get());
        }
        return PhysicianConverter.physicianToDto(
                physicianRepository.save(PhysicianConverter.dtoToPhysician(newPhysicianDto)), avatarUrl, null);
    }

    public void deletePhysician(UUID physicianId) {
        physicianRepository.deleteById(physicianId);
    }

    public AppointmentDto createOrUpdateAppointment(AppointmentDto newAppointmentDto) throws EntityNotFoundException {
        Physician physician = entityManager.getReference(Physician.class, newAppointmentDto.getPhysicianId());
        Profile profile = entityManager.getReference(Profile.class, newAppointmentDto.getPatientId().orElseThrow(EntityNotFoundException::new));

        return AppointmentConverter.appointmentToDto(
                createOrUpdateAppointment(
                        AppointmentConverter.dtoToAppointment(newAppointmentDto, profile, physician)));
    }

    public Appointment createOrUpdateAppointmentV2(AppointmentDto newAppointmentDto) {
        Physician physician = entityManager.getReference(Physician.class, newAppointmentDto.getPhysicianId());
        Profile profile = entityManager.getReference(Profile.class, newAppointmentDto.getPatientId().orElseThrow(EntityNotFoundException::new));

        return createOrUpdateAppointment(
                        AppointmentConverter.dtoToAppointment(newAppointmentDto, profile, physician));
    }

    public Appointment createOrUpdateAppointment(Appointment updateAppointment) throws EntityNotFoundException {
        return appointmentRepository.save(updateAppointment);
    }

    public Set<AppointmentDto> getAppointmentsByPatient(UUID cognitoId) {
        return appointmentRepository.findByPatientId(cognitoId).stream()
                .map(AppointmentConverter::appointmentToDto)
                .collect(Collectors.toSet());
    }

    public Appointment getAppointment(UUID id, UUID patientId) {
        return appointmentRepository.findById(id).map(appointment -> {
            if (appointment.getPatientId().equals(patientId)) {
                return appointment;
            }
            throw new AuthorizationServiceException("Unauthorized");
        }).orElseThrow(() -> new ResourceNotFoundException("Appointment not found " + id));
    }

    public Appointment getAppointmentByPhysician(UUID id, UUID physicianId) {
        return appointmentRepository.findById(id).map(appointment -> {
            if (appointment.getPhysicianId().equals(physicianId)) {
                return appointment;
            }
            throw new AuthorizationServiceException("Unauthorized");
        }).orElseThrow(() -> new ResourceNotFoundException("Appointment not found " + id));
    }


    public List<PhysicianDetailDto> convertPhysiciansData(Double lat, Double lng,
            List<Physician> physicianList,
            List<NpiProvider> npiInfoList,
            List<UUID> favoriteIds,
            Map<UUID, URL> avatarMap,
            Map<UUID, Set<Network>> networkMap,
            Map<UUID, List<Specialty>> specialtyMap,
            Set<UUID> networkIds,
            boolean showInNetworkTag) {

        List<PhysicianDetailDto> physicianDto = new ArrayList<>();

        for (int i = 0; i < physicianList.size(); i++) {
            var physician = physicianList.get(i);
            var physicianId = physician.getPhysicianId();
            URL url = avatarMap.get(physicianId);
            boolean isInNetwork = Optional.ofNullable(networkMap.get(physicianId))
                    .orElse(new HashSet<>())
                    .stream()
                    .anyMatch(network -> networkIds.contains(network.getId()));
            
            physicianDto.add(ResponseHandler.generatePhysicianItem(physician,
                    url,
                    Optional.ofNullable(npiInfoList.get(i)),
                    physician.getProfile().calculateDistance(lat, lng),
                    networkMap,
                    specialtyMap,
                    favoriteIds.contains(physicianId), isInNetwork, showInNetworkTag
            ));
        }

        return physicianDto;
    }

    public Page<PhysicianDetailDto> getPhysicians(Optional<Integer> page, Optional<Integer> pageSize,
            PhysicianSearchCriteria criteria, LocationDto location) throws Exception {
        Double lat;
        Double lng;
        if (Objects.nonNull(location.getLatitude()) && Objects.nonNull(location.getLongitude())) {
            lat = Double.valueOf(location.getLatitude().trim());
            lng = Double.valueOf(location.getLongitude().trim());
        } else {
            var me = profileService.fetchMeCoordinate();
            lat = me.getLatitude();
            lng = me.getLongitude();
        }

        var pageRequest = PageRequest.of(page.orElse(SysEnum.EPaging.PageNumber.getValue()),
                pageSize.orElse(SysEnum.EPaging.PageSize.getValue()));
        Page<Physician> pagePhysicians = physicianRepository.findAllPhysicans(
                criteria.name,
                criteria.rating,
                criteria.specialty,
                pageRequest
        );
        List<UUID> physicianIds = pagePhysicians.map(Physician::getPhysicianId).toList();
        List<UUID> favoriteIds = patientFavoriteRepository.findOwnPatientFavorites(AuthUtils.getCognitoId(),
                EFavoriteType.PROVIDER.getValue(), physicianIds);
        List<String> npiList = pagePhysicians.map(Physician::getNpi).toList();
        var npiInfoList = npiService.getNpiInfoList(npiList);

        var physicianAvatarMap = s3Service.getAvatarMap(physicianIds);
        Map<UUID, Set<Network>> networkMap = physicianRepository.fetchPhysicianNetworks(physicianIds)
                .stream()
                .collect(Collectors.toMap(Physician::getPhysicianId, Physician::getNetworks));

        Map<UUID, List<Specialty>> specialtyMap = physicianRepository.fetchPhysicianSpecialties(physicianIds)
                .stream()
                .collect(Collectors.toMap(Physician::getPhysicianId, Physician::getSpecialties));
        List<PhysicianDetailDto> convertedPhysicians = convertPhysiciansData(
                lat,
                lng,
                pagePhysicians.getContent(),
                npiInfoList,
                favoriteIds,
                physicianAvatarMap,
                networkMap,
                specialtyMap,
                null, false
        );
        return new PageImpl<>(convertedPhysicians, pagePhysicians.getPageable(), pagePhysicians.getTotalElements());
    }

    public void deleteAppointment(UUID id) {
        appointmentRepository.deleteById(id);
    }

    public ClaimDto createOrUpdateClaim(ClaimDto newClaimDto) throws EntityNotFoundException {
        Physician physician = entityManager.getReference(Physician.class, newClaimDto.getPhysicianId());
        Profile profile = entityManager.getReference(Profile.class, newClaimDto.getPatientId());

        return ClaimConverter.claimToDto(
                createOrUpdateClaim(ClaimConverter.dtoToClaim(newClaimDto, profile, physician)),
                physician.getProfile());
    }

    public Claim createOrUpdateClaim(Claim updateClaim) throws EntityNotFoundException {
        return claimRepository.save(updateClaim);
    }

    public PriorAuthorDto createOrUpdatePriorAuthor(PriorAuthorDto priorAuthorDto) {
        // save to DB
        UUID physicianId = AuthUtils.getCognitoId();
        UUID patientId = priorAuthorDto.getPatientId();
        // check physician is existed in DB
        Optional<Physician> physician = physicianRepository.findById(physicianId);
        if (physician.isEmpty()) {
            throw new ResourceNotFoundException("Not found physician id: " + physicianId);
        }
        // check patient is existed in DB
        Optional<ProfileDto> patient = profileService.getProfileById(patientId);
        if (patient.isEmpty()) {
            throw new ResourceNotFoundException("Not found patient id: " + patientId);
        }
        Optional<ProfileDto> physicianProfileDto = profileService.getProfileById(physicianId);
        if (physicianProfileDto.isEmpty()) {
            throw new ResourceNotFoundException("Not found physician's info by id: " + physicianId);
        }
        Profile profile = entityManager.getReference(Profile.class, physicianId);
        String claimCode = priorAuthorDto.getClaimCode();

        Optional<Claim> claim = claimRepository.findById(claimCode);
        if (claim.isEmpty()) {
            throw new ResourceNotFoundException("Not found claim's info by code: " + claimCode);
        }
        PriorAuthor priorAuthor = PriorAuthorConverter.priorAuthorDtoToPriorAuthor(priorAuthorDto, physicianId,
                patientId, profile, claim.get());
        // save
        priorAuthorService.createOrUpdatePriorAuthor(priorAuthor);
        return priorAuthorDto;
    }

    public HealthCareMethodDto createOrUpdateHealthCareMethod(HealthCareMethodDto newHealthCareMethodDto) {
        return HealthCareMethodConverter.healthCareMethodToDto((healthCareMethodRepository
                .save(HealthCareMethodConverter.dtoToHealthCareMethod(newHealthCareMethodDto))));
    }

    public VisitCostDto createOrUpdateVisitCost(VisitCostDto newVisitCostDto) throws EntityNotFoundException {
        UUID physicianId = AuthUtils.getCognitoId();
        Physician physician = entityManager.getReference(Physician.class, physicianId);
        return VisitCostConverter.visitCostToDto((visitCostRepository
                .save(VisitCostConverter.dtoToVisitCost(newVisitCostDto, physician))));
    }

    public HealthCheckDto createOrUpdateHealthCheck(HealthCheckDto healthCheckDto)
            throws EntityNotFoundException {
        HealthCheckID healthCheckId = healthCheckDto.getHealthCheckId();
        String claimCode = healthCheckId.getClaimCode();
        UUID methodId = healthCheckId.getMethodId();
        Claim claim = entityManager.getReference(Claim.class, claimCode);
        HealthCareMethod healthCareMethod = entityManager.getReference(HealthCareMethod.class, methodId);

        return HealthCheckConverter.healthCheckToDto(healthCheckRepository
                .save(HealthCheckConverter.dtoToHealthCheck(healthCheckDto, healthCareMethod, claim)));
    }

    @Transactional
    public ProviderDetailDto getProviderDetail(UUID physicianId, String npi, LocationDto location) {
        CompletableFuture<NpiProvider> jsonFuture = CompletableFuture.supplyAsync(() -> npiService.getNpiInfo(npi));
        Double lat;
        Double lng;
        if (Objects.nonNull(location.getLatitude()) && Objects.nonNull(location.getLongitude())) {
            lat = Double.valueOf(location.getLatitude().trim());
            lng = Double.valueOf(location.getLongitude().trim());
        } else {
            var me = profileService.fetchMeCoordinate();
            lat = me.getLatitude();
            lng = me.getLongitude();
        }
        CompletableFuture<Map<String, Object>> getPhysicianFuture = CompletableFuture.supplyAsync(() -> {
            Map<String, Object> target = new HashMap<>();
            var cb = entityManager.getCriteriaBuilder();
            var cq = cb.createQuery(Physician.class);
            Root<Physician> root = cq.from(Physician.class);
            root.fetch(Physician.Fields.networks, JoinType.LEFT);
            root.fetch(Physician.Fields.specialties, JoinType.LEFT);
            root.fetch(Physician.Fields.profile);
            root.fetch(Physician.Fields.physicianRating);
            Physician physician = entityManager.createQuery(cq.select(root)
                    .where(cb.equal(root.get(Physician.Fields.physicianId), physicianId))).getSingleResult();
            ProviderAvailableTimes providerAvailableTimes = new ProviderAvailableTimes();
            List<LocalTime> availableTimes = providerAvailableTimes.getDefaultAvailableTimes();
            Review topReview = reviewRepository.findFirstByCreatedDate(physicianId);
            long totalReviews = reviewRepository.countByPhysicianId(physicianId);
            target.put("physician", physician);
            target.put("topReview", topReview);
            target.put("availableTimes", availableTimes);
            target.put("totalReviews", totalReviews);
            return target;
        });
        CompletableFuture<Object[]> combinedFuture = jsonFuture.thenCombine(getPhysicianFuture, (json, map) -> {
            Object[] resultArray = new Object[2];
            resultArray[0] = json;
            resultArray[1] = map;
            return resultArray;
        });

        Object[] combinedResult = combinedFuture.join();

        NpiProvider jsonResult = (NpiProvider) combinedResult[0];
        Map<String, Object> mapResult = (Map<String, Object>) combinedResult[1];

        Physician physician = (Physician) mapResult.get("physician");
        List<LocalTime> availableTimes = (List<LocalTime>) mapResult.get("availableTimes");
        Review topReview = (Review) mapResult.get("topReview");
        long totalReviews = (long) mapResult.get("totalReviews");
        boolean isFavorite = patientFavoriteRepository.existPatientFavorite(AuthUtils.getCognitoId(), physicianId) > 0;
        Map<UUID, Set<Network>> networkMap = Stream.of(physician)
                .collect(Collectors.toMap(Physician::getPhysicianId, Physician::getNetworks));

        Map<UUID, List<Specialty>> specialtyMap = Stream.of(physician)
                .collect(Collectors.toMap(Physician::getPhysicianId, Physician::getSpecialties));

        PhysicianDetailDto physicianDto = ResponseHandler.generatePhysicianItem(physician,
                getAvatarUrl(physicianId), Optional.ofNullable(jsonResult),
                RoundingUtils.roundDoubleValue(
                        physician.getProfile().calculateDistance(lat, lng)
                ),
                networkMap,
                specialtyMap,
                isFavorite, false, false);
        return ProviderDetailConverter.modelsToDto(physicianDto, availableTimes, topReview, totalReviews);
    }

    public Long countMatchedEntity(Double lat, Double lng, PhysicianProviderSearchCriteria criteria) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        Root<Physician> physician = cq.from(Physician.class);
        cq.select(cb.count(physician));
        buildPredicates(lat, lng, cb, cq, physician, criteria, true);
        return entityManager.createQuery(cq).getSingleResult();
    }

    public void buildPredicates(Double lat, Double lng, CriteriaBuilder cb, CriteriaQuery<?> cq, Root<Physician> root,
            PhysicianProviderSearchCriteria criteria, Boolean isCount) {
        List<Order> sorterList = new LinkedList<>();
        List<Predicate> predicateList = new LinkedList<>();

        // Fetch and join the profile in one go
        if (criteria.getKeyword() != null && !criteria.getKeyword().isEmpty()) {
            Join<Physician, Profile> physicianProfileJoin = root.join(Physician.Fields.profile);
            Predicate namePredicate = cb.or(
                    cb.like(cb.lower(physicianProfileJoin.get(Profile.Fields.firstName)), searchByKeywordPattern(criteria.getKeyword())),
                    cb.like(cb.lower(physicianProfileJoin.get(Profile.Fields.middleName)), searchByKeywordPattern(criteria.getKeyword())),
                    cb.like(cb.lower(physicianProfileJoin.get(Profile.Fields.lastName)), searchByKeywordPattern(criteria.getKeyword()))
            );

            Join<Physician, Specialty> specialtyJoinForKeyword = root.join(Physician.Fields.specialties, JoinType.LEFT);
            Predicate specialtyPredicate = cb.like(cb.lower(specialtyJoinForKeyword.get(Specialty.Fields.name)), searchByKeywordPattern(criteria.getKeyword()));

            predicateList.add(cb.or(namePredicate, specialtyPredicate));
        }

        if (criteria.getSpecialtyIds() != null && !criteria.getSpecialtyIds().isEmpty()) {
            var physicianSpecialtyJoin = root.join(Physician.Fields.specialties, JoinType.LEFT);
            predicateList.add(physicianSpecialtyJoin.get(Specialty.Fields.id).in(criteria.getSpecialtyIds()));
        }

        if (criteria.getNetworkIds() != null && !criteria.getNetworkIds().isEmpty()) {
            var physicianNetworkJoin = root.join(Physician.Fields.networks, JoinType.LEFT);
            predicateList.add(physicianNetworkJoin.get(Network.Fields.id).in(criteria.getNetworkIds()));
        }

        if (Objects.nonNull(criteria.getGroupId())) {
            Join<Physician, GroupPhysician> physiciansInGroup = root.join(Physician.Fields.groups);
            Predicate groupPredicate = cb.equal(physiciansInGroup.get("id").get("groupId"),
                    UUID.fromString(criteria.getGroupId()));
            predicateList.add(groupPredicate);
        }

        if (Objects.nonNull(criteria.getValue())) {
            ESortType sortType = Optional.ofNullable(criteria.getValueSortType()).orElse(ESortType.DESC);
            if (sortType.name().equals(ESortType.ASC.name())) {
                sorterList.add(cb.asc(root.get(Physician.Fields.value)));
            } else {
                sorterList.add(cb.desc(root.get(Physician.Fields.value)));
            }
            Predicate valuePredicate = cb.greaterThanOrEqualTo(root.get(Physician.Fields.value), criteria.getValue());
            predicateList.add(valuePredicate);
        }

        if (Objects.nonNull(criteria.getDistance())) {
            Join<Physician, Profile> profile = root.join(Physician.Fields.profile);
            Expression<Double> x_2 = cb.prod(
                    cb.diff(profile.get(Profile.Fields.latitude), lat),
                    cb.diff(profile.get(Profile.Fields.latitude), lat)
            );

            Expression<Double> y_2 = cb.prod(
                    cb.diff(profile.get(Profile.Fields.longitude), lng),
                    cb.diff(profile.get(Profile.Fields.longitude), lng)
            );
            ESortType sortType = Optional.ofNullable(criteria.getDistanceSortType()).orElse(ESortType.ASC);
            if (sortType.name().equals(ESortType.ASC.name())) {
                sorterList.add(cb.asc(cb.sum(x_2, y_2)));
            } else {
                sorterList.add(cb.desc(cb.sum(x_2, y_2)));
            }

            Predicate distancePredicate = cb.lessThanOrEqualTo(cb.sum(x_2, y_2), Math.pow(criteria.getDistance(), 2));
            predicateList.add(distancePredicate);
        }

        if (!isCount) {
            cq.orderBy(sorterList);
        }
        cq.where(predicateList.toArray(new Predicate[0]));
    }

    public List<UUID> getPhysicianIds(PhysicianProviderSearchCriteria criteria,
            Double lat, Double lng, Pageable pageable) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<UUID> cq = cb.createQuery(UUID.class);
        Root<Physician> physician = cq.from(Physician.class);

        buildPredicates(lat, lng, cb, cq, physician, criteria, false);
        return entityManager.createQuery(cq.select(physician.get(Physician.Fields.physicianId)))
                .setFirstResult((int) pageable.getOffset())
                .setMaxResults(pageable.getPageSize())
                .getResultList();
    }

    private List<Physician> getPhysicianInfo(List<UUID> phyIds) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Physician> cq = cb.createQuery(Physician.class);
        Root<Physician> phyRoot = cq.from(Physician.class);
        phyRoot.fetch(Physician.Fields.physicianRating);
        phyRoot.fetch(Physician.Fields.profile);
        phyRoot.fetch(Physician.Fields.specialties, JoinType.LEFT);
        phyRoot.fetch(Physician.Fields.networks, JoinType.LEFT);

        cq.where(phyRoot.get(Physician.Fields.physicianId).in(phyIds));
        return entityManager.createQuery(cq.select(phyRoot).distinct(true)).getResultList();
    }

    private String searchByKeywordPattern(String keyword) {
        return "%" + keyword.toLowerCase() + "%";
    }


    public ResponseEntity<?> getPhysicianProvidersWithFilter(Integer page, Integer pageSize,
            PhysicianProviderSearchCriteria criteria, LocationDto location) throws Exception {
        // validate request
        var groupId = criteria.getGroupId();
        if (Objects.nonNull(groupId) && !FormatUtils.isMatchUUID(groupId)) {
            var errorData = ErrorData.builder().errorMsg("GroupId must be UUID").build();
            var errorDto = new ErrorDto();
            errorDto.setErrors(List.of(errorData));
            errorDto.setStatusCode(-1);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorDto);
        }

        String keyword = criteria.getKeyword();
        List<PhysicianDetailDto> result = Collections.emptyList();
        String auth0ID = AuthUtils.getAuth0Id();
        SubscriberMemberDto subscriberID = auth0Service.getSubscriberMemberID(auth0ID);

        var subscriberGroup = vbaUserService.getEnrollmentSubscriber(subscriberID.getSubscriberId()).getGroupId();

        Double lat;
        Double lng;
        if (Objects.nonNull(location.getLatitude()) && Objects.nonNull(location.getLongitude())) {
            lat = Double.valueOf(location.getLatitude().trim());
            lng = Double.valueOf(location.getLongitude().trim());
        } else {
            var me = profileService.fetchMeCoordinate();
            lat = me.getLatitude();
            lng = me.getLongitude();
        }

        Pageable pageable = PageRequest.of(Optional.ofNullable(page).orElse(SysEnum.EPaging.PageNumber.getValue()),
                Optional.ofNullable(pageSize).orElse(SysEnum.EPaging.PageSize.getValue()));

        var networkIds = networkRepository.findByGroupId(subscriberGroup)
                .stream()
                .map(Network::getId)
                .collect(Collectors.toSet());
        boolean showInNetworkTag = false;
        /*If group is
            "MR": Show in network providers only
            "CG": Show all
            "All-number": Show all provider with in-network tag
        */
        if (subscriberGroup.toLowerCase().contains(InNetworkOnlyGroup)) {
            criteria.setNetworkIds(networkIds);
        } else if (subscriberGroup.matches(AllNumbersGroup)) {
            showInNetworkTag = true;
        }

        List<UUID> phyIds = getPhysicianIds(criteria, lat, lng, pageable);
        List<Physician> physicianList = getPhysicianInfo(phyIds);
        Map<UUID, Set<Network>> networkMap = physicianList
                .stream()
                .collect(Collectors.toMap(Physician::getPhysicianId, Physician::getNetworks));

        Map<UUID, List<Specialty>> specialtyMap = physicianList
                .stream()
                .collect(Collectors.toMap(Physician::getPhysicianId, Physician::getSpecialties));
        List<String> npiList = new LinkedList<>();
        List<UUID> physicianIds = new LinkedList<>();
        for (var phy : physicianList) {
            physicianIds.add(phy.getPhysicianId());
            npiList.add(phy.getNpi());
        }
        List<UUID> favoriteIds = patientFavoriteRepository.findOwnPatientFavorites(
                AuthUtils.getCognitoId(), EFavoriteType.PROVIDER.getValue(), physicianIds);

        var npiInfoList = npiService.getNpiInfoList(npiList);
        var physicianAvatarMap = s3Service.getAvatarMap(physicianIds);
        List<PhysicianDetailDto> convertedPhysicians = convertPhysiciansData(lat, lng,
                physicianList,
                npiInfoList, favoriteIds, physicianAvatarMap, networkMap,
                specialtyMap, networkIds, showInNetworkTag);

        var combinedResult = Stream.concat(convertedPhysicians.stream(), result.stream()).toList();

        return ResponseEntity
                .status(HttpStatus.OK)
                .body(new PageImpl<>(combinedResult, pageable, countMatchedEntity(lat, lng, criteria)));
    }


    public ProviderFilterOptionsDto getProviderFilterOptions() {
        Set<String> specialties = Arrays.stream(SysEnum.ESpecialty.values()).map(SysEnum.ESpecialty::getValue)
                .collect(Collectors.toSet());
        return ProviderFilterOptionsDto.builder()
                .specialtyList(specialties)
                .minValue(SysEnum.EFilterOptions.MinValue.getValue())
                .maxValue(SysEnum.EFilterOptions.MaxValue.getValue())
                .minDistance(SysEnum.EFilterOptions.MinDistance.getValue())
                .maxDistance(SysEnum.EFilterOptions.MaxDistance.getValue())
                .defaultRating(SysEnum.EFilterOptions.DefaultRating.getValue())
                .build();

    }

    /**
     * @param physicianId
     * @return avatarUrl from DB image if not expired; otherwise, return S3 presignedUrl
     */
    private URL getAvatarUrl(@NonNull UUID physicianId) {
        return s3Service.getPresignedURL(physicianId.toString());
    }

    @Transactional
    public void insertPhysicianAddress() {
        int page = 0;
        String sourcePhysicianAddress = "PHYSICIAN_ADDRESS";
        var maxPage = insertBatchStatusRepository.getMaxSuccessInsertBatch(sourcePhysicianAddress);
        if (maxPage.isPresent()) {
            page = maxPage.get() + 1;
        }
        log.info("Page number: {}", page);
        var startTime = System.currentTimeMillis();
        List<CompletableFuture<Void>> getAddressFutures = new LinkedList<>();
        Map<String, String> addressMap = new ConcurrentHashMap<>();
        Pageable pageRequest = PageRequest.of(page, 100);
        var data = physicianRepository.getIDNpiList(pageRequest);
        if (!data.isEmpty()) {
            log.info("Inserting page: {}...", page);
            getAddressFutures.add(CompletableFuture.supplyAsync(
                    () -> physicianRepository.getIDNpiList(pageRequest)
            ).thenAcceptAsync(result -> {
                for (var item : result) {
                    addressMap.computeIfAbsent(item.get(0), (__) ->
                            npiService.buildNpiAddressTemplate(npiService.getNpiInfo(item.get(1))));
                }
            }));
            CompletableFuture.allOf(getAddressFutures.toArray(new CompletableFuture[0])).join();
            List<PhysicianAddress> target = new LinkedList<>();
            addressMap.forEach((key, value) -> target.add(new PhysicianAddress(UUID.fromString(key), value)));

            physicianAddressRepository.saveAll(target);
            insertBatchStatusRepository.save(new InsertBatchStatus(UUID.randomUUID(), page, true, sourcePhysicianAddress));
            var endTime = System.currentTimeMillis();
            log.info("done page {} with duration time {}", page, endTime - startTime);
        }
        log.info("done at page {}", page);
    }

    public void getCoordinateWithCallback() {
        var startTime = System.currentTimeMillis();
        int page = 0;
        String sourcePhysicianCoordinates = "PHYSICIAN_COORDINATES";
        var maxPage = insertBatchStatusRepository.getMaxSuccessInsertBatch(sourcePhysicianCoordinates);
        if (maxPage.isPresent()) {
            page = maxPage.get() + 1;
        }
        log.info("Page number with address: {}", page);
        var pageable = PageRequest.of(page, 100);
        Page<PhysicianAddress> data = physicianAddressRepository.getNonNullAddressByPage(pageable);

        if (!data.getContent().isEmpty()) {
            log.info("Inserting page: {}...", page);
            var coordinatesMap = googleMapService.getCoordinatesTest(data.getContent());
            List<Profile> profileList = profileService.getProfilesByIds(
                    coordinatesMap.keySet().stream().toList()
            );
            log.info("profile size: {}", profileList.size());
            profileList.forEach(item -> {
                item.setLatitude(coordinatesMap.get(item.getCognitoId()).lat);
                item.setLongitude(coordinatesMap.get(item.getCognitoId()).lng);
            });

            profileService.saveManyProfiles(profileList);

            insertBatchStatusRepository.save(
                    new InsertBatchStatus(
                            UUID.randomUUID(),
                            page,
                            true,
                            sourcePhysicianCoordinates
                    )
            );
            var endTime = System.currentTimeMillis();
            log.info("Update done page {} with duration {}", page, endTime - startTime);
        } else {
            log.info("Already updated");
        }
    }

    public Optional<PhysicianDto> findPhysicianByNameAndNpi(String providerName, String npi) {
        return physicianRepository.findByNameAndNpi(providerName, npi)
                .map(physician -> PhysicianConverter.physicianToDto(physician, null, null));
    }
}