package com.covet.profile.clients.vba.VBAService;

import com.covet.profile.clients.vba.VBAModels.Claim.VBAClaim;
import com.covet.profile.clients.vba.VBAModels.Claim.VBAClaimDetail;
import com.covet.profile.persistence.model.Claim;
import com.covet.profile.clients.vba.VBAInterfaces.IVBAClaimService;
import com.covet.profile.clients.vba.VBARepository.VBAClaimRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class VBAClaimService implements IVBAClaimService {
    private final VBAClaimRepository vbaClaimRepository;

    private final RestTemplate vbaRestTemplate;

    private final String vbaApiUrl = "https://vbapi.vbasoftware.com/vbasoftware/";

    private final ObjectMapper objectMapper;

    @Autowired
    public VBAClaimService(VBAClaimRepository vbaClaimRepository, @Qualifier("vbaRestTemplate") RestTemplate vbaRestTemplate, ObjectMapper objectMapper) {
        this.vbaClaimRepository = vbaClaimRepository;
        this.vbaRestTemplate = vbaRestTemplate;
        this.objectMapper = objectMapper;
    }

    @Retryable(retryFor = SQLException.class, maxAttempts = 2, backoff = @Backoff(delay = 100))
    public List<Claim> getRecentClaims() {
        return vbaClaimRepository.findRecentClaims();
    }

    public List<VBAClaimDetail> getVbaClaimDetailList(long claimNumber) {
        String path = "claims/" + claimNumber + "/details";

        URI uri = UriComponentsBuilder
                .fromHttpUrl(vbaApiUrl)
                .path(path)
                .build()
                .toUri();

        List<VBAClaimDetail> claimDetails = new ArrayList<>();

        try {
            ResponseEntity<String> response = vbaRestTemplate.getForEntity(uri, String.class);
            JsonNode resultsNode = objectMapper.readTree(response.getBody()).get("data");
            if (resultsNode != null && resultsNode.isArray()) {
                ArrayNode arrayNode = (ArrayNode) resultsNode;

                for (JsonNode claimDetail : arrayNode) {
                    VBAClaimDetail claim = null;
                    try {
                        claim = objectMapper.treeToValue(claimDetail, VBAClaimDetail.class);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                    claimDetails.add(claim);
                }
            }
        } catch (Exception e) {
            // Handle specific exceptions here and rethrow a custom exception if needed
            throw new RuntimeException(e);
        }

        return claimDetails;
    }

    public List<VBAClaim> getVbaClaimsBySubscriber(String subscriberID, Optional<String> memberSeq) {
        return vbaClaimRepository.getClaimsBySubscriber(subscriberID, memberSeq);
    }

    public List<VBAClaim> getVbaClaimsBySubscriberAndMemberSequences(String subscriberID, List<String> memberSequences) {
        return vbaClaimRepository.getClaimsBySubscriberAndMemberSequences(subscriberID, memberSequences);
    }
}
