package com.covet.profile.FactoryMethod.GetFavorite;

import com.covet.profile.dto.ProviderDto.LocationDto;
import com.covet.profile.systemEnum.EFavoriteType;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.Objects;

@Builder
@Getter
@Setter
@Data
public class GetFavoriteRequest {

        @Builder.Default
        Integer pageNumber = 0;

        @Builder.Default
        Integer pageSize = 10;

        @NotNull
        EFavoriteType favoriteType;

        String latitude;

        String longitude;

}
