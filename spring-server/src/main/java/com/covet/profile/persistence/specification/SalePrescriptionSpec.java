package com.covet.profile.persistence.specification;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.criteria.Predicate;

import com.covet.profile.persistence.model.PrescriptionPharmacy;
import org.springframework.data.jpa.domain.Specification;

import com.covet.profile.persistence.compositeKey.PrescriptionID;

public class SalePrescriptionSpec {
    public static Specification<PrescriptionPharmacy> getPharmaciesByPrescriptionId(PrescriptionID prescriptionId) {
        return (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<Predicate>();
            Predicate prescriptionNamePredicate = builder.equal(root.get("id").get("prescriptionName"),
                    prescriptionId.getPrescriptionName());
            Predicate prescriptionCodePredicate = builder.equal(root.get("id").get("prescriptionCode"),
                    prescriptionId.getPrescriptionCode());
            predicates.add(prescriptionNamePredicate);
            predicates.add(prescriptionCodePredicate);
            return builder.and(predicates.toArray(new Predicate[predicates.size()]));
        };
    }
}
