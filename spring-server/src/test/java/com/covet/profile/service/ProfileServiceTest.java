package com.covet.profile.service;

import com.covet.profile.converter.ProfileConverter;
import com.covet.profile.converter.notification.PrescriptionSchedulerConverter;
import com.covet.profile.converter.notification.response.GetListMedicineNotificationResponse;
import com.covet.profile.dto.PrescriptionSchedulerDto.UpdateNotificationRequest;
import com.covet.profile.dto.covet.profile.ProfileDto;
import com.covet.profile.dto.common.PageRequestParams;
import com.covet.profile.persistence.model.Notification;
import com.covet.profile.persistence.model.Picture;
import com.covet.profile.persistence.model.Profile;
import com.covet.profile.persistence.model.RegistrationFCM;
import com.covet.profile.persistence.repository.NotificationRepository;
import com.covet.profile.persistence.repository.PictureRepository;
import com.covet.profile.persistence.repository.ProfileRepository;
import com.covet.profile.persistence.repository.RegistrationFcmRepository;
import com.covet.profile.systemEnum.ETemplateType;
import com.covet.profile.utils.AuthUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.test.util.ReflectionTestUtils;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import java.net.URL;
import java.time.LocalTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProfileServiceTest {

    @Mock
    private ProfileRepository profileRepository;

    @Mock
    private PictureRepository pictureRepository;

    @Mock
    private S3Service s3Service;

    @Mock
    private RegistrationFcmRepository registrationFcmRepository;

    @Mock
    private NotificationRepository notificationRepository;

    @Mock
    private PrescriptionSchedulerConverter prescriptionSchedulerConverter;

    @Mock
    private EntityManager em;

    @InjectMocks
    private ProfileService profileService;

    // Test Data
    private static final UUID TEST_PROFILE_ID = UUID.randomUUID();
    private Profile testProfile;
    private ProfileDto testProfileDto;
    private Picture testPicture;
    private MultipartFile testFile;
    private URL testUrl;

    @BeforeEach
    void setUp() throws Exception {
        // Initialize test profile
        testProfile = new Profile();
        testProfile.setCognitoId(TEST_PROFILE_ID);
        testProfile.setFirstName("John");
        testProfile.setMiddleName("M");
        testProfile.setLastName("Doe");
        testProfile.setSocialSecurityNumber("123456789");
        testProfile.setAddress("123 Test St, Test City, CA 90210");
        testProfile.setLatitude(34.0522);
        testProfile.setLongitude(-118.2437);

        testProfileDto = ProfileConverter.profileToProfileDto(testProfile, null);

        // Initialize test picture
        testPicture = new Picture();
        testPicture.setCognitoId(TEST_PROFILE_ID);
        testPicture.setImageURL(new URL("https://test-bucket.s3.amazonaws.com/test-image.jpg"));

        // Initialize test file
        testFile = new MockMultipartFile(
                "test-image.jpg",
                "test-image.jpg",
                "image/jpeg",
                "test image content".getBytes()
        );

        testUrl = new URL("https://test-bucket.s3.amazonaws.com/test-image.jpg");
        
        // Set EntityManager in ProfileService
        ReflectionTestUtils.setField(profileService, "em", em);
    }

    @Nested
    @DisplayName("Profile CRUD Operations")
    class ProfileCrudTests {
        
        @Test
        @DisplayName("Should return empty when profile not found")
        void getProfileById_WhenNotFound_ReturnsEmpty() {
            // Arrange
            when(profileRepository.findById(TEST_PROFILE_ID)).thenReturn(Optional.empty());

            // Act
            Optional<ProfileDto> result = profileService.getProfileById(TEST_PROFILE_ID);

            // Assert
            assertThat(result).isEmpty();
            verify(profileRepository).findById(TEST_PROFILE_ID);
        }

        @Test
        @DisplayName("Should return profile when found")
        void getProfileById_WhenFound_ReturnsProfile() {
            // Arrange
            when(profileRepository.findById(TEST_PROFILE_ID)).thenReturn(Optional.of(testProfile));
            when(s3Service.getExpiredDays()).thenReturn(7);

            // Act
            Optional<ProfileDto> result = profileService.getProfileById(TEST_PROFILE_ID);

            // Assert
            assertThat(result).isPresent();
            assertThat(result.get().getFirstName()).isEqualTo(testProfile.getFirstName());
            assertThat(result.get().getLastName()).isEqualTo(testProfile.getLastName());
            verify(profileRepository).findById(TEST_PROFILE_ID);
        }

        @Test
        @DisplayName("Should create new profile successfully")
        void createProfile_Success() {
            // Arrange
            when(profileRepository.save(any(Profile.class))).thenReturn(testProfile);

            // Act
            ProfileDto result = profileService.createOrUpdate(testProfileDto);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getFirstName()).isEqualTo(testProfileDto.getFirstName());
            assertThat(result.getLastName()).isEqualTo(testProfileDto.getLastName());
            verify(profileRepository).save(any(Profile.class));
        }

        @Test
        @DisplayName("Should delete profile successfully")
        void deleteProfile_Success() {
            // Arrange
            doNothing().when(profileRepository).deleteById(TEST_PROFILE_ID);

            // Act
            profileService.deleteProfile(TEST_PROFILE_ID);

            // Assert
            verify(profileRepository).deleteById(TEST_PROFILE_ID);
        }
    }

    @Nested
    @DisplayName("Avatar Management Tests")
    class AvatarTests {

        @Test
        @DisplayName("Should upload avatar successfully")
        void postAvatar_Success() throws Exception {
            // Arrange
            try (MockedStatic<AuthUtils> authUtils = mockStatic(AuthUtils.class)) {
                authUtils.when(AuthUtils::getCognitoId).thenReturn(TEST_PROFILE_ID);
                when(s3Service.putFile(any(MultipartFile.class), anyString())).thenReturn(null);
                when(s3Service.getPresignedURL(anyString())).thenReturn(testUrl);
                when(pictureRepository.findById(TEST_PROFILE_ID)).thenReturn(Optional.empty());
                when(pictureRepository.save(any(Picture.class))).thenReturn(testPicture);

                // Act
                URL result = profileService.postAvatar(testFile);

                // Assert
                assertThat(result).isNotNull();
                verify(s3Service).putFile(eq(testFile), anyString());
                verify(pictureRepository).save(any(Picture.class));
            }
        }
    }

    @Nested
    @DisplayName("Registration Token Tests")
    class RegistrationTokenTests {

        @Test
        @DisplayName("Should set registration token successfully")
        void setRegistrationToken_Success() {
            // Arrange
            String token = "test-token";
            RegistrationFCM expectedRegistration = new RegistrationFCM(TEST_PROFILE_ID, token, testProfile);
            
            try (MockedStatic<AuthUtils> authUtils = mockStatic(AuthUtils.class)) {
                authUtils.when(AuthUtils::getCognitoId).thenReturn(TEST_PROFILE_ID);
                when(profileRepository.getReferenceById(TEST_PROFILE_ID)).thenReturn(testProfile);
                when(registrationFcmRepository.save(any(RegistrationFCM.class))).thenReturn(expectedRegistration);

                // Act
                RegistrationFCM result = profileService.setRegistrationToken(token);

                // Assert
                assertThat(result).isNotNull();
                assertThat(result.getRegistrationToken()).isEqualTo(token);
                verify(registrationFcmRepository).save(any(RegistrationFCM.class));
            }
        }
    }

    @Nested
    @DisplayName("Notification Tests")
    class NotificationTests {

        @Test
        @DisplayName("Should get daily prescription scheduler successfully")
        void getDailyPrescriptionScheduler_Success() {
            // Arrange
            UUID cognitoId = UUID.randomUUID();
            PageRequestParams pageRequest = new PageRequestParams();
            pageRequest.setPageNumber(0);
            pageRequest.setPageSize(10);
            
            Notification notification = Notification.builder()
                .notificationId(UUID.randomUUID())
                .cognitoId(cognitoId)
                .isActive(true)
                .isRead(false)
                .templateId(UUID.randomUUID())
                .schedulerId(UUID.randomUUID())
                .startTime(LocalTime.now())
                .isPushed(false)
                .build();
            
            Page<Notification> notificationPage = new PageImpl<>(
                Collections.singletonList(notification)
            );
            
            try (MockedStatic<AuthUtils> authUtils = mockStatic(AuthUtils.class)) {
                authUtils.when(AuthUtils::getCognitoId).thenReturn(cognitoId);
                
                when(notificationRepository.filterDailyMedicineNotification(
                    eq(cognitoId), 
                    eq(ETemplateType.MEDICINE), 
                    any(Pageable.class)
                )).thenReturn(notificationPage);
                
                when(notificationRepository.countTaken(cognitoId, ETemplateType.MEDICINE)).thenReturn(1L);
                when(notificationRepository.countRemain(cognitoId, ETemplateType.MEDICINE)).thenReturn(2L);
                when(notificationRepository.countOverdue(cognitoId, ETemplateType.MEDICINE)).thenReturn(0L);
                when(prescriptionSchedulerConverter.convertToMedicineTemplateRes(notification))
                    .thenReturn(null);

                // Act
                GetListMedicineNotificationResponse result = profileService.getDailyPrescriptionScheduler(pageRequest);

                // Assert
                assertThat(result).isNotNull();
                assertThat(result.getTotalTaken()).isEqualTo(1L);
                assertThat(result.getTotalRemain()).isEqualTo(2L);
                assertThat(result.getTotalOverdue()).isEqualTo(0L);
                verify(notificationRepository).filterDailyMedicineNotification(
                    eq(cognitoId), 
                    eq(ETemplateType.MEDICINE), 
                    any(Pageable.class)
                );
                verify(prescriptionSchedulerConverter).convertToMedicineTemplateRes(notification);
            }
        }

        @Test
        @DisplayName("Should switch notification mode successfully")
        void switchModeNotificationScheduler_Success() {
            // Arrange
            UUID cognitoId = UUID.randomUUID();
            UUID notificationId = UUID.randomUUID();
            UUID templateId = UUID.randomUUID();
            
            Notification notification = Notification.builder()
                    .notificationId(notificationId)
                    .cognitoId(cognitoId)
                    .templateId(templateId)
                    .schedulerId(UUID.randomUUID())
                    .startTime(LocalTime.now())
                    .isPushed(false)
                    .isActive(true)
                    .isRead(false)
                    .build();

            UpdateNotificationRequest request = UpdateNotificationRequest.builder()
                    .notificationId(notificationId)
                    .isActive(false)
                    .isRead(true)
                    .build();

            when(notificationRepository.getReferenceById(notificationId)).thenReturn(notification);
            when(notificationRepository.save(any(Notification.class))).thenReturn(notification);

            // Act
            profileService.switchModeNotificationScheduler(request);

            // Assert
            assertThat(notification.getIsActive()).isFalse();
            assertThat(notification.getIsRead()).isTrue();
            verify(notificationRepository).save(notification);
            verify(notificationRepository).getReferenceById(notificationId);
        }
    }

    @Nested
    @DisplayName("Coordinate Operations")
    class CoordinateTests {

        @Test
        @DisplayName("Should update coordinates successfully")
        void updateCoordinates_Success() {
            // Arrange
            Double newLat = 37.7749;
            Double newLng = -122.4194;
            doNothing().when(profileRepository).updateCoordinate(TEST_PROFILE_ID, newLat, newLng);

            // Act
            profileService.updateCoordinates(TEST_PROFILE_ID, newLat, newLng);

            // Assert
            verify(profileRepository).updateCoordinate(TEST_PROFILE_ID, newLat, newLng);
        }

        @Test
        @DisplayName("Should calculate distances successfully")
        void calculateDistance_Success() {
            // Arrange
            Double targetLat = 37.7749;
            Double targetLng = -122.4194;
            List<Double> expectedDistances = Arrays.asList(10.5, 20.3, 30.1);
            
            CriteriaBuilder cb = mock(CriteriaBuilder.class);
            CriteriaQuery<Double> cq = mock(CriteriaQuery.class);
            Root<Profile> root = mock(Root.class);
            @SuppressWarnings("unchecked")
            Path<Object> latPath = mock(Path.class);
            @SuppressWarnings("unchecked")
            Path<Object> lngPath = mock(Path.class);
            Expression<Double> latDiff = mock(Expression.class);
            Expression<Double> lngDiff = mock(Expression.class);
            Expression<Double> latSquare = mock(Expression.class);
            Expression<Double> lngSquare = mock(Expression.class);
            Expression<Double> sum = mock(Expression.class);
            Order order = mock(Order.class);
            TypedQuery<Double> query = mock(TypedQuery.class);
            
            when(em.getCriteriaBuilder()).thenReturn(cb);
            when(cb.createQuery(Double.class)).thenReturn(cq);
            when(cq.from(Profile.class)).thenReturn(root);
            when(root.get(Profile.Fields.latitude)).thenReturn(latPath);
            when(root.get(Profile.Fields.longitude)).thenReturn(lngPath);
            
            // Mock latitude calculations
            when(cb.diff(any(), eq(targetLat))).thenReturn(latDiff);
            when(cb.prod(latDiff, latDiff)).thenReturn(latSquare);
            
            // Mock longitude calculations
            when(cb.diff(any(), eq(targetLng))).thenReturn(lngDiff);
            when(cb.prod(lngDiff, lngDiff)).thenReturn(lngSquare);
            
            // Mock sum and ordering
            when(cb.sum(latSquare, lngSquare)).thenReturn(sum);
            when(cb.asc(sum)).thenReturn(order);
            when(cq.select(sum)).thenReturn(cq);
            when(cq.orderBy(order)).thenReturn(cq);
            
            when(em.createQuery(cq)).thenReturn(query);
            when(query.getResultList()).thenReturn(expectedDistances);
            
            // Act
            List<Double> result = profileService.calculateDistance(targetLat, targetLng);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).isEqualTo(expectedDistances.stream().limit(10).toList());
            verify(em).createQuery(cq);
            verify(cq).select(sum);
            verify(cq).orderBy(order);
        }
    }

    @Nested
    @DisplayName("Error Handling Tests")
    class ErrorHandlingTests {

        @Test
        @DisplayName("Should handle null profile data gracefully")
        void handleNullProfile_Gracefully() {
            // Arrange
            ProfileDto nullProfile = null;

            // Act & Assert
            assertThrows(IllegalArgumentException.class, () -> {
                profileService.createOrUpdate(nullProfile);
            });
        }

        @Test
        @DisplayName("Should handle invalid coordinates gracefully")
        void handleInvalidCoordinates_Gracefully() {
            // Arrange
            Double invalidLat = 91.0; // Invalid latitude value
            Double invalidLng = -181.0; // Invalid longitude value

            // Act & Assert
            assertThrows(IllegalArgumentException.class, () -> {
                profileService.updateCoordinates(TEST_PROFILE_ID, invalidLat, invalidLng);
            });
        }
    }

    @Nested
    @DisplayName("Bulk Operations Tests")
    class BulkOperationsTests {

        @Test
        @DisplayName("Should save multiple profiles successfully")
        void saveManyProfiles_Success() {
            // Arrange
            List<Profile> profiles = Arrays.asList(
                createTestProfile("John", "Doe"),
                createTestProfile("Jane", "Smith")
            );
            when(profileRepository.saveAll(anyList())).thenReturn(profiles);

            // Act
            List<Profile> result = profileService.saveManyProfiles(profiles);

            // Assert
            assertThat(result).hasSize(2);
            verify(profileRepository).saveAll(profiles);
        }

        private Profile createTestProfile(String firstName, String lastName) {
            Profile profile = new Profile();
            profile.setCognitoId(UUID.randomUUID());
            profile.setFirstName(firstName);
            profile.setLastName(lastName);
            profile.setLatitude(0.0);
            profile.setLongitude(0.0);
            return profile;
        }
    }

    @Nested
    @DisplayName("Profile Image Tests")
    class ProfileImageTests {

        @Test
        @DisplayName("Should handle missing avatar gracefully")
        void handleMissingAvatar_Gracefully() {
            // Arrange
            when(profileRepository.findById(TEST_PROFILE_ID)).thenReturn(Optional.of(testProfile));
            when(pictureRepository.findById(TEST_PROFILE_ID)).thenReturn(Optional.empty());
            when(s3Service.getExpiredDays()).thenReturn(7);

            // Act
            Optional<ProfileDto> result = profileService.getProfileById(TEST_PROFILE_ID);

            // Assert
            assertThat(result).isPresent();
            verify(pictureRepository).findById(TEST_PROFILE_ID);
            verify(profileRepository).findById(TEST_PROFILE_ID);
        }

        @Test
        @DisplayName("Should handle invalid image file gracefully")
        void handleInvalidImageFile_Gracefully() {
            // Arrange
            MultipartFile invalidFile = new MockMultipartFile(
                "test-file.txt",
                "test-file.txt",
                "text/plain",
                "invalid image content".getBytes()
            );

            try (MockedStatic<AuthUtils> authUtils = mockStatic(AuthUtils.class)) {
                authUtils.when(AuthUtils::getCognitoId).thenReturn(TEST_PROFILE_ID);

                // Act & Assert
                assertThrows(IllegalArgumentException.class, () -> {
                    profileService.postAvatar(invalidFile);
                });
            }
        }
    }
}
