package com.covet.profile.converter;

import java.util.Optional;

import com.covet.profile.dto.ImmutableVisitCostDto;
import com.covet.profile.dto.VisitCostDto;
import com.covet.profile.persistence.model.Physician;
import com.covet.profile.persistence.model.VisitCost;

public class VisitCostConverter {
    private VisitCostConverter() {
    }

    public static VisitCostDto visitCostToDto(VisitCost visitCost) {
        return ImmutableVisitCostDto.of(Optional.ofNullable(visitCost.getId()),
                Optional.ofNullable(visitCost.getPhysicianId()), visitCost.getVisitCost(),
                visitCost.getVirtualVisitCost());
    }

    public static VisitCost dtoToVisitCost(VisitCostDto visitCostDto, Physician physician) {
        return new VisitCost(visitCostDto.getId().<PERSON><PERSON><PERSON><PERSON>(null), physician.getPhysicianId(),
                visitCostDto.getVisitCost(), visitCostDto.getVirtualVisitCost(), physician);
    }
}
