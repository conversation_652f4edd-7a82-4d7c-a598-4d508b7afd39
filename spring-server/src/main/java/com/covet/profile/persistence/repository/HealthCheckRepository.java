package com.covet.profile.persistence.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import com.covet.profile.persistence.compositeKey.HealthCheckID;
import com.covet.profile.persistence.model.HealthCheck;

public interface HealthCheckRepository
        extends JpaRepository<HealthCheck, HealthCheckID>, JpaSpecificationExecutor<HealthCheck> {

}
