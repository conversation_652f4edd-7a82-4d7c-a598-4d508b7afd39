package com.covet.profile.persistence.repository;


import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import com.covet.profile.persistence.model.AppVersion;

import java.util.Optional;

public interface AppVersionRepository extends JpaRepository<AppVersion, Long> {
    @Query(value ="SELECT * FROM app_versions av ORDER BY av.created_at DESC LIMIT 1", nativeQuery = true)
    Optional<AppVersion> findLatestVersion();
}
