DO
$$
BEGIN

CREATE TABLE if NOT EXISTS npi_address (
    id SERIAL PRIMARY KEY,
    identifier uuid not null,
    npi VARCHAR(20),
    address VARCHAR(256),
    specialty_code VARCHAR(20),
    taxonomy_group VARCHAR(1024),
    specialty_desc VARCHAR(1024),
    specialty_license VARCHAR(1024),
    state VARCHAR(10)
);
CREATE SEQUENCE IF NOT EXISTS npi_address_seq START WITH 1 INCREMENT BY 50;

CREATE TABLE if NOT EXISTS covet_insert_status (
    id uuid PRIMARY KEY,
    status VARCHAR(16),
    current_chunk INT,
    key_name VARCHAR(128)
);

END
$$;