package com.covet.profile.persistence.model;

import lombok.Data;

import javax.persistence.*;
import java.util.UUID;

@Data
@Entity
@Table(name = "npi_address")
public class NpiAddressEntity {
    @Id
    @Column(name = "id")
    @SequenceGenerator(
            name = "npi_address_gen",
            sequenceName = "npi_address_seq"
    )
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "npi_address_gen"
    )
    Long id;

    @Column(name = "identifier")
    UUID identifier;

    @Column(name = "npi")
    String npi;

    @Column(name = "address")
    String address;

    @Column(name = "specialty_code")
    String specialtyCode;

    @Column(name = "taxonomy_group")
    String taxonomyGroup;

    @Column(name = "specialty_desc")
    String specialtyDesc;

    @Column(name = "specialty_license")
    String specialtyLicense;

    @Column(name = "state")
    String state;
}
