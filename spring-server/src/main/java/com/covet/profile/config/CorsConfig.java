package com.covet.profile.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

@Configuration
public class CorsConfig {

    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        
        // Allow specific origin
        config.addAllowedOrigin("http://localhost:3000");
        config.addAllowedOrigin("https://portal.covethealth.io");
        
        // Allow credentials
        config.setAllowCredentials(true);
        
        // Allow specific headers
        config.addAllowedHeader("Content-Type");
        config.addAllowedHeader("Authorization");
        config.addAllowedHeader("x-api-key");  // Explicitly add x-api-key here
        
        // Allow all HTTP methods
        config.addAllowedMethod("*");
        
        // Register the CORS configuration
        source.registerCorsConfiguration("/**", config);
        return new CorsFilter(source);
    }
}
