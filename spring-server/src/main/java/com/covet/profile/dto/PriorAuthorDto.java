package com.covet.profile.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;
import java.util.Date;
import java.util.Optional;
import java.util.UUID;

@JsonSerialize(as = ImmutablePriorAuthorDto.class)
@JsonDeserialize(as = ImmutablePriorAuthorDto.class, builder = ImmutablePriorAuthorDto.Builder.class)
@Value.Immutable
public interface PriorAuthorDto {

    @Value.Parameter
    @JsonProperty
    Optional<UUID> getId();

    @Value.Parameter
    @JsonProperty
    UUID getPatientId();

    @Value.Parameter
    @JsonProperty
    String getRequestMedication();

    @Value.Parameter
    @JsonProperty
    String getRequestOperation();

    @Value.Parameter
    @JsonProperty
    Date getCreatedDate();

    @Value.Parameter
    @JsonProperty
    String getAuthorStatus();

    @Value.Parameter
    @JsonProperty
    Boolean getIsHistorical();

    @Value.Parameter
    @JsonProperty
    Boolean getIsCurrent();

    @Value.Parameter
    @JsonProperty
    String getClaimCode();
}
