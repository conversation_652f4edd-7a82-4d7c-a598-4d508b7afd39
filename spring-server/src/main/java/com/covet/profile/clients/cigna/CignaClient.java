package com.covet.profile.clients.cigna;

import com.covet.profile.clients.cigna.response.location.LocationResponse;
import com.covet.profile.clients.cigna.response.organization.OrganizationResponse;
import com.covet.profile.clients.cigna.response.practitioner.PractitionerResponse;
import com.covet.profile.config.CignaClientConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        value = "cignaClient",
        url = "https://fhir.cigna.com/ProviderDirectory/v1",
        configuration = CignaClientConfig.class
)
public interface CignaClient {
    @GetMapping("/Practitioner")
    PractitionerResponse getPractitioner(@RequestParam(value = "name", required = false) String name);

    @GetMapping("/Location")
    LocationResponse getLocation(@RequestParam(value = "address-city", required = false) String addressCity,
            @RequestParam(value = "address-state", required = false) String addressState,
            @RequestParam(value = "address-postalcode", required = false) String addressPostalCode,
            @RequestParam(value = "address", required = false) String address,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "_id", required = false) String id);

    @GetMapping("/Organization")
    OrganizationResponse getOrganization(@RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "address", required = false) String address,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "_id", required = false) String id);
}
