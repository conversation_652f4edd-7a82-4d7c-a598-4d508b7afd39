package com.covet.profile.persistence.model;

import com.sun.istack.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "health_care_method")
public class HealthCareMethod {
    @Id
    @Column(name = "method_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID methodId;

    @Column(name = "method_name")
    @NotNull
    private String methodName;

    @Column(name = "cost")
    @NotNull
    private Double cost;
}
