package com.covet.profile.converter;

import com.covet.profile.dto.covet.profile.ImmutableProfileDto;
import com.covet.profile.dto.covet.profile.ProfileDto;
import com.covet.profile.persistence.model.Profile;

import java.net.URL;
import java.util.Optional;

public class ProfileConverter {

    private ProfileConverter() {
    }

    public static ProfileDto profileToProfileDto(Profile profile, URL avatarUrl) {
        return ImmutableProfileDto.of(
                Optional.ofNullable(profile.getCognitoId()),

                profile.getFirstName(),
                Optional.ofNullable(profile.getMiddleName()),
                profile.getLastName(),

                Optional.ofNullable(profile.getCreatedDate()),
                Optional.ofNullable(profile.getUpdatedDate()),

                profile.getSocialSecurityNumber(),
                Optional.ofNullable(profile.getDateOfBirth()),
                profile.getAddress(),
                Optional.ofNullable(profile.getAddressLine()),

                Optional.ofNullable(profile.getCity()),
                Optional.ofNullable(profile.getState()),
                Optional.ofNullable(profile.getZipCode()),
                Optional.ofNullable(profile.getLatitude()),
                Optional.ofNullable(profile.getLongitude()), Optional.ofNullable(avatarUrl));
    }

    public static Profile profileDtoToProfile(ProfileDto profileDto) {
        return new Profile(
                profileDto.getCognitoId().orElse(null),

                profileDto.getFirstName(),
                profileDto.getMiddleName().orElse(null),
                profileDto.getLastName(),

                profileDto.getCreatedDate().orElse(null),
                profileDto.getUpdatedDate().orElse(null),

                profileDto.getSocialSecurityNumber(),
                profileDto.getDateOfBirth().orElse(null),
                profileDto.getAddress(),
                profileDto.getAddressLine().orElse(null),

                profileDto.getCity().orElse(null),
                profileDto.getState().orElse(null),
                profileDto.getZipCode().orElse(null),
                profileDto.getLatitude().orElse(0.0),
                profileDto.getLongitude().orElse(0.0));
    }
}
