package com.covet.profile.persistence.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "physician")
@FieldNameConstants
public class Physician {

    @Id
    @Column(name = "physician_id")
    @NotNull
    private UUID physicianId;

    @Column(name = "accept_new_patient")
    private Boolean isAcceptNewPatient;

    @Column(name = "is_virtual")
    @ColumnDefault(value = "false")
    private Boolean isVirtual;

    @Column(name = "address")
    private String address;

    @Column(name = "address_line")
    private String addressLine;

    @Column(name = "city")
    private String city;

    @Column(name = "administrative_area")
    private String state;

    @Column(name = "zip_code")
    private String zipCode;

    @Column(name = "npi")
    private String npi;

    @Column(name = "value")
    @NotNull
    private Double value;

    @Column(name = "quality")
    @NotNull
    private Double quality;

    @Column(name = "efficiency")
    @NotNull
    private Double efficiency;

    @Column(name = "source")
    @NotNull
    private String source;

    @Column(name = "facility_name")
    @NotNull
    private String facilityName;

    @OneToOne(cascade = CascadeType.ALL)
    @NotNull
    @MapsId
    @JoinColumn(name = "physician_id")
    private Profile profile;

    @JsonIgnore
    @OneToMany(mappedBy = "physician", cascade = CascadeType.ALL)
    @BatchSize(size = 10)
    List<GroupPhysician> groups;

    @JsonIgnore
    @MapsId
    @JoinColumn(name = "physician_id")
    @OneToOne(cascade = {CascadeType.REMOVE})
    private PhysicianRating physicianRating;

    @JsonIgnore
    @ManyToMany(fetch = FetchType.LAZY)
    @BatchSize(size = 10)
    @JoinTable(
            name = "physician_specialty",
            joinColumns = @JoinColumn(name = "physician_id"),
            inverseJoinColumns = @JoinColumn(name = "specialty_id")
    )
    private List<Specialty> specialties;

    @JsonIgnore
    @ManyToMany(fetch = FetchType.LAZY)
    @BatchSize(size = 10)
    @JoinTable(
            name = "physician_network",
            joinColumns = @JoinColumn(name = "physician_id"),
            inverseJoinColumns = @JoinColumn(name = "network_id")
    )
    private Set<Network> networks;
}
