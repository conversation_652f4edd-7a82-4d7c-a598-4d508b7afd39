create table prior_author  (
                        id uuid not null,
                        patient_id uuid not null,
                        req_med varchar (256),
                        req_op varchar (256),
                        created_date timestamp,
                        physician_id uuid not null,
                        author_status varchar (20) not null,
                        is_historical boolean,
                        is_current boolean,
                        primary key (id),
                        constraint fk_prior_author_physician foreign key (physician_id) references physician,
                        constraint fk_prior_author_patient foreign key (patient_id) references profile
                      );