package com.covet.profile.converter;

import com.covet.profile.dto.covet.pharmacy.PharmacyDto;
import com.covet.profile.dto.covet.prescription.ActivePrescriptionsDto;
import com.covet.profile.dto.covet.prescription.ImmutableActivePrescriptionsDto;
import com.covet.profile.dto.covet.prescription.ImmutablePrescriptionDto;
import com.covet.profile.dto.covet.prescription.PrescriptionDto;
import com.covet.profile.persistence.model.Prescription;

import java.net.URL;
import java.util.Optional;

public class PrescriptionConverter {
    private PrescriptionConverter() {
    }

    public static PrescriptionDto prescriptionToDto(Prescription prescription) {
        return ImmutablePrescriptionDto.of(
                Optional.ofNullable(prescription.getPrescriptionId()),
                prescription.getQuantity(),
                prescription.getForm(),
                prescription.getStrength(),
                Optional.ofNullable(prescription.getPhysicianID()),
                Optional.ofNullable(prescription.getInitRefillDate()),
                Optional.ofNullable(prescription.getNextRefillDate()),
                prescription.getDrugID());
    }

    public static Prescription dtoToPrescription(PrescriptionDto prescriptionDto) {
        return new Prescription(
                prescriptionDto.getPrescriptionId().orElse(null),
                prescriptionDto.getQuantity(),
                prescriptionDto.getForm(),
                prescriptionDto.getStrength(),
                prescriptionDto.getPhysicianID().orElse(null),
                prescriptionDto.getInitRefillDate().orElse(null),
                prescriptionDto.getInitRefillDate().orElse(null),
                prescriptionDto.getDrugID());
    }

    public static ActivePrescriptionsDto toActivePrescriptionsDto(Prescription prescription, URL imgURL, long numOfRefills, String prescriber, PharmacyDto pharmacy, Double cost) {
        return ImmutableActivePrescriptionsDto.of(
                prescription.getPrescriptionId().getPrescriptionName(),
                prescription.getPrescriptionId().getPrescriptionCode(),
                Optional.ofNullable(imgURL),
                prescription.getQuantity(),
                prescription.getForm(),
                prescription.getStrength(),
                numOfRefills,
                Optional.ofNullable(prescription.getInitRefillDate()),
                Optional.ofNullable(prescription.getNextRefillDate()),
                prescriber,
                Optional.ofNullable(pharmacy),
                cost
        );
    }
}
