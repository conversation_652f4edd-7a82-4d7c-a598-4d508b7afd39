package com.covet.profile.FactoryMethod;

import com.covet.profile.FactoryMethod.GetFavorite.GetFavoriteRequest;
import com.covet.profile.FactoryMethod.GetFavorite.IGetFavoriteMethod;
import com.covet.profile.systemEnum.EFavoriteType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.EnumMap;
import java.util.Set;

@Service
public class GetFavoriteFactoryMethod<T, R> {
    private final EnumMap<EFavoriteType, IGetFavoriteMethod<T, R>> methodEnumMap;

    @Autowired
    public GetFavoriteFactoryMethod(Set<IGetFavoriteMethod<T, R>> methods) {
        this.methodEnumMap = new EnumMap<>(EFavoriteType.class);
        for (var method : methods) {
            this.methodEnumMap.put(method.getFavoriteType(), method);
        }
    }

    public IGetFavoriteMethod<T, R> getMethod(GetFavoriteRequest request) {
        return methodEnumMap.get(request.getFavoriteType());
    }
}
