alter table prior_author
drop constraint if exists fk_prior_author_provider;
alter table prior_author
drop constraint if exists fk_prior_author_owner;


DO $$
BEGIN
  IF EXISTS(SELECT *
    FROM information_schema.columns
    WHERE table_name='prior_author' and column_name='provider_id')
  THEN
      ALTER TABLE "public"."prior_author" RENAME COLUMN "provider_id" TO "physician_id";
  END IF;
END $$;

DO $$
BEGIN
  IF EXISTS(SELECT *
    FROM information_schema.columns
    WHERE table_name='prior_author' and column_name='provider_id')
  THEN
      ALTER TABLE "public"."prior_author" RENAME COLUMN "owner_id" TO "patient_id";
  END IF;
END $$;


DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'fk_prior_author_physician') THEN
        alter table prior_author
        add constraint fk_prior_author_physician foreign key (physician_id) references physician;
    END IF;
END;
$$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'fk_prior_author_patient') THEN
        alter table prior_author
        add constraint fk_prior_author_patient foreign key (patient_id) references profile;
    END IF;
END;
$$;
