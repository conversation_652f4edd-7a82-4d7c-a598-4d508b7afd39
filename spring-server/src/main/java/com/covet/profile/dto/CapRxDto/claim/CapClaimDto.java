
package com.covet.profile.dto.CapRxDto.claim;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class CapClaimDto {

    @JsonProperty("compound_code")
    String compoundCode;

    @JsonProperty("date_prescription_written")
    String datePrescriptionWritten;

    @JsonProperty("daw")
    String daw;

    @JsonProperty("days_supply")
    Double daysSupply;

    @JsonProperty("fill_number")
    int fillNumber;

    @JsonProperty("ndc")
    String ndc;

    @JsonProperty("number_of_refills_authorized")
    int numberOfRefillsAuthorized;

    @JsonProperty("other_coverage_code")
    String otherCoverageCode;

    @JsonProperty("prescription_service_reference_number")
    String prescriptionServiceReferenceNumber;

    @JsonProperty("prescription_service_reference_number_qualifier")
    String prescriptionServiceReferenceNumberQualifier;

    @JsonProperty("product_service_id_qualifier")
    String productServiceIdQualifier;

    @JsonProperty("quantity_dispensed")
    Double quantityDispensed;

}
