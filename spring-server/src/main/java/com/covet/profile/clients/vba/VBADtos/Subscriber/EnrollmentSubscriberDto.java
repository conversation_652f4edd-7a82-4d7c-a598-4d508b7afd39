package com.covet.profile.clients.vba.VBADtos.Subscriber;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.util.Date;

@JsonSerialize(as = ImmutableEnrollmentSubscriberDto.class)
@JsonDeserialize(as = ImmutableEnrollmentSubscriberDto.class, builder = ImmutableEnrollmentSubscriberDto.Builder.class)
@Value.Immutable
public interface EnrollmentSubscriberDto {
    @Value.Parameter
    @JsonProperty
    String getSubscriberId();

    @Value.Parameter
    @JsonProperty
    String getPlanId();

    @Value.Parameter
    @JsonProperty
    String getGroupId();

    @Value.Parameter
    @JsonProperty
    Date getPlanStart();

    @Value.Parameter
    @JsonProperty
    Date getPlanEnd();
}
