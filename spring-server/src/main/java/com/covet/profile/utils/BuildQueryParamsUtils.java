package com.covet.profile.utils;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.net.URLEncoder;

@Slf4j
public class BuildQueryParamsUtils {
    public static String build(Object obj) {
        StringBuilder query = new StringBuilder();
        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                String fieldName = field.getName();
                Object fieldValue = field.get(obj);
                if (fieldValue != null) {
                    if (!query.isEmpty()) {
                        query.append("&");
                    }
                    query.append(URLEncoder.encode(fieldName, "UTF-8"));
                    query.append("=");
                    query.append(URLEncoder.encode(fieldValue.toString(), "UTF-8"));
                }
            } catch (Exception e) {
                log.info("Build query params fail with e: {}", e.getMessage());
            }

        }
        return query.toString();
    }
}
