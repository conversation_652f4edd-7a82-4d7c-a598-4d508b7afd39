package com.covet.profile.searchCriteria;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

import java.util.Optional;
import java.util.Set;

@Data
public class GroupProviderSearchCriteria {
    @Parameter(description = "Search by Group Name - Description - Specialty")
    public String keyword;

    @Parameter(description = "Values:  Get list of values in CH-49 comments")
    public Set<String> groupSpecialty;

    @Parameter(description = "Values: From 0 to 5")
    public Double rating;

    @Parameter
    public Double distance;

    @Parameter
    public Double value;

    public Optional<String> getKeyword() {
        return Optional.ofNullable(keyword);
    }

    public Optional<Set<String>> getGroupSpecialty() {
        return Optional.ofNullable(groupSpecialty);
    }

    public Optional<Double> getRating() {
        return Optional.ofNullable(rating);
    }
}
