package com.covet.profile.converter.notification;

import com.covet.profile.persistence.compositeKey.PrescriptionID;
import com.covet.profile.dto.reminder.template.MedicineTemplateDto;
import com.covet.profile.dto.PrescriptionSchedulerDto.ShortCutActivePrescription;
import org.springframework.stereotype.Component;

@Component
public class MedicineConverter {
    public MedicineTemplateDto activePrescriptionToMedicineTemplateBuilder(
            ShortCutActivePrescription activePrescriptionsDto, String note, String dosage) {
        var prescriptionId = new PrescriptionID(activePrescriptionsDto.getPrescriptionName(),
                activePrescriptionsDto.getPrescriptionCode());
        var quantity = activePrescriptionsDto.getQuantity();
        var form = activePrescriptionsDto.getForm();
        var strength = activePrescriptionsDto.getStrength();

        return MedicineTemplateDto.builder()
                .prescriptionId(prescriptionId)
                .quantity(quantity)
                .form(form)
                .note(note)
                .dosage(dosage)
                .strength(strength)
                .build();
    }
}
