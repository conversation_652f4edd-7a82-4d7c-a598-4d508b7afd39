package com.covet.profile.persistence.model;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "app_versions")
public class AppVersion {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String version;

    @Column(name = "force_update", nullable = false)
    private boolean forceUpdate;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt = LocalDateTime.now();

    // Constructors
    public AppVersion() {}

    public AppVersion(String version, boolean forceUpdate) {
        this.version = version;
        this.forceUpdate = forceUpdate;
    }

    // Getters and Setters
    public Long getId() { return id; }
    public String getVersion() { return version; }
    public boolean isForceUpdate() { return forceUpdate; }
    public LocalDateTime getCreatedAt() { return createdAt; }

    public void setVersion(String version) { this.version = version; }
    public void setForceUpdate(boolean forceUpdate) { this.forceUpdate = forceUpdate; }
}
