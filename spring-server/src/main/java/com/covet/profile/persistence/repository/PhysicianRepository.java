package com.covet.profile.persistence.repository;

import com.covet.profile.persistence.model.Network;
import com.covet.profile.persistence.model.Physician;
import com.covet.profile.persistence.model.Specialty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface PhysicianRepository extends JpaRepository<Physician, UUID>, JpaSpecificationExecutor<Physician> {

    List<Physician> findByIsAcceptNewPatient(boolean accept);
    List<Physician> findDistinctByProfile_FirstNameContainingOrProfile_LastNameContaining(String physicianName, String physicianNameCopy);

    @Query("select phy " +
            "from Physician phy join AverageRating ave on phy.physicianId = ave.physicianId " +
            "join phy.specialties spec " +
            "where " +
            "((:name is null)  or  upper(concat('%', phy.profile.firstName, '%', phy.profile.middleName,'%', phy.profile.lastName, '%')) LIKE upper(concat('%', :name,'%'))) " +
            "and " +
            "((:rating is null) or  (ave.averageRating >= :rating and ave.averageRating < (:rating + 1))) " +
            "and " +
            "((:specialty is null)  or  upper(spec.name) LIKE upper(concat('%', :specialty,'%')))"
    )
    Page<Physician> findAllPhysicans(@Param("name") String name, @Param("rating") Integer rating, @Param("specialty") String specialty, Pageable pageable);

    Optional<Physician> findByPhysicianId(UUID physicianId);

    Page<Physician> findAll(Specification<Physician> spec, Pageable pageable);

    @Query(value = "select p.* from physician p " +
                   "join group_physician gp on p.physician_id = gp.physician_id " +
                   "where gp.group_id = :groupId", nativeQuery = true)
    Optional<List<Physician>> findByGroupId(@Param("groupId") UUID groupId);

    @Query(value = "select p from Physician p where p.physicianId in (:physicianIds)")
    List<Physician> findByPhysicianIds(@Param("physicianIds") List<UUID> physicianIds);

    @Query(value = "select p from Physician p where p.npi in (:providerIds)")
    List<Physician> findByPhysicianNpis(@Param("providerIds") List<String> providerIds);

    @Query(value = """
            select p.physicianId, p.npi from Physician p
            """)
    List<List<String>> getIDNpiList(Pageable pageable);

    @Query(value = "select p from Physician p where lower(p.profile.firstName) like lower(concat('%', :providerName, '%')) and p.npi = :npi")
    Optional<Physician> findByNameAndNpi(@Param("providerName") String providerName, @Param("npi") String npi);

    @Query(value = """
            select p from Physician p
            where p.npi = :npiCode
            """)
    List<Physician> getPhysiciansByNpiCode(@Param("npiCode") String npiCode);

    @Query(value = """
            select distinct p from Physician p
            left join fetch p.networks
            where p.physicianId in (:phyIds)
            """)
    List<Physician> fetchPhysicianNetworks(@Param("phyIds") List<UUID> phyIds);

    @Query(value = """
            select distinct p from Physician p
            left join fetch p.specialties
            where p.physicianId in (:phyIds)
            """)
    List<Physician> fetchPhysicianSpecialties(@Param("phyIds") List<UUID> phyIds);
}
