import WebSocket from 'ws';
import pubsub from '../webSocket/pubsub.js';
//TODO: add token refresh and solve why reconnect failes 
//TODO: subscriptikons from apollo studio work but not the graphql_flutter client 

// Global map to track WebSocket connections
let wsConnections = new Map();

const createHealthieWsConnection = (userId, conversationId) => {
  const token = process.env.HEALTHIE_TOKEN;
  const healthieWsUrl = `wss://ws.staging.gethealthie.com/subscriptions?token=${token}`;
  const wsClient = new WebSocket(healthieWsUrl);  
  let pingTimeout = null;

  const heartbeat = () => {
    clearTimeout(pingTimeout);
    
    // Set the pong wait to twice the ping interval to give some buffering time.
    pingTimeout = setTimeout(() => {
      console.log(`No ping received for WebSocket connection of user ${userId} and conversation ${conversationId}. Closing connection.`);
      closeWsConnection(userId, conversationId);
    }, 700000); 
  };

  wsClient.on('open', () => {
    heartbeat();
    console.log(`WebSocket connection to Healthie API opened for user ${userId} and conversation ${conversationId}`);

    const channelId = Math.round(Date.now() + Math.random() * 100000).toString(16);
    
    const subscribeCommand = {
      command: 'subscribe',
      identifier: JSON.stringify({
        channel: 'GraphqlChannel',
        channelId,
      }),
    };
    
    wsClient.send(JSON.stringify(subscribeCommand));

    const noteAddedSubscriptionQuery = {
      query: `
        subscription onNoteAddedSubscription($id: String) {
          noteAddedSubscription(conversationId: $id) {
            ...NoteFragment
            __typename
          }
        }
    
        fragment NoteFragment on Note {
          id
          content
          user_id
          conversation_id
          attached_image_url
          attached_audio_url
          document_id
          created_at
          updated_at
          is_autoresponse
          deleted_by_user
          scheduled_at
          image_name
          document_name
          on_behalf_user {
            id
            full_name
            __typename
          }
          creator {
            id
            full_name
            avatar_url
            is_patient
            first_name_last_initial
            __typename
          }
          __typename
        }
      `,
      variables: {
        id: conversationId
      },
      operationName: "onNoteAddedSubscription",
      action: "execute"
    };

    const messageCommand = {
      command: 'message',
      identifier: JSON.stringify({
        channel: 'GraphqlChannel',
        channelId,
      }),
      data: JSON.stringify(noteAddedSubscriptionQuery),
    };

    wsClient.send(JSON.stringify(messageCommand));
  });

  wsClient.on('message', (message) => {
    heartbeat();
    const msg = JSON.parse(message);
    if (msg.type === 'ping') {
      console.log('Ping received:', msg.message);
    } else if (msg.type === 'welcome') {
      console.log('Connection successful, welcome message received:', msg.sid);
    } else if (msg.type === 'confirm_subscription') {
      console.log('Subscription confirmed:', msg.identifier);
    } else if (msg.message && msg.message.result && msg.message.result.data && msg.message.result.data.noteAddedSubscription) {
      console.log('Received subscription event:', msg.message.result);
      pubsub.publish(`OPEN_CONVERSATION_${conversationId}`, { openConversation: msg.message.result.data.noteAddedSubscription });
    } else {
      console.log('Other message:', msg);
    }
  });

  wsClient.on('error', (error) => {
    heartbeat();
    console.error('WebSocket error:', error);
  });

  wsClient.on('close', (code, reason) => {
    console.log(`WebSocket connection closed with code ${code} and reason ${reason}`);
    clearTimeout(pingTimeout);
    closeWsConnection(userId, conversationId); // cleaning up after close
  });

  if (!wsConnections.has(userId)) {
    wsConnections.set(userId, new Map());
  }

   const userConnections = wsConnections.get(userId);
  if (userConnections.has(conversationId)) {
    // Close the existing connection if there is one
    console.log("closing already opened ws!")
    const existingWsClient = userConnections.get(conversationId);
    existingWsClient.close();
  }
  userConnections.set(conversationId, wsClient);

  return wsClient;
};

export const closeWsConnection = (userId, conversationId) => {
  if (wsConnections.has(userId)) {
    const userConnections = wsConnections.get(userId);
    if (userConnections.has(conversationId)) {
      const wsClient = userConnections.get(conversationId);
      wsClient.close();
      userConnections.delete(conversationId);
      console.log(`WebSocket connection for user ${userId} and conversation ${conversationId} closed`);
    }
    if (userConnections.size === 0) {
      wsConnections.delete(userId);
    }
  }
};

export { createHealthieWsConnection, wsConnections };