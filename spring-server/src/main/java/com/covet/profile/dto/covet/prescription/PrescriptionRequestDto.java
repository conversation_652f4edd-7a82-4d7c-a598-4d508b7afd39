package com.covet.profile.dto.covet.prescription;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

@JsonSerialize(as = ImmutablePrescriptionRequestDto.class)
@JsonDeserialize(as = ImmutablePrescriptionRequestDto.class, builder = ImmutablePrescriptionRequestDto.Builder.class)
@Value.Immutable
public interface PrescriptionRequestDto {
    @Value.Parameter
    @JsonProperty
    String getPrescriptionName();

    @Value.Parameter
    @JsonProperty
    String getPrescriptionCode();

    @Value.Parameter
    @JsonProperty
    Double getQuantity();

    @Value.Parameter
    @JsonProperty
    String getForm();

    @Value.Parameter
    @JsonProperty
    String getStrength();

    @Value.Parameter
    @JsonProperty
    Integer getDrugId();

}
