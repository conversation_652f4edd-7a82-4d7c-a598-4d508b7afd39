package com.covet.profile.data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum State {

//    AK,AL,AR,AS,AZ,CA("California"),CO,CT,DC,DE,FL,GA,GU,HI,IA,ID,IL,IN,KS,KY,LA,MA,MD,ME,MI,MN,MO,MP,MS,MT,NC,ND,NE,NH,NJ,NM,NV,NY,OH,OK,OR,PA,PR,RI,SC,SD,TN,TX,UM,UT,VA,VI,VT,WA,WI,WV,WY;

    AK("Alaska"),
    AL("Alabama"),
    AR("Arkansas"),
    AS("American Samoa"),
    AZ("Arizona"),
    CA("California"),
    CO("Colorado"),
    CT("Connecticut"),
    DC("District of Columbia"),
    DE("Delaware"),
    FL("Florida"),
    GA("Georgia"),
    GU("Guam"),
    HI("Hawaii"),
    IA("Iowa"),
    ID("Idaho"),
    IL("Illinois"),
    IN("Indiana"),
    KS("Kansas"),
    KY("Kentucky"),
    LA("Louisiana"),
    MA("Massachusetts"),
    MD("Maryland"),
    ME("Maine"),
    MI("Michigan"),
    MN("Minnesota"),
    MO("Missouri"),
    MP("Northern Mariana Islands"),
    MS("Mississippi"),
    MT("Montana"),
    NC("North Carolina"),
    ND("North Dakota"),
    NE("Nebraska"),
    NH("New Hampshire"),
    NJ("New Jersey"),
    NM("New Mexico"),
    NV("Nevada"),
    NY("New York"),
    OH("Ohio"),
    OK("Oklahoma"),
    OR("Oregon"),
    PA("Pennsylvania"),
    PR("Puerto Rico"),
    RI("Rhode Island"),
    SC("South Carolina"),
    SD("South Dakota"),
    TN("Tennessee"),
    TX("Texas"),
    UM("U.S. Minor Outlying Islands"),
    UT("Utah"),
    VA("Virginia"),
    VI("Virgin Islands of the U.S."),
    VT("Vermont"),
    WA("Washington"),
    WI("Wisconsin"),
    WV("West Virginia"),
    WY("Wyoming");

    private static final State[] ENUMS = State.values();
    private final String label;
    private State(String label) {
        this.label = label;
    }

    public static Map<String, String> getAllStatesWithName(){
        Map<String, String> listOfStatesWithName = new HashMap<>();
        for (State state : values()){
            listOfStatesWithName.put(state.name(), state.label);
        }
        return listOfStatesWithName;
    }

    public static List<String>  getAllStateCodes(){
        List<String> listOfStateCodes = new ArrayList<>();
        for (State state : values()){
            listOfStateCodes.add(state.name());
        }
        return listOfStateCodes;
    }


}




