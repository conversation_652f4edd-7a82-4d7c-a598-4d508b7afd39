BEGIN;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'address'
    ) THEN
CREATE TABLE public.address (
                                id uuid PRIMARY KEY,
                                address VARCHAR(1000) NOT NULL,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                created_by <PERSON><PERSON><PERSON><PERSON>(64),
                                updated_by <PERSON><PERSON><PERSON><PERSON>(64)
);
    ELSE
        RAISE NOTICE 'Table already exists.';
END IF;

BEGIN
    IF EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'physician_address'
    ) THEN
        -- Remove primary key constraint from the existing 'id' column if it exists
        IF EXISTS (SELECT constraint_name FROM information_schema.constraint_column_usage
                   WHERE table_name = 'physician_address' AND column_name = 'id' AND constraint_name = 'physician_address_pkey')
            THEN
                ALTER TABLE physician_address DROP CONSTRAINT IF EXISTS physician_address_pkey;
        END IF;

        -- Rename the 'id' column to 'provider_id')
        ALTER TABLE physician_address
        RENAME COLUMN id TO provider_id;

        -- Add a new 'id' column as BIGSERIAL
        ALTER TABLE physician_address ADD COLUMN id BIGSERIAL;

        -- Populate the 'id' column for existing rows
        UPDATE physician_address SET id = DEFAULT
        WHERE id IS NULL;

        -- Set the sequence to the max value of 'id' to avoid conflicts
        PERFORM setval(pg_get_serial_sequence('physician_address', 'id'), COALESCE(MAX(id), 1)) FROM physician_address;

        -- Now add the primary key constraint to the new 'id' column
        ALTER TABLE physician_address ADD PRIMARY KEY (id);
    ELSE
        RAISE NOTICE 'Table already exists.';
    END IF;
END;
END $$;

END;

COMMIT;