package com.covet.profile.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.test.context.TestPropertySource;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.DeleteObjectResponse;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@SpringBootConfiguration
@TestPropertySource(properties = {
        "spring.cloud.aws.s3.bucket=test-bucket",
        "spring.cloud.aws.s3.region=us-east-1",
        "spring.cloud.aws.s3.expire-days=30"
})
public class S3ServiceTest {
    @Value("${spring.cloud.aws.s3.bucket}")
    private String bucketName;
    @Test
    void testDeleteFile() {
        // Arrange
        S3Client s3Client = mock(S3Client.class);
        String regionString = "us-east-1"; // Replace with your actual region
        S3Service s3Service = new S3Service(regionString);
        s3Service.setS3Client(s3Client); // Set the mock S3Client

        // Assume PREFIX_FOLDER is "images/"
        String objectKey = "example-key";
        DeleteObjectRequest expectedRequest = DeleteObjectRequest.builder()
                .bucket(bucketName)
                .key("images/" + objectKey)
                .build();

        DeleteObjectResponse expectedResponse = DeleteObjectResponse.builder().build();
        when(s3Client.deleteObject(expectedRequest)).thenReturn(expectedResponse);

        // Act
        DeleteObjectResponse actualResponse = s3Service.deleteFile(objectKey);

        // Assert
        verify(s3Client, times(1)).deleteObject(expectedRequest);
        assertEquals(expectedResponse, actualResponse, "Response should match");
    }
}