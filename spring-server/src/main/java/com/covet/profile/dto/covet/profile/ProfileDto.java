package com.covet.profile.dto.covet.profile;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.PastOrPresent;
import javax.validation.constraints.Pattern;

import java.net.URL;
import java.util.Date;
import java.util.Optional;
import java.util.UUID;

@JsonSerialize(as = ImmutableProfileDto.class)
@JsonDeserialize(as = ImmutableProfileDto.class, builder = ImmutableProfileDto.Builder.class)
@Value.Immutable
public interface ProfileDto {

    @Value.Parameter
    @JsonProperty
    Optional<UUID> getCognitoId();

    @Value.Parameter
    @JsonProperty
    @NotBlank()
    String getFirstName();

    @Value.Parameter
    @JsonProperty
    Optional<String> getMiddleName();

    @Value.Parameter
    @JsonProperty
    @NotBlank(message = "Last name is mandatory")
    String getLastName();

    @JsonInclude()
    @Value.Parameter
    @JsonProperty
    Optional<Date> getCreatedDate();

    @JsonInclude()
    @Value.Parameter
    @JsonProperty
    Optional<Date> getUpdatedDate();

    @JsonInclude()
    @Value.Parameter
    @JsonProperty
    @NotBlank(message = "Social security number is mandatory")
    String getSocialSecurityNumber();

    @JsonInclude()
    @Value.Parameter
    @JsonProperty
    Optional<@PastOrPresent Date> getDateOfBirth();

    @Value.Parameter
    @JsonProperty
    @NotBlank(message = "Address is mandatory")
    String getAddress();

    @Value.Parameter
    @JsonProperty
    Optional<String> getAddressLine();

    @Value.Parameter
    @JsonProperty
    Optional<String> getCity();

    @Value.Parameter
    @JsonProperty
    Optional<String> getState();

    @JsonInclude()
    @Value.Parameter
    @JsonProperty
    Optional<String> getZipCode();

    @Value.Parameter
    @JsonProperty
    Optional<Double> getLatitude();

    @Value.Parameter
    @JsonProperty
    Optional<Double> getLongitude();

    @Value.Parameter
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    Optional<URL> getAvatarUrl();
}