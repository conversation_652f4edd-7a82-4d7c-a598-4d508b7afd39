package com.covet.profile.clients.cigna.response.organization;

import com.covet.profile.clients.cigna.response.common.Address;
import com.covet.profile.clients.cigna.response.common.BaseResource;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class OrganizationResource extends BaseResource {
    private boolean active;
    private String name;
    private List<Address> address;
    private PartOf partOf;
}
