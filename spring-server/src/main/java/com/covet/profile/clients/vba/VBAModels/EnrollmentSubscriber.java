package com.covet.profile.clients.vba.VBAModels;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.util.Date;

@Getter
@Setter
public class EnrollmentSubscriber {
    @Column(name = "Subscriber_ID")
    private String subscriberId;

    @Column(name = "Plan_ID")
    private String planId;

    @Column(name = "Group_ID")
    private String groupId;

    @Column(name = "Plan_Start")
    private Date planStart;

    @Column(name = "Plan_End")
    private Date planEnd;
}
