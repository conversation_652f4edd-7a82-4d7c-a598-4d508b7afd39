package com.covet.profile.service;

import com.covet.profile.converter.DiscountConverter;
import com.covet.profile.dto.DiscountDto;
import com.covet.profile.exception.ResourceNotFoundException;
import com.covet.profile.persistence.model.Claim;
import com.covet.profile.persistence.model.HealthCareMethod;
import com.covet.profile.persistence.model.VisitCost;
import com.covet.profile.persistence.repository.*;
import com.covet.profile.responses.ResponseHandler;
import com.covet.profile.persistence.specification.DiscountSpec;
import com.covet.profile.persistence.specification.HealthCheckSpec;
import com.covet.profile.persistence.specification.VisitCostSpec;
import com.covet.profile.utils.RoundingUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.persistence.EntityNotFoundException;
import java.util.*;

@Service
public class IndividualService {
    private final HealthCheckRepository healthCheckRepository;
    private final HealthCareMethodRepository healthCareMethodRepository;
    private final DiscountRepository discountRepository;
    private final VisitCostRepository visitCostRepository;
    private final ClaimRepository claimRepository;

    @Autowired
    IndividualService(
            HealthCheckRepository healthCheckRepository,
            HealthCareMethodRepository healthCareMethodRepository,
            DiscountRepository discountRepository,
            VisitCostRepository visitCostRepository,
            PhysicianRepository physicianRepository,
            ClaimRepository claimRepository) {
        this.healthCheckRepository = healthCheckRepository;
        this.healthCareMethodRepository = healthCareMethodRepository;
        this.discountRepository = discountRepository;
        this.visitCostRepository = visitCostRepository;
        this.claimRepository = claimRepository;

    }

    public ResponseEntity<Object> getIndividualClaimDetail(String claimCode) throws EntityNotFoundException {
        Claim targetClaim = claimRepository.getReferenceById(claimCode);

        UUID physicianId = targetClaim.getPhysicianId();
        VisitCost visitCost = getPhysicianVisitCode(physicianId);
        Date createdDate = targetClaim.getCreatedDate();
        List<UUID> methodIds = healthCheckRepository.findAll(HealthCheckSpec.getMethodIdsByClaimCode(claimCode))
                .stream()
                .map(healthCheck -> healthCheck.getHealthCheckId().getMethodId()).toList();
        List<HealthCareMethod> healthCareMethods = healthCareMethodRepository.findAllById(methodIds);
        Double totalCostMethods = getCostMethods(healthCareMethods);
        Double totalCost = visitCost.getVisitCost() + totalCostMethods;
        List<Map<String, Object>> healthCareInfos = getHealthCareInfo(healthCareMethods);

        return ResponseHandler.generateIndividualClaimDetailResponse(healthCareInfos, visitCost, totalCost,
                totalCostMethods, createdDate, HttpStatus.OK);
    }

    public List<Map<String, Object>> getHealthCareInfo(List<HealthCareMethod> healthCareMethods) {
        List<Map<String, Object>> methodInfoList = new ArrayList<>();
        for (int i = 0; i < healthCareMethods.size(); i++) {
            List<DiscountDto> discounts = discountRepository
                    .findAll(DiscountSpec.getDiscountsByHealthCareMethodId(healthCareMethods.get(i).getMethodId()))
                    .stream()
                    .map(discount -> DiscountConverter.DiscountToDto(discount)).toList();
            methodInfoList.add(ResponseHandler.generateHealthCareMethod(healthCareMethods.get(i).getMethodId(),
                    healthCareMethods.get(i).getMethodName(), healthCareMethods.get(i).getCost(),
                    discounts));

        }
        return methodInfoList;
    }

    public VisitCost getPhysicianVisitCode(UUID physicianId) {

        Optional<VisitCost> visitCost = visitCostRepository
                .findOne(VisitCostSpec.getVisitCostByPhysicianId(physicianId));
        if (!visitCost.isPresent()) {
            throw new ResourceNotFoundException("Not found physician's (virtual)visit cost");
        }
        var virtualVisitCostValue = RoundingUtils.roundDoubleValue(visitCost.get().getVirtualVisitCost());
        var visitCostValue = RoundingUtils.roundDoubleValue(visitCost.get().getVisitCost());

        visitCost.get().setVirtualVisitCost(virtualVisitCostValue);
        visitCost.get().setVirtualVisitCost(visitCostValue);

        return visitCost.get();
    }

    public Double getCostMethods(List<HealthCareMethod> healthCareMethods) {
        Double result = 0.0;
        for (int i = 0; i < healthCareMethods.size(); i++) {
            result += healthCareMethods.get(i).getCost();
        }
        return result;
    }
}
