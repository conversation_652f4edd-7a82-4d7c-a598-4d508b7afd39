package com.covet.profile.controller;

import com.covet.profile.persistence.model.Claim;
import com.covet.profile.clients.vba.VBAService.VBAClaimService;

import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Validated
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class VBAController {
    private final VBAClaimService vbaClaimService;

    @PostMapping("/vba/claims")
    public ResponseEntity<List<Claim>> createClaims() {
        List<Claim> claims = vbaClaimService.getRecentClaims();
        return new ResponseEntity<>(claims, HttpStatus.CREATED);
    }
}
