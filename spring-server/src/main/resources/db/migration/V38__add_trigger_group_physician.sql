CREATE OR <PERSON><PERSON>LACE FUNCTION update_average_rating_when_insert_group() RETURNS TRIGGER AS $update_average_rating_when_insert_group$
BEGIN
    IF (NEW.group_id IS NOT NULL) THEN
        INSERT INTO group_rating VALUES (NEW.group_id, 0, 0);
        RAISE NOTICE 'updated group provider average rating';
    END IF;
    RETURN NULL; -- result is ignored since this is an AFTER trigger
END;
$update_average_rating_when_insert_group$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER update_average_rating_when_insert_group
    AFTER INSERT OR DELETE ON group_provider
    FOR EACH ROW EXECUTE FUNCTION update_average_rating_when_insert_group();

DO $$
BEGIN
  IF NOT EXISTS(SELECT *
    FROM information_schema.columns
    WHERE table_name='physician' and column_name='facility_name')
  THEN
      ALTER TABLE "public"."physician" ADD facility_name varchar(256);
  END IF;
END $$;