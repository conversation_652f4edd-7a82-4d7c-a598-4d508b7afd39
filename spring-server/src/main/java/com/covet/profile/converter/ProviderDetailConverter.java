package com.covet.profile.converter;

import com.covet.profile.dto.covet.provider.ImmutableProviderDetailDto;
import com.covet.profile.dto.covet.provider.PhysicianDetailDto;
import com.covet.profile.dto.covet.provider.ProviderDetailDto;
import com.covet.profile.persistence.model.Review;

import java.time.LocalTime;
import java.util.List;
import java.util.Optional;

public class ProviderDetailConverter {
    private ProviderDetailConverter() {

    }

    public static ProviderDetailDto modelsToDto(PhysicianDetailDto physician, List<LocalTime> availableTimes,
            Review review, long totalReviews) {
        return ImmutableProviderDetailDto.of(
                physician,
                availableTimes,
                Optional.ofNullable(review),
                totalReviews
        );
    }
}
