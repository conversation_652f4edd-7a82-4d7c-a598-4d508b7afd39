package com.covet.profile.service;

import com.covet.profile.converter.NotificationConverter;
import com.covet.profile.converter.NotificationTemplateConverter;
import com.covet.profile.converter.SchedulerConverter;
import com.covet.profile.converter.notification.response.ReminderRes;
import com.covet.profile.dto.common.PageRequestParams;
import com.covet.profile.dto.enums.EReminderStatus;
import com.covet.profile.dto.reminder.ReminderDto;
import com.covet.profile.persistence.model.Notification;
import com.covet.profile.persistence.repository.NotificationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Service
@RequiredArgsConstructor
public class NotificationService {

    private final NotificationRepository notificationRepository;
    private final Auth0Service auth0Service;
    private final NotificationTemplateConverter notificationTemplateConverter;
    private final SchedulerConverter schedulerConverter;
    private final NotificationConverter notificationConverter;

    @PersistenceContext
    private EntityManager entityManager;

    public void updateNotification(UUID notificationUuid) {
        Notification noti = notificationRepository.getReferenceById(notificationUuid);
        noti.setIsPushed(false);
        notificationRepository.save(noti);
    }

    public ReminderRes getPatientNotifications(PageRequestParams requestParams, EReminderStatus filter) {
        var pageNumber = Optional.ofNullable(requestParams.getPageNumber()).orElse(0);
        var pageSize = Optional.ofNullable(requestParams.getPageSize()).orElse(10);
        Pageable pageable = PageRequest.of(pageNumber, pageSize);

        UUID cognitoId = auth0Service.getCognitoId();

        Page<Notification> notificationPage;
        switch (filter) {
            case READ -> notificationPage = notificationRepository.filterDailyNotification(cognitoId, true, pageable);
            case UNREAD -> notificationPage = notificationRepository.filterDailyNotification(cognitoId, false, pageable);
            default -> notificationPage = notificationRepository.getAllDailyNotification(cognitoId, pageable);
        }

        CompletableFuture<Long> countAllTaken = CompletableFuture.supplyAsync(
                () -> notificationRepository.countAllTaken(cognitoId)
        );
        CompletableFuture<Long> countAllRemain = CompletableFuture.supplyAsync(
                () -> notificationRepository.countAllRemain(cognitoId)
        );
        CompletableFuture<Long> countAllOverdue = CompletableFuture.supplyAsync(
                () -> notificationRepository.countAllOverdue(cognitoId)
        );

        List<CompletableFuture<Long>> futures = new ArrayList<>();
        futures.add(countAllTaken);
        futures.add(countAllRemain);
        futures.add(countAllOverdue);
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allOf.join();

        var reminderPage =  notificationPage.map(noti -> {
            var reminderItemBuilder = ReminderDto.builder();

            var template = notificationTemplateConverter.toNotificationTemplateDto(noti.getTemplate());
            var scheduler = schedulerConverter.toSchedulerDto(noti.getScheduler());
            var notification = notificationConverter.toNotificationDto(noti);

            reminderItemBuilder.notification(notification);
            reminderItemBuilder.scheduler(scheduler);
            reminderItemBuilder.template(template);
            return reminderItemBuilder.build();

        });

        return ReminderRes.builder()
                .data(reminderPage)
                .totalTaken(countAllTaken.join())
                .totalRemain(countAllRemain.join())
                .totalOverdue(countAllOverdue.join())
                .build();
    }

    public ReminderDto getPatientNotificationDetail(UUID notificationId) {
        UUID cognitoId = auth0Service.getCognitoId();
        var noti = notificationRepository.dailyNotificationDetail(cognitoId, notificationId);

        var template = notificationTemplateConverter.toNotificationTemplateDto(noti.getTemplate());
        var scheduler = schedulerConverter.toSchedulerDto(noti.getScheduler());
        var notification = notificationConverter.toNotificationDto(noti);

        var reminderItemBuilder = ReminderDto.builder();

        reminderItemBuilder.notification(notification);
        reminderItemBuilder.scheduler(scheduler);
        reminderItemBuilder.template(template);
        return reminderItemBuilder.build();
    }

    @Transactional
    public void deletePatientNotification(UUID notificationId) {
        Notification notification = entityManager.find(Notification.class, notificationId);
        if (notification != null) {
            entityManager.remove(notification);
        }
    }
}
