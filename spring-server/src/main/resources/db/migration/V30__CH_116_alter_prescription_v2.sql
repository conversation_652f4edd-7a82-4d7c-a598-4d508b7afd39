BEGIN;
        D<PERSON> $$
BEGIN
IF NOT EXISTS (
  SELECT column_name
  FROM information_schema.columns
  WHERE table_name = 'prescription'
    AND column_name in ('physician_id', 'drug_id')
) THEN
  -- Add the column
ALTER TABLE prescription
    ADD COLUMN physician_id UUID DEFAULT 'b3651772-f0b4-49bc-80b1-f58b68be0e27';

ALTER TABLE prescription
    ADD COLUMN drug_id int DEFAULT 0 NOT NULL;
END IF;
END $$;

-- DO $$
-- BEGIN
-- IF NOT EXISTS (
--   SELECT column_name
--   FROM information_schema.columns
--   WHERE table_name = 'prescription_pharmacy'
--     AND column_name in ('patient_id')
-- ) THEN
--   -- Add the column
-- ALTER TABLE prescription_pharmacy
--     ADD COLUMN patient_id UUID DEFAULT 'c5290591-b336-4a6b-b654-38d7092963f6';
-- END IF;
-- END $$;
COMMIT;