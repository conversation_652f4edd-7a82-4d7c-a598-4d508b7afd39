package com.covet.profile.dto.covet.prescription;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

@JsonSerialize(as = ImmutablePrescriptionPharmacyRequestDto.class)
@JsonDeserialize(as = ImmutablePrescriptionPharmacyRequestDto.class, builder = ImmutablePrescriptionPharmacyRequestDto.Builder.class)
@Value.Immutable
public interface PrescriptionPharmacyRequestDto {
    @Value.Parameter
    @JsonProperty("prescription")
    PrescriptionDto getPrescriptionDto();

    @Value.Parameter
    @JsonProperty
    String getPharmacyId();

    @Value.Parameter
    @JsonProperty
    Double getCost();
}
