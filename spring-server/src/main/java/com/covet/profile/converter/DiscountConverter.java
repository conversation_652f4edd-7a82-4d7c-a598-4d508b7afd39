package com.covet.profile.converter;

import java.util.Optional;

import com.covet.profile.dto.DiscountDto;
import com.covet.profile.dto.ImmutableDiscountDto;
import com.covet.profile.persistence.model.HealthCareMethod;
import com.covet.profile.persistence.model.Discount;

public class DiscountConverter {
    private DiscountConverter() {
    }

    public static DiscountDto DiscountToDto(Discount discount) {
        return ImmutableDiscountDto.of(Optional.ofNullable(discount.getDiscountId()), discount.getMethodId(),
                discount.getDiscountType(),
                discount.getDiscountCode(), discount.getCreateAt(), discount.getExpiredAt());
    }

    public static Discount DtoToDiscount(DiscountDto discountDto, HealthCareMethod healthCareMethod) {
        return new Discount(discountDto.getDiscountId().orElse(null), discountDto.getMethodId(),
                discountDto.getDiscountType(), discountDto.getDiscountCode(), discountDto.getCreatedAt(),
                discountDto.getExpiredAt(), healthCareMethod);
    }
}
