springdoc:
  api-docs:
    path : /covet-api
    enabled: true
  swagger-ui:
    oauth:
      client-id: yzE2fuXJ1xgNWIeET4UhZO753DAFAVLB
      client-secret: gfp-GqfGMSaHiYFGvojs9mWo9CV7OkgxD1jqOk9G7H3i8XE0ZT1Q0JLtpxmnZoKm
    oauth2-redirect-url: https://dev-backend.covethealth.io/covet/swagger-ui/oauth2-redirect.html

swagger:
  authorization-url: https://dev-covet.us.auth0.com/authorize?audience=https://dev-backend.covethealth.io/api
  token-url: https://dev-covet.us.auth0.com/oauth/token
  server-url: https://dev-backend.covethealth.io


spring:
  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: https://dev-covet.us.auth0.com/
          audiences: https://dev-backend.covethealth.io/api
  web:
    cors:
      allowed-origin-patterns: "http://localhost:3000"
      allowed-headers: "Content-Type,Authorization,x-api-key"
      allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
  third-party:
    vba:
      domain: https://vbapi.vbasoftware.com/vbasoftware
      secret-name: VBA-headers-info
      databaseSecret: VBATestDatabase

    auth0:
      clientId: ${AUTH0_CLIENT_ID}
      clientSecret: ${AUTH0_CLIENT_SECRET}
      audience: https://dev-covet.us.auth0.com/api/v2/
      domain: dev-covet.us.auth0.com
    metabase:
      key: ${METABASE_KEY}
logging:
  level:
    root: INFO
