#!/bin/bash

# Fetch the latest tag from the repository
latest_tag=$(git describe --tags `git rev-list --tags --max-count=1`)

# Default to 0.1.0 if no tags exist
if [ -z "$latest_tag" ]; then
    latest_tag="0.1.0"
fi

# Extract major, minor, and patch numbers
IFS='.' read -ra PARTS <<< "$latest_tag"
major=${PARTS[0]}
minor=${PARTS[1]}
patch=${PARTS[2]}

# Check the last commit message for major or minor keywords
last_commit_message=$(git log -1 --pretty=%B)

if [[ "$last_commit_message" == *"[major]"* ]]; then
    let major+=1
    minor=0
    patch=0
elif [[ "$last_commit_message" == *"[minor]"* ]]; then
    let minor+=1
    patch=0
else
    let patch+=1
fi

# Create new version tag
new_tag="${major}.${minor}.${patch}"
echo "New version: $new_tag"

# Update version in properties file
echo "app.version=$new_tag" > spring-server/src/main/resources/version.properties
cat spring-server/src/main/resources/version.properties

# Add and commit the change
git add spring-server/src/main/resources/version.properties
git commit -m "Update version to $new_tag [skip ci]"

# Tagging and pushing
git tag $new_tag
git push --tags
git push