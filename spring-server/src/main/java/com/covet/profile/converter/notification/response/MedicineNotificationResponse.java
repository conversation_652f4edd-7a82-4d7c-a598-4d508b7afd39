package com.covet.profile.converter.notification.response;

import com.covet.profile.persistence.compositeKey.PrescriptionID;
import com.covet.profile.systemEnum.ETakenPrescriptionStatus;
import lombok.Builder;
import lombok.Data;

import java.time.LocalTime;
import java.util.UUID;

@Data
@Builder
public class MedicineNotificationResponse {
    UUID notificationId;
    PrescriptionID prescriptionId;
    int quantity;
    String form;
    String strength;
    Boolean isPushed;
    ETakenPrescriptionStatus status;
    Boolean isActive;
    LocalTime startTime;
    String dosage;
    String note;
}
