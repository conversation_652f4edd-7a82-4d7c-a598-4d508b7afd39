package com.covet.profile.dto.covet.pharmacy;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.util.Optional;

@JsonSerialize(as = ImmutablePharmacyDto.class)
@JsonDeserialize(as = ImmutablePharmacyDto.class, builder = ImmutablePharmacyDto.Builder.class)
@Value.Immutable
public interface PharmacyDto {
    @Value.Parameter
    @JsonProperty
    Optional<String> getPharmacyId();

    @Value.Parameter
    @JsonProperty
    Double getLatitude();

    @Value.Parameter
    @JsonProperty
    Double getLongitude();

    @Value.Parameter
    @JsonProperty
    String getPharmacyName();

    @Value.Parameter
    @JsonProperty
    String getAddress();

    @Value.Parameter
    @JsonProperty
    String getPhoneNumber();
}
