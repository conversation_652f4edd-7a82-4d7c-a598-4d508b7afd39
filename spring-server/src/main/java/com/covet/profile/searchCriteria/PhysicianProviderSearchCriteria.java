package com.covet.profile.searchCriteria;

import com.covet.profile.systemEnum.ESortType;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import java.util.Set;
import java.util.UUID;

@Getter
@Setter
public class PhysicianProviderSearchCriteria {

    @Parameter(description = "Search by Doctor Name and Specialty")
    private String keyword;

    @Parameter(description = "Values:  Radiology, Cardiology, Oncology, Psychology,\n" +
            "        Endocrinology, Pediatrics, Dentist;")
    private Set<UUID> specialtyIds;

    @Parameter(description = "Values: From 0 to 5")
    private Double rating;

    @Parameter
    @Min(0)
    private Double distance;

    @Parameter(description = "default ASC")
    private ESortType distanceSortType;

    @Parameter
    @Min(0)
    private Double value;

    @Parameter(description = "default DESC")
    private ESortType valueSortType;

    private String groupId;

    @Parameter
    private Set<UUID> networkIds;

    public Set<UUID> getSpecialtyIds() {
        return specialtyIds;
    }

    public Set<UUID> getNetworkIds() {
        return networkIds;
    }

}
