package com.covet.profile.clients.vba.VBADtos.IDcard.Spending;

import org.immutables.value.Value;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonSerialize(as = ImmutableVBASpendingDto.class)
@JsonDeserialize(as = ImmutableVBASpendingDto.class, builder = ImmutableVBASpendingDto.Builder.class)
@Value.Immutable
public interface VBASpendingDto {

    @Value.Parameter
    @JsonProperty
    VBASpendingAttributesDto getDeductibles();

    @Value.Parameter
    @JsonProperty
    VBASpendingAttributesDto getOutOfPocketMax();

}
