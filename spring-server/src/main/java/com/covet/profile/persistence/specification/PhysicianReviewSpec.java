package com.covet.profile.persistence.specification;

import com.covet.profile.persistence.model.Profile;
import com.covet.profile.persistence.model.Review;
import com.covet.profile.searchCriteria.PhysicianReviewSearchCriteria;
import com.covet.profile.systemEnum.SysEnum;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class PhysicianReviewSpec {
    private PhysicianReviewSpec() {}
    public static Specification<Review> hasFilterCriteriaIn(PhysicianReviewSearchCriteria criteria) {
        return ((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (criteria.getVisitType().isPresent()) {
                Set<Integer> values = Arrays.stream(SysEnum.EVisitType.values()).map(SysEnum.EVisitType::getValue).collect(Collectors.toSet());
                boolean isVisitTypeContained = criteria.getVisitType().get().stream().anyMatch(values::contains);

                if (isVisitTypeContained) {
                    predicates.add(root.get("visitType").in(criteria.getVisitType().get()));
                }
            }

            criteria.getRating().ifPresent(rating -> predicates.add(criteriaBuilder.equal(root.get("rating"), rating)));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        });
    }

    public static Specification<Review> hasReviewContentLike(String keyword) {
        return (root, query, criteriaBuilder) -> criteriaBuilder.like(criteriaBuilder.lower(root.get("content")), "%" + keyword.toLowerCase() + "%");
    }

    public static Specification<Review> hasPatientFirstNameLike(String keyword) {
        return getReviewSpecification(keyword, "firstName");
    }
    public static Specification<Review> hasPatientMiddleNameLike(String keyword) {
        return getReviewSpecification(keyword, "middleName");
    }

    public static Specification<Review> hasPatientLastNameLike(String keyword) {
        return getReviewSpecification(keyword, "lastName");
    }

    private static Specification<Review> getReviewSpecification(String keyword, String attributeName) {
        return (root, query, criteriaBuilder) -> {
            Join<Review, Profile> patientProfile= root.join("profile");
            return criteriaBuilder.like(criteriaBuilder.lower(patientProfile.get(attributeName)), "%" + keyword.toLowerCase() + "%");
        };
    }
}
