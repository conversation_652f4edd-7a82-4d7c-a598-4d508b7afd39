package com.covet.profile.dto.covet.provider;

import com.covet.profile.persistence.model.Network;
import com.covet.profile.persistence.model.PhysicianRating;
import com.covet.profile.persistence.model.Profile;
import com.covet.profile.persistence.model.Specialty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.net.URL;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

@JsonSerialize(as = ImmutablePhysicianDto.class)
@JsonDeserialize(as = ImmutablePhysicianDto.class, builder = ImmutablePhysicianDto.Builder.class)
@Value.Immutable
public interface PhysicianDto {

    @Value.Parameter
    @JsonProperty
    Optional<UUID> getPhysicianId();

    @Value.Parameter
    @JsonProperty
    Boolean getIsAcceptNewPatient();

    @Value.Parameter
    @JsonProperty
    Boolean getIsVirtual();

    @Value.Parameter
    @JsonProperty
    List<Specialty> getSpecialties();

    @Value.Parameter
    @JsonProperty
    @NotBlank(message = "Address is mandatory")
    String getAddress();

    @Value.Parameter
    @JsonProperty
    Optional<String> getAddressLine();

    @Value.Parameter
    @JsonProperty
    Optional<String> getCity();

    @Value.Parameter
    @JsonProperty
    Optional<String> getState();

    @JsonInclude()
    @Value.Parameter
    @JsonProperty
    Optional<String> getZipCode();

    @Value.Parameter
    @JsonProperty
    Optional<String> getNpi();

    @Value.Parameter
    @JsonProperty
    Optional<String> getFacilityName();

    @Value.Parameter
    @JsonProperty
    Optional<Double> getValue();

    @Value.Parameter
    @JsonProperty
    Optional<Double> getQuality();

    @Value.Parameter
    @JsonProperty
    Optional<Double> getEfficiency();

    @Value.Parameter
    @JsonProperty
    Optional<String> getSource();

    @Value.Parameter
    @JsonProperty
    Profile getProfile();

    @Value.Parameter
    @JsonProperty
    Optional<PhysicianRating> getPhysicianRating();

    @Value.Parameter
    @JsonProperty
    Set<Network> getNetworks();

    @Value.Parameter
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    Optional<URL> getAvatarUrl();

    @Value.Parameter
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    Optional<JsonNode> getAdditionalInfo();

}
