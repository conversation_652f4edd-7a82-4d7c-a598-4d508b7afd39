package com.covet.profile.persistence.repository;

import com.covet.profile.persistence.compositeKey.ClaimPrescriptionID;
import com.covet.profile.persistence.compositeKey.PrescriptionID;
import com.covet.profile.persistence.model.ClaimPrescription;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.UUID;

public interface ClaimPrescriptionRepository
        extends JpaRepository<ClaimPrescription, ClaimPrescriptionID>, JpaSpecificationExecutor<ClaimPrescription> {

    Page<ClaimPrescription> findAll(Specification<ClaimPrescription> spec, Pageable pageable);

    @Modifying
    @Query(value = "update claim_prescription " +
            "set pharmacy_id = :newPharmacyId " +
            "where prescription_code = :prescriptionCode and prescription_name = :prescriptionName and pharmacy_id = :oldPharmacyId and claim_code = :claimCode", nativeQuery = true)
    void updateMappingPharmacy(@Param("oldPharmacyId") UUID oldPharmacyId, @Param("newPharmacyId") UUID newPharmacyId, @Param("prescriptionName") String prescriptionName, @Param("prescriptionCode") String prescriptionCode, @Param("claimCode") String claimCode);

    @Query(value = "select cp from ClaimPrescription cp " +
            "where cp.prescription.prescriptionId in (:prescriptions) and cp.claim.patientId = :patientID")
    List<ClaimPrescription> findAllByPatientPrescription(@Param("prescriptions") List<PrescriptionID> prescriptions, @Param("patientID") UUID patientID);

//    @Query(value = "select * from claim_prescription cp\n" +
//            "join claim c on cp.claim_code = c.claim_code\n" +
//            "join prescription p on cp.prescription_name = p.prescription_name and cp.prescription_code = p.prescription_code\n" +
//            "where p.prescription_name in (:prescriptions.Prescription) and p.prescription_code in (:prescriptionCodes) \n" +
//            "and c.patient_id = :patientID", nativeQuery = true)
//    List<ClaimPrescription> findAllByPatientPrescription(@Param("prescriptionName") List<PrescriptionID> prescriptions, @Param("patientID") UUID patientID);
}
