package com.covet.profile.converter;

import com.covet.profile.clients.vba.VBADtos.Financial.ImmutableFinancialValuesDto;
import com.covet.profile.utils.RoundingUtils;
import com.covet.profile.clients.vba.VBADtos.Financial.FinancialValuesDto;

import java.math.BigDecimal;

public class FinanceConverter {
    public static FinancialValuesDto financialValueToDto(BigDecimal appliedValue, BigDecimal remainingValue, BigDecimal maxValue) {
        return ImmutableFinancialValuesDto.of(
                RoundingUtils.customRoundValue(appliedValue),
                RoundingUtils.customRoundValue(remainingValue),
                RoundingUtils.customRoundValue(maxValue)
        );
    }
}
