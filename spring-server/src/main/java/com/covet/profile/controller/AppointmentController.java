package com.covet.profile.controller;

import com.covet.profile.dto.covet.appointment.AppointmentDto;
import com.covet.profile.dto.covet.appointment.AppointmentTicketDto;
import com.covet.profile.clients.vba.VBADtos.Member.SubscriberMemberDto;
import com.covet.profile.service.AppointmentService;
import com.covet.profile.service.Auth0Service;
import com.covet.profile.utils.AuthUtils;
import com.covet.profile.service.interfaces.IPhysicianService;
import com.microsoft.sqlserver.jdbc.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Validated
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@Slf4j
public class AppointmentController {

    private final AppointmentService appointmentService;
    private final Auth0Service auth0Service;
    private final IPhysicianService physicianService;

    @PostMapping("/schedule-appointment")
    public ResponseEntity scheduleAppointment(@Valid @RequestBody AppointmentTicketDto appointmentTicketDto) {
        try {

            String auth0ID = AuthUtils.getAuth0Id();
            // String email = auth0Service.getUserEmail(auth0ID);

            // SubscriberMemberDto subscriberID = vbaUserService.getSubscriberMemberInfo(email);
            SubscriberMemberDto subscriberID = auth0Service.getSubscriberMemberID(auth0ID);

            return physicianService.getPhysicianById(appointmentTicketDto.getPhysicianId())
                    .map(physician -> handleAppointmentCreation(appointmentTicketDto, subscriberID))
                    .orElseGet(() -> {
                        log.error("PhysicianId {} doesn't exist for creating appointment", appointmentTicketDto.getPhysicianId());
                        return new ResponseEntity<>("Physician not found", HttpStatus.BAD_REQUEST);
                    });

        } catch (org.springframework.web.client.HttpClientErrorException.UnprocessableEntity e) {
            log.error("Unprocessable Entity: Server could not parse JSON", e);
            return new ResponseEntity<>("Server could not parse JSON. Please check the request payload.", HttpStatus.UNPROCESSABLE_ENTITY);
        } catch (Exception e) {
            log.error("Error scheduling appointment", e);
            return new ResponseEntity<>("An error occurred while scheduling appointment", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private ResponseEntity handleAppointmentCreation(AppointmentTicketDto appointmentTicketDto, SubscriberMemberDto subscriberID) {
        try {
            AppointmentDto newAppointment = appointmentService.createOrUpdateAppointment(appointmentTicketDto);
            if (newAppointment == null) {
                return new ResponseEntity<>("Failed to create appointment", HttpStatus.INTERNAL_SERVER_ERROR);
            }

            appointmentTicketDto.setAppointmentId(newAppointment.getId());
            String response = appointmentService.createAppointmentTicket(appointmentTicketDto, subscriberID);
            if (StringUtils.isEmpty(response)) {
                var appointmentId = newAppointment.getId();
                if (appointmentId.isPresent()) {
                    appointmentService.deleteAppointment(appointmentId.get());
                }
                return new ResponseEntity<>("Failed to create appointment ticket", HttpStatus.INTERNAL_SERVER_ERROR);
            }

            return new ResponseEntity<>(newAppointment, HttpStatus.CREATED);
        } catch (Exception e) {
            log.error("Error while creating appointment ticket", e);
            return new ResponseEntity<>("An error occurred while creating appointment ticket", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}