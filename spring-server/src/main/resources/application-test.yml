springdoc:
  api-docs:
    path: /covet-api
    enabled: true
  swagger-ui:
    oauth:
      client-id: yzE2fuXJ1xgNWIeET4UhZO753DAFAVLB
      client-secret: ****************************************************************
    oauth2-redirect-url: http://localhost:8080/covet/swagger-ui/oauth2-redirect.html

swagger:
  authorization-url: https://dev-covet.us.auth0.com/authorize?audience=https://dev-backend.covethealth.io/api
  token-url: https://dev-covet.us.auth0.com/oauth/token
  server-url: http://localhost:8080

spring:
  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: https://dev-covet.us.auth0.com/
          audiences: https://dev-backend.covethealth.io/api
  third-party:
    vba:
      domain: https://vbapi.vbasoftware.com/vbasoftware
      secret-name: VBA-headers-info
      databaseSecret: VBATestDatabase

    auth0:
      clientId: RRxY7AwFIdKZG2CsIC3LfuyNhDFTk7Pl  #${AUTH0_CLIENT_ID}
      clientSecret: **************************************************************** #${AUTH0_CLIENT_SECRET}
      audience: https://dev-covet.us.auth0.com/api/v2/
      domain: dev-covet.us.auth0.com
logging:
  level:
    root: INFO
    org:
      hibernate:
        SQL: DEBUG
        type:
          descriptor:
            sql:
              BasicBinder: TRACE
        orm:
          jdbc:
            bind: TRACE
RDS_DB_NAME: ebdb
RDS_HOSTNAME: playground-member-db.cs1zxbfdxfam.us-east-1.rds.amazonaws.com
RDS_PORT: 5432
RDS_USERNAME: dev
RDS_PASSWORD: 433814ced7854cc7bd8ffe8f

AUTH0_CLIENT_ID: RRxY7AwFIdKZG2CsIC3LfuyNhDFTk7Pl
AUTH0_CLIENT_SECRET: ****************************************************************
