package com.covet.profile.dto.covet.pharmacy;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.util.Optional;

@JsonSerialize(as = ImmutablePharmacyIdDto.class)
@JsonDeserialize(as = ImmutablePharmacyIdDto.class, builder = ImmutablePharmacyIdDto.Builder.class)
@Value.Immutable
public interface PharmacyIdDto {
    @Value.Parameter
    @JsonProperty
    Optional<String> getPharmacyId();
}