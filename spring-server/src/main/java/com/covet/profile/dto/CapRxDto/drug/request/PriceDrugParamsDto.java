package com.covet.profile.dto.CapRxDto.drug.request;

import com.covet.profile.dto.ProviderDto.LocationDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

// @JsonSerialize(as = ImmutablePriceDrugParamsDto.class)
// @JsonDeserialize(as = ImmutablePriceDrugParamsDto.class, builder = ImmutablePriceDrugParamsDto.Builder.class)
// @Value.Immutable
@Data
public class PriceDrugParamsDto {

    @JsonProperty
    Integer quantity;

    @JsonProperty
    String dosage;

    @JsonProperty("dosage_form")
    String dosageForm;

    @JsonProperty
    LocationDto location;

    @JsonProperty("external_member_id")
    String externalMemberId;

    @JsonProperty("drug_id")
    Integer drugId;

    @JsonProperty("person_code")
    String personCode;

    @JsonProperty("plan_id")
    Integer planId;

}
