package com.covet.profile.config;

import org.springframework.http.HttpStatus;
import org.springframework.web.filter.OncePerRequestFilter;

import com.covet.profile.utils.SecretManagerUtils;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

public class ApiKeyAuthFilter extends OncePerRequestFilter {

    private static final String API_KEY_HEADER_NAME = "X-API-KEY";
    private final String apiKey;

    public ApiKeyAuthFilter(SecretManagerUtils secretMangerUtil, String preRegistrationSecretName) throws Exception {
        this.apiKey = getPreRegistrationApiKeyFromSecretsManager(secretMangerUtil, preRegistrationSecretName);
    }

    private String getPreRegistrationApiKeyFromSecretsManager(SecretManagerUtils secretMangerUtil, String preRegistrationSecretName) throws Exception {
        Map<String, String> secrets = secretMangerUtil.getSecrets(preRegistrationSecretName);
        return secrets.get(API_KEY_HEADER_NAME);
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) 
            throws ServletException, IOException {
        
        String requestApiKey = request.getHeader(API_KEY_HEADER_NAME);

        if ("/api/profile/create-user".equals(request.getServletPath())) {
            if (apiKey.equals(requestApiKey)) {
                // API key is valid, continue the filter chain to reach the controller
                filterChain.doFilter(request, response);
                return;
            } else {
                // API key is invalid, send unauthorized error and do not continue the filter chain
                response.sendError(HttpStatus.UNAUTHORIZED.value(), "Invalid API Key");
                return;
            }
        }

        filterChain.doFilter(request, response);
    }
}

