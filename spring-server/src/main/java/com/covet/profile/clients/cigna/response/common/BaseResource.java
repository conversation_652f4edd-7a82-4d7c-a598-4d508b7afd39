package com.covet.profile.clients.cigna.response.common;

import com.covet.profile.clients.cigna.response.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class BaseResource {
    private String resourceType;
    private String id;
    private BaseMeta meta;
    private String language;
    private List<Identifier> identifier;
    private List<Telecom> telecom;
}
