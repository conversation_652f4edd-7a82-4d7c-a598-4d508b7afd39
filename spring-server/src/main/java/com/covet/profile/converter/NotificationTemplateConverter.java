package com.covet.profile.converter;

import java.util.UUID;

import com.covet.profile.FactoryMethod.ParseNotiTemplate.BaseTemplateDto;
import com.covet.profile.FactoryMethod.ParseNotiTemplateFactoryMethod;
import com.covet.profile.dto.PrescriptionSchedulerDto.NotificationTemplateDto;
import com.covet.profile.persistence.model.NotificationTemplate;
import com.covet.profile.systemEnum.ETemplateType;
import com.covet.profile.utils.JsonUtils;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Data
@RequiredArgsConstructor
@Component
public class NotificationTemplateConverter {
    private final ParseNotiTemplateFactoryMethod notiParser;

    public NotificationTemplate toNotificationTemplate(NotificationTemplateDto template) {
        UUID templateId = template.getTemplateId();
        ETemplateType templateType = template.getTemplateType();
        String route = template.getRoute();
        UUID targetId = template.getTargetId();
        String json = JsonUtils.toJson(template.getNotificationInfo());
        return new NotificationTemplate(templateId,
                templateType,
                route,
                targetId,
                json, null);

    }

    public NotificationTemplateDto toNotificationTemplateDto(NotificationTemplate template) {
        UUID templateId = template.getTemplateId();
        String route = template.getRoute();
        UUID targetId = template.getTemplateId();
        var parser = notiParser.getMethod(template.getTemplateType());
        BaseTemplateDto notificationInfo = parser.parse(template.getNotificationInfo());
        return new NotificationTemplateDto(templateId,
                template.getTemplateType(),
                route,
                targetId,
                notificationInfo);
    }
}
