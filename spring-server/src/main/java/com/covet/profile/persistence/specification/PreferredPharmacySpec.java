package com.covet.profile.persistence.specification;

import java.util.UUID;
import org.springframework.data.jpa.domain.Specification;
import com.covet.profile.persistence.model.PreferredPharmacy;

public class PreferredPharmacySpec {
    public static Specification<PreferredPharmacy> prescriptionByConditions(UUID patientId) {
        return (root, query, builder) -> {
            return builder.equal(root.get("preferredPharmacyID").get("cognitoId"), patientId);
        };
    }
}
