package com.covet.profile.persistence.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "prior_author")

public class PriorAuthor {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "id")
    private UUID id;

    @Column(name = "patient_id")
    private UUID patientId;

    @Column(name = "req_med")
    private String reqMed;

    @Column(name = "req_op")
    private String reqOp;

    @Column(name = "created_date")
    private Date createdDate;

    @Column(name = "physician_id")
    private UUID physicianId;

    @Column(name = "author_status")
    private String authorStatus;

    @Column(name = "is_historical")
    private Boolean isHistorical;

    @Column(name = "is_current")
    private Boolean isCurrent;

    // relationship
    @JsonIgnore
    @ManyToOne
    @MapsId("cognitoId")
    @JoinColumn(name = "physician_id")
    private Profile physicianInfo;

    // relationship
    @JsonIgnore
    @JoinColumn(name = "claim_code", referencedColumnName = "claim_code")
    @OneToOne(cascade = CascadeType.ALL)
    private Claim claim;
}
