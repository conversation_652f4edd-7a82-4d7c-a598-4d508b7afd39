package com.covet.profile.clients.vba.VBADtos.IDcard.Spending;

import java.math.BigDecimal;

import org.immutables.value.Value;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonSerialize(as = ImmutableVBASpendingAttributesDto.class)
@JsonDeserialize(as = ImmutableVBASpendingAttributesDto.class, builder = ImmutableVBASpendingAttributesDto.Builder.class)
@Value.Immutable
public interface VBASpendingAttributesDto {

    @Value.Parameter
    @JsonProperty
    BigDecimal getInNetwork();

    @Value.Parameter
    @JsonProperty
    BigDecimal getOutOfNetwork();
}
