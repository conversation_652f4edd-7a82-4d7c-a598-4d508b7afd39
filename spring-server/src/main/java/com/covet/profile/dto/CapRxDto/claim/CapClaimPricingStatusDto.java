package com.covet.profile.dto.CapRxDto.claim;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class CapClaimPricingStatusDto {

    @JsonProperty("authorization_number")
    String authorizationNumber;

    @JsonProperty("help_desk_phone_number")
    String helpDeskPhoneNumber;

    @JsonProperty("help_desk_phone_number_qualifier")
    String helpDeskPhoneNumberQualifier;

    @JsonProperty("transaction_response_status")
    String transactionResponseStatus;

    @JsonProperty("reject_message")
    String rejectMessage;

}
