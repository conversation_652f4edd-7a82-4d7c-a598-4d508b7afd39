package com.covet.profile.controller;

import com.covet.profile.responses.PriorAuthorPendingResponse;
import com.covet.profile.searchCriteria.PriorAuthorCriteria;
import com.covet.profile.service.PriorAuthorService;
import com.covet.profile.service.ReadFileService;
import com.covet.profile.systemEnum.SysEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.IOException;
import java.util.Optional;

// @PreAuthorize("hasRole('patient') || hasRole('insurance')")
@Validated
@RestController
@RequestMapping("/api")
public class PriorAuthorController {
    public static final Logger log = LoggerFactory.getLogger(PriorAuthorController.class);
    private final PriorAuthorService priorAuthorService;

    @Autowired
    public PriorAuthorController(PriorAuthorService priorAuthorService, ReadFileService readFileService) {
        this.priorAuthorService = priorAuthorService;
    }

    @GetMapping("/prior-author")
    public ResponseEntity<PriorAuthorPendingResponse> getPriorAuthorListByConditions(
            @Valid @RequestParam Optional<SysEnum.EPriorAuthorStatus> priorAuthorStatus,
            @Valid @ParameterObject PriorAuthorCriteria criteria, Optional<Integer> page, Optional<Integer> pageSize) {
        String authorStatusValue = "";
        if (priorAuthorStatus.isPresent()) {
            authorStatusValue = priorAuthorStatus.get().getValue();
        } else {
            authorStatusValue = null;
        }
        PriorAuthorPendingResponse result = priorAuthorService.getPriorAuthorListByConditions(authorStatusValue,
                criteria,
                page,
                pageSize);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
}
