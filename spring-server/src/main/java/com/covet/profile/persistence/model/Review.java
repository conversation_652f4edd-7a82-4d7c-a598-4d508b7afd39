package com.covet.profile.persistence.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sun.istack.NotNull;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.util.Date;
import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "review")
public class Review {

    @Id
    @GeneratedValue
    @Column(name = "review_id")
    private UUID id;

    @Column(name = "patient_id")
    @NotNull
    private UUID patientId;

    @Column(name = "physician_id")
    private UUID physicianId;

    @Column(name = "group_id")
    private UUID groupId;

    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_date", updatable = false)
    private Date createdDate;

    @Column(name = "content")
    private String content;

    @NotNull
    @Column(name = "rating")
    private Integer rating;

    @Column(name = "visit_type")
    private Integer visitType;

    @Column(name = "is_helpful")
    private Boolean isHelpful;

    @Column(name = "helpful_total")
    private int helpfulTotal;

    @JsonIgnore
    @ManyToOne
    @MapsId("patientId")
    @JoinColumn(name="patient_id")
    private Profile profile;

    @JsonIgnore
    @ManyToOne
    @MapsId("physicianId")
    @JoinColumn(name="physician_id")
    private Physician physician;

    @JsonIgnore
    @ManyToOne
    @MapsId("groupId")
    @JoinColumn(name="group_id")
    private GroupProvider group;

}
