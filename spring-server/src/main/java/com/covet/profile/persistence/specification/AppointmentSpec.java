
package com.covet.profile.persistence.specification;

import java.util.Date;
import java.util.UUID;

import javax.persistence.criteria.Predicate;

import org.springframework.data.jpa.domain.Specification;

import com.covet.profile.persistence.model.Appointment;

public class AppointmentSpec {
    public static Specification<Appointment> searchByAppointmentId(UUID patientId, UUID physicianId, Date startTime,
            Date endTime) {
        return (root, query, builder) -> {
            Predicate patientIdPredicate = builder.equal(root.get("patientId"), patientId);
            Predicate physicianIdPredicate = builder.equal(root.get("physicianId"), physicianId);
            Predicate startTimePredicate = builder.equal(root.get("startTime"), startTime);
            Predicate endTimePredicate = builder.equal(root.get("endTime"), endTime);
            Predicate[] predicates = { patientIdPredicate,
                    physicianIdPredicate,
                    startTimePredicate,
                    endTimePredicate };
            return builder.and(predicates);
        };
    }
}