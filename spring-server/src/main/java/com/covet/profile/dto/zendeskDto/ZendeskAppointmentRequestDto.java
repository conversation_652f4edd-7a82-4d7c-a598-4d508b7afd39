package com.covet.profile.dto.zendeskDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.UUID;

@Data
public class ZendeskAppointmentRequestDto implements Serializable {
    @JsonProperty("appointment_id")
    private UUID appointmentId;
    @JsonProperty("provider_name")
    private String providerName;
    @JsonProperty("provider_npi")
    private String npi;
    @JsonProperty("appointment_date")
    private LocalDate appointmentDate;
    @JsonProperty("appointment_time")
    private LocalTime appointmentTime;
    @JsonProperty("appointment_type")
    private String appointmentType;
    @JsonProperty("subscriberID_memberSeq")
    private String subscriberIdAndSeq;

    public void setAppointmentTime(String timeString) {
        DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                .parseCaseInsensitive()
                .appendPattern("hh:mm a")
                .toFormatter();
        this.appointmentTime = LocalTime.parse(timeString, formatter);
    }

    public void setNpi(String npi) {
        if (npi == null || npi.length() != 10) {
            throw new IllegalArgumentException("NPI must be 10 digits long");
        }
        this.npi = npi;
    }
}