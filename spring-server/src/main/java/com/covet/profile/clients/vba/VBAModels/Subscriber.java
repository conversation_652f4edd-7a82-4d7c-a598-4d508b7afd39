package com.covet.profile.clients.vba.VBAModels;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class Subscriber {
    @JsonProperty("subscriber_ID")
    String subscriberID;
    @JsonProperty("member_Seq")
    String memberSeq;
    @JsonProperty("access_Code")
    String accessCode;
    @JsonProperty("adult_Child")
    String adultChild;
    @JsonProperty("adult_Dependent")
    String adultDependent;
    @JsonProperty("adult_Dependent_End")
    String adultDependentEnd;
    @JsonProperty("alternate_ID")
    String alternateID;
    @JsonProperty("birth_Date")
    String birthDate;
    @JsonProperty("continue_Coverage")
    String continueCoverage;
    @JsonProperty("continue_Coverage_End_Date")
    String continueCoverageEndDate;
    @JsonProperty("continue_Coverage_Ex_Code")
    String continueCoverageExCode;
    @JsonProperty("coverage_Type")
    String coverageType;
    @JsonProperty("credible_Coverage")
    String credibleCoverage;
    @JsonProperty("creditable_Coverage_End")
    String creditableCoverageEnd;
    @JsonProperty("creditable_Coverage_Start")
    String creditableCoverageStart;
    @JsonProperty("date_Enrolled")
    String dateEnrolled;
    @JsonProperty("date_Of_Death")
    String dateOfDeath;
    @JsonProperty("disenroll_Date")
    String disenrollDate;
    @JsonProperty("ethnicity_Code")
    String ethnicityCode;
    @JsonProperty("first_Name")
    String firstName;
    @JsonProperty("height")
    String height;
    @JsonProperty("initial_Volume")
    String initialVolume;
    @JsonProperty("initial_Volume_Salary_Pct")
    String initialVolumeSalaryPct;
    @JsonProperty("last_Name")
    String lastName;
    @JsonProperty("marital_Status")
    String maritalStatus;
    @JsonProperty("middle_Name")
    String middleName;
    @JsonProperty("name_Suffix")
    String nameSuffix;
    @JsonProperty("notes")
    String notes;
    @JsonProperty("other_Insurance")
    String otherInsurance;
    @JsonProperty("other_Name")
    String otherName;
    @JsonProperty("pend_Ex_Code")
    String pendExCode;
    @JsonProperty("pend_Flag")
    String pendFlag;
    @JsonProperty("plan_Year_Frequency")
    String planYearFrequency;
    @JsonProperty("plan_Year_Frequency_Type")
    String planYearFrequencyType;
    @JsonProperty("pre_Exist")
    String preExist;
    @JsonProperty("pre_Exist_End")
    String preExistEnd;
    @JsonProperty("pre_Exist_Ex_Code")
    String preExistExCode;
    @JsonProperty("relationship")
    String relationship;
    @JsonProperty("ssn")
    String ssn;
    @JsonProperty("salutation")
    String salutation;
    @JsonProperty("sex")
    String sex;
    @JsonProperty("smoker")
    String smoker;
    @JsonProperty("student")
    String student;
    @JsonProperty("student_End")
    String studentEnd;
    @JsonProperty("unique_ID")
    String uniqueID;
    @JsonProperty("use_Member_Plan_Year")
    String useMemberPlanYear;
    @JsonProperty("viP_Flag")
    String viPFlag;
    @JsonProperty("weight")
    String weight;
}
