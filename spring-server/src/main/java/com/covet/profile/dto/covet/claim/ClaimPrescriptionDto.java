package com.covet.profile.dto.covet.claim;

import org.immutables.value.Value;

import com.covet.profile.persistence.compositeKey.ClaimPrescriptionID;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.Optional;

@JsonSerialize(as = ImmutableClaimPrescriptionDto.class)
@JsonDeserialize(as = ImmutableClaimPrescriptionDto.class, builder = ImmutableClaimPrescriptionDto.Builder.class)
@Value.Immutable
public interface ClaimPrescriptionDto {
    @Value.Parameter
    @JsonProperty
    Optional<ClaimPrescriptionID> getClaimPrescriptionId();
}
