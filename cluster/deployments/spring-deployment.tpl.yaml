apiVersion: apps/v1
kind: Deployment
metadata:
  name: spring-server
  labels:
    app: spring-server
spec:
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  replicas: 1
  selector:
    matchLabels:
      app: spring-server
  template:
    metadata:
      labels:
        app: spring-server
    spec:
      containers:
      - name: spring-server
        image: ${IMAGE_TAG}
        ports:
        - containerPort: 5000
        envFrom:
        - secretRef:
            name: spring-secrets
        resources:
          requests:
            cpu: 750m 
            memory: 500Mi 
          limits:
            cpu: 1200m
            memory: 500Mi
      
---
apiVersion: v1
kind: Service
metadata:
  name: spring-server
spec:
  selector:
    app: spring-server
  ports:
    - protocol: TCP
      port: 5000
      targetPort: 5000
