package com.covet.profile.persistence.model;

import com.covet.profile.persistence.compositeKey.HealthCheckID;
import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "health_check")
public class HealthCheck implements Serializable {

    @EmbeddedId
    private HealthCheckID healthCheckId;

    // relationship
    @JsonIgnore
    @MapsId("claimCode")
    @JoinColumn(name = "claim_code")
    @ManyToOne(cascade = CascadeType.ALL)
    private Claim claim;

    // relationship
    @JsonIgnore
    @MapsId("methodId")
    @JoinColumn(name = "method_id")
    @ManyToOne(cascade = CascadeType.ALL)
    private HealthCareMethod healthCareMethod;

}
