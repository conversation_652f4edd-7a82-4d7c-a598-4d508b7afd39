package com.covet.profile.responses.prescriptions;

import com.covet.profile.dto.covet.pharmacy.PharmacyDetailDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.domain.Page;

@Getter
@Setter
@NoArgsConstructor
public class PrescriptionPharmaciesResponse {
    private Page<PharmacyDetailDto> page;
    private Boolean isPreferredExist;
    private String message;

    // Constructor for the message-only scenario
    public PrescriptionPharmaciesResponse(String message) {
        this.message = message;
    }

    // Constructor for the scenario with page and isPreferredExist
    public PrescriptionPharmaciesResponse(Page<PharmacyDetailDto> page, Boolean isPreferredExist) {
        this.page = page;
        this.isPreferredExist = isPreferredExist;
    }
}
