package com.covet.profile.clients.vba.VBAModels;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class VBAMember {
    @JsonProperty("subscriber_ID")
    String subscriberID;
    @JsonProperty("member_Seq")
    String memberSeq;
    @JsonProperty("first_Name")
    String firstName;
    @JsonProperty("middle_name")
    String middleName;
    @JsonProperty("last_Name")
    String lastName;
    @JsonProperty("relationship")
    String relationship;
    @JsonProperty("birth_Date")
    Date birthDate;
}
