
package com.covet.profile.converter;

import java.util.Optional;

import com.covet.profile.dto.covet.prescription.ImmutablePrescriptionPharmacyDto;
import com.covet.profile.dto.covet.prescription.PrescriptionPharmacyDto;
import com.covet.profile.persistence.model.Prescription;
import com.covet.profile.persistence.model.PrescriptionPharmacy;

public class PrescriptionPharmacyConverter {
    private PrescriptionPharmacyConverter() {
    }

    public static PrescriptionPharmacyDto prescriptionToDto(PrescriptionPharmacy prescriptionPharmacy) {
        return ImmutablePrescriptionPharmacyDto.of(
                Optional.ofNullable(prescriptionPharmacy.getId()), prescriptionPharmacy.getCost());
    }

    public static PrescriptionPharmacy dtoToPrescriptionPharmacy(PrescriptionPharmacyDto prescriptionPharmacyDto,
                                                         Prescription prescription) {
        return new PrescriptionPharmacy(
                prescriptionPharmacyDto.getPrescriptionPharmacy().orElse(null), prescriptionPharmacyDto.getCost(), prescription);
    }
}
