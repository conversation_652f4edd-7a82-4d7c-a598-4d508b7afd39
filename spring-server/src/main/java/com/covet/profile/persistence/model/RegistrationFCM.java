package com.covet.profile.persistence.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sun.istack.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "registration_fcm_token")
public class RegistrationFCM {
    @Id
    @Column(name = "cognito_id")
    @NotNull
    private UUID cognitoId;

    @Column(name = "registration_token")
    @NotNull
    private String registrationToken;

    // relationship
    @JsonIgnore
    @ManyToOne(cascade = CascadeType.ALL)
    @MapsId("cognito_id")
    @JoinColumn(name = "cognito_id")
    private Profile profile;
}
