package com.covet.profile.dto;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.Optional;
import java.util.UUID;

@JsonSerialize(as = ImmutableAddressDto.class)
@JsonDeserialize(as = ImmutableAddressDto.class, builder = ImmutableAddressDto.Builder.class)
@Value.Immutable
public interface AddressDto {

    @Value.Parameter
    @JsonProperty
    Optional<UUID> getId();

    @Value.Parameter
    @JsonProperty
    @NotBlank(message = "Address is mandatory")
    String getLine1();

    @Value.Parameter
    @JsonProperty
    Optional<String> getLine2();

    @Value.Parameter
    @JsonProperty
    Optional<String> getCity();

    @Value.Parameter
    @JsonProperty
    Optional<String> getState();

    @JsonInclude()
    @Value.Parameter
    @JsonProperty
    Optional<
            @Pattern(regexp = "^(\\d{5}|^$)$", message = "Check zip code format: xxxxx")
                    String> getZipCode();  // can only contain 5 numeric characters

    @Value.Parameter
    @JsonProperty
    Optional<String> getCountry();

    @Value.Parameter
    @JsonProperty
    Optional<String> getDescription();

}