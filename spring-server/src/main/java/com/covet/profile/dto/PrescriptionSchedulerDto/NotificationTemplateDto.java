package com.covet.profile.dto.PrescriptionSchedulerDto;

import java.util.UUID;

import com.covet.profile.systemEnum.ETemplateType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@AllArgsConstructor
@Builder
public class NotificationTemplateDto {
    private UUID templateId;
    private ETemplateType templateType;
    private String route;
    private UUID targetId;
    private Object notificationInfo;
}
