package com.covet.profile.dto.PrescriptionSchedulerDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ShortCutActivePrescription {
    @NotBlank
    String prescriptionName;
    @NotBlank
    String prescriptionCode;
    @NotNull
    Integer quantity;
    @NotBlank
    String form;
    @NotBlank
    String strength;
}
