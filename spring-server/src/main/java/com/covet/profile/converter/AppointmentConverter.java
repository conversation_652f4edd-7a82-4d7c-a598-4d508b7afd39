package com.covet.profile.converter;

import com.covet.profile.dto.covet.appointment.AppointmentDto;
import com.covet.profile.dto.covet.appointment.AppointmentTicketDto;
import com.covet.profile.dto.covet.appointment.ImmutableAppointmentDto;
import com.covet.profile.dto.reminder.PhysicianProfileDto;
import com.covet.profile.dto.reminder.template.AppointmentTemplateDto;
import com.covet.profile.persistence.model.Appointment;
import com.covet.profile.persistence.model.Physician;
import com.covet.profile.persistence.model.Profile;
import com.covet.profile.systemEnum.EAppointmentStatus;
import com.covet.profile.systemEnum.SysEnum;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;


public class AppointmentConverter {

    private AppointmentConverter() {
    }
    public static AppointmentDto appointmentToDto(Appointment appointment) {
        var appointmentDate = appointment.getAppointmentDate();
        var status = appointment.getStatus();

        if (appointmentDate != null && LocalDateTime.now().toEpochSecond(ZoneOffset.UTC) > appointmentDate.toEpochSecond(ZoneOffset.UTC)) {
            status = EAppointmentStatus.COMPLETED.getValue();
        }

        return ImmutableAppointmentDto.of(
                Optional.ofNullable(appointment.getId()),
                Optional.ofNullable(appointment.getPatientId()),
                appointment.getPhysicianId(),
                appointment.getStartTime(),
                appointment.getEndTime(),
                appointment.getIsVirtual(),
                Optional.ofNullable(appointment.getAppointmentType()),
                Optional.ofNullable(appointment.getDescription()),
                Optional.ofNullable(status),
                appointment.getDaysOfWeek(),
                Optional.ofNullable(appointment.getAppointmentDate()),
                Optional.ofNullable(appointment.getPhysician())
        );
    }

    private static PhysicianProfileDto buildPhysicianProfileDto(Appointment appointment) {
        var profile = appointment.getProfile();
        var physician = appointment.getPhysician();

        UUID cognitoId = profile.getCognitoId();
        String firstName = profile.getFirstName();
        String middleName = profile.getMiddleName();
        String lastName = profile.getLastName();
        String socialSecurityNumber = profile.getSocialSecurityNumber();
        Date dateOfBirth = profile.getDateOfBirth();
        String address = profile.getAddress();
        String addressLine = profile.getAddressLine();
        String city = profile.getCity();
        String state = profile.getState();
        String zipCode = profile.getZipCode();
        Double latitude = profile.getLatitude();
        Double longitude = profile.getLongitude();
        UUID physicianId = physician.getPhysicianId();
        Boolean isAcceptNewPatient = physician.getIsAcceptNewPatient();
        Boolean isVirtual = physician.getIsVirtual();
        Set<String> specialties = physician.getSpecialties().stream().map(specialty -> specialty.getName()).collect(Collectors.toSet());
        Double value = physician.getValue();
        Double quality = physician.getQuality();
        Double efficiency = physician.getEfficiency();
        String facilityName = physician.getFacilityName();
        String npi = physician.getNpi();

        var physicianDto = new PhysicianProfileDto();
        physicianDto.setCognitoId(cognitoId);
        physicianDto.setFirstName(firstName);
        physicianDto.setLastName(lastName);
        physicianDto.setMiddleName(middleName);
        physicianDto.setSocialSecurityNumber(socialSecurityNumber);
        physicianDto.setDateOfBirth(dateOfBirth);
        physicianDto.setAddress(address);
        physicianDto.setAddressLine(addressLine);
        physicianDto.setCity(city);
        physicianDto.setState(state);
        physicianDto.setZipCode(zipCode);
        physicianDto.setLatitude(latitude);
        physicianDto.setLongitude(longitude);
        physicianDto.setPhysicianId(physicianId);
        physicianDto.setIsAcceptNewPatient(isAcceptNewPatient);
        physicianDto.setIsVirtual(isVirtual);
        physicianDto.setSpecialties(specialties);
        physicianDto.setValue(value);
        physicianDto.setQuality(quality);
        physicianDto.setEfficiency(efficiency);
        physicianDto.setFacilityName(facilityName);
        physicianDto.setNpi(npi);

        return physicianDto;

    }

    public static AppointmentTemplateDto appointmentToTemplateDto(Appointment appointment) {
        var startTime = LocalDateTime.ofInstant(appointment.getStartTime().toInstant(), ZoneId.systemDefault());
        var endTime = LocalDateTime.ofInstant(appointment.getEndTime().toInstant(), ZoneId.systemDefault());

        var physicianDto = buildPhysicianProfileDto(appointment);

        return AppointmentTemplateDto.builder()
                .id(appointment.getId())
                .patientId(appointment.getPatientId())
                .physicianId(appointment.getPhysicianId())
                .startTime(startTime)
                .endTime(endTime)
                .isVirtual(appointment.getIsVirtual())
                .appointmentType(appointment.getAppointmentType())
                .description(appointment.getDescription())
                .status(appointment.getStatus())
                .preVisit(appointment.getPreVisit())
                .preVisit(appointment.getPostVisit())
                .daysOfWeek(appointment.getDaysOfWeek())
                .appointmentDate(appointment.getAppointmentDate())
                .physicianProfile(physicianDto)
                .build();
    }
    public static Appointment dtoToAppointment(AppointmentDto appointmentDto, Profile profile, Physician physician) {
        return new Appointment(
                appointmentDto.getId().orElse(null),
                profile.getCognitoId(),
                physician.getPhysicianId(),
                appointmentDto.getStartTime(),
                appointmentDto.getEndTime(),
                appointmentDto.getIsVirtual(),
                appointmentDto.getAppointmentType().orElse(null),
                appointmentDto.getDescription().orElse(null),
                appointmentDto.getStatus().orElse(null),
                "",
                "",
                appointmentDto.getDaysOfWeek(),
                appointmentDto.getAppointmentDate().orElse(null),
                profile,
                physician
        );
    }

    public static AppointmentDto zendeskTicketToAppointmentDto(AppointmentTicketDto ticket, UUID patientId, String daysOfWeek, Physician physician) {
        return ImmutableAppointmentDto.of(
                ticket.getAppointmentId(),
                Optional.ofNullable(patientId),
                ticket.getPhysicianId(),
                new Date(),
                new Date(),
                ticket.isVirtual(),
                Optional.ofNullable(ticket.getAppointmentType()),
                Optional.ofNullable(ticket.getDescription()),
                Optional.ofNullable(SysEnum.EAppointmentStatus.PENDING.getValue()),
                daysOfWeek,
                Optional.ofNullable(LocalDateTime.now().plusDays(7)),
                Optional.ofNullable(physician)
        );
    }
}