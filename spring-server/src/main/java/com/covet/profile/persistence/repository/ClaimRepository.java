package com.covet.profile.persistence.repository;

import com.covet.profile.persistence.model.Claim;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.UUID;

public interface ClaimRepository extends JpaRepository<Claim, String>, JpaSpecificationExecutor<Claim> {
    List<Claim> findByPhysicianId(UUID physicianId);

    Page<Claim> findAll(Specification<Claim> spec, Pageable pageable);

    List<Claim> findByPatientId(UUID patientId);

    List<Claim> findByClaimType(Integer claimType);
}
