package com.covet.profile.FactoryMethod.ParseNotiTemplate;

import com.covet.profile.dto.reminder.template.AppointmentTemplateDto;
import com.covet.profile.systemEnum.ETemplateType;
import com.covet.profile.utils.JsonUtils;
import org.springframework.stereotype.Service;

@Service
public class ParseAppointmentTemplate implements IParseNotiTemplate {
    @Override
    public AppointmentTemplateDto parse(String request) {
        return JsonUtils.toObj(request, AppointmentTemplateDto.class);
    }

    @Override
    public ETemplateType getTemplateType() {
        return ETemplateType.APPOINTMENT;
    }
}
