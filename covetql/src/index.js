
import express from 'express';
import { createServer } from 'http';
import { ApolloServer } from '@apollo/server';
import { expressMiddleware } from '@apollo/server/express4';
import { ApolloServerPluginDrainHttpServer } from '@apollo/server/plugin/drainHttpServer';
import { WebSocketServer } from 'ws';
import { useServer } from 'graphql-ws/lib/use/ws';
import dotenv from 'dotenv';
import { jwtAuthMiddleware, verifyToken } from './authentication/jwtAuthMiddleware.js';
import buildSchema from './graphql/schema.js';
import cors from 'cors';
import bodyParser from 'body-parser';

dotenv.config();



const app = express();
app.use(bodyParser.json());
app.use(jwtAuthMiddleware);


// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).send('OK');
});

// GraphQL Schema

const schema =  await buildSchema();
// Creating an HTTP server
const httpServer = createServer(app);

// Creating a WebSocket server
const wsServer = new WebSocketServer({
  server: httpServer,
  path: '/subscriptions',
});

  // Use the WebSocket server
  const serverCleanup = useServer({ schema, 
    onConnect: async (ctx) => {
    try {
      if (ctx.connectionParams && ctx.connectionParams.authentication) {
        const match = ctx.connectionParams.authentication.match(/Bearer (.+)/);
        
        const user = await verifyToken( match[1]);
        return { user }; // Connection is authorized, user is added to the context
      } else {
        throw new Error("No authentication token provided");
      }
    } catch (error) {
      console.error("Authentication error:", error);
      throw new Error("Unauthorized"); // Connection is rejected
    }
  }, }, wsServer);


// Starting ApolloServer
async function startApolloServer() {
  const server = new ApolloServer({
    schema,
    context: ({ req }) => ({ user: req.user }),
    plugins: [
      ApolloServerPluginDrainHttpServer({ httpServer }),
      {
        async serverWillStart() {
          return {
            async drainServer() {
              await serverCleanup.dispose();
            },
          };
        },
      },
    ],
    
  });
  
  await server.start();
  // server.applyMiddleware({ app });
  app.use('/graphql', cors(), express.json(), expressMiddleware(server));


  // Start listening
  httpServer.listen({ port: 4000 }, () =>
    console.log(`Server ready at http://localhost:4000/graphql`)
  );
}

startApolloServer();