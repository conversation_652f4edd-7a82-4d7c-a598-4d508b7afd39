
package com.covet.profile.persistence.repository;

import com.covet.profile.persistence.compositeKey.PrescriptionID;
import com.covet.profile.persistence.model.GroupProviderAddress;
import com.covet.profile.persistence.model.Prescription;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface PrescriptionRepository
        extends JpaRepository<Prescription, PrescriptionID>, JpaSpecificationExecutor<Prescription> {

    @Query(value = """
            select * from prescription
            where prescription_name = :prescriptionName and prescription_code = :prescriptionCode and patient_id = :patientID
            """, nativeQuery = true)
    Optional<Prescription> findPrescriptionByPatientID(@Param("prescriptionName") String prescriptionName,
            @Param("prescriptionCode") String prescriptionCode, @Param("patientID") UUID patientID);

    Page<Prescription> findAll(Specification<Prescription> spec, Pageable pageable);

    List<Prescription> findAllByPrescriptionIdPatientID(UUID patientID);

    Optional<Prescription> findByPrescriptionId(PrescriptionID prescriptionID);

    @Query("""
            select gpa from GroupProviderAddress gpa
            where gpa.address != ''
            """)
    List<GroupProviderAddress> getGroupProviderNonNullAddress(Pageable pageable);
}
