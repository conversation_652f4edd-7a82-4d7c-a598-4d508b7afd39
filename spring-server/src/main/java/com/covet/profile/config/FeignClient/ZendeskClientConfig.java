package com.covet.profile.config.FeignClient;

import com.covet.profile.config.SecretMangerConfig;
import feign.RequestInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

import java.util.Map;

public class ZendeskClientConfig {
    @Autowired
    SecretMangerConfig secretMangerConfig;

    @Value("${spring.third-party.zendesk.secret-name-api}")
    private String zendeskSecretName;

    @Bean("ZendeskClientInterceptor")
    public RequestInterceptor requestInterceptor() {
        return requestTemplate -> {
            try {
                Map<String, String> secrets = secretMangerConfig.getSecrets(zendeskSecretName);
                String apiKey = secrets.get("zendeskKey");
                requestTemplate.header("Authorization", "Basic " + apiKey);
                requestTemplate.header("Content-Type","application/json");
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        };
    }
}
