package com.covet.profile.converter.VBA;

import com.covet.profile.clients.vba.VBADtos.Providers.ImmutableVBAProviderDto;
import com.covet.profile.clients.vba.VBADtos.Providers.VBAProviderDto;
import com.covet.profile.clients.vba.VBAModels.VBAProvider;

public class VBAProviderConverter {
    private VBAProviderConverter() {}

    public static VBAProviderDto vbaProviderToDto(VBAProvider provider) {
        return ImmutableVBAProviderDto.of(
                provider.getProviderId(),
                getProviderName(provider)
        );
    }

    private static String getProviderName(VBAProvider provider) {
        if (provider.getFirstName() != null && provider.getLastName() != null) {
            return String.format("%s %s", provider.getFirstName(), provider.getLastName());
        }
        else {
            return provider.getOrgName();
        }
    }
}
