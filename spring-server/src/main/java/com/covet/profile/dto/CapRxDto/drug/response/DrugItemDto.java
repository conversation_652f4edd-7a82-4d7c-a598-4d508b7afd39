
package com.covet.profile.dto.CapRxDto.drug.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

@JsonSerialize(as = ImmutableDrugItemDto.class)
@JsonDeserialize(as = ImmutableDrugItemDto.class, builder = ImmutableDrugItemDto.Builder.class)
@Value.Immutable
public interface DrugItemDto {

    @Value.Parameter
    @JsonProperty
    DrugDto getDrug();

}
