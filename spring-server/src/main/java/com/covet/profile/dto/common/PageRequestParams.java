package com.covet.profile.dto.common;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.Builder;
import lombok.Data;

import javax.annotation.Nullable;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@Data
public class PageRequestParams {
    @Nullable
    @Parameter
    @Builder.Default
    @Min(0)
    Integer pageNumber;

    @Nullable
    @Parameter
    @Builder.Default
    @Min(5)
    @Max(50)
    Integer pageSize;

    public PageRequestParams() {
        pageNumber = 0;
        pageSize = 10;
    }

}
