#server:
#  port: 8081
management:
  endpoints:
    web:
      exposure:
        include: health

springdoc:
  api-docs:
    path : /covet-api
    enabled: false
  swagger-ui:
    path: covet/ui
    oauth:
      client-id: yzE2fuXJ1xgNWIeET4UhZO753DAFAVLB
      client-secret: gfp-GqfGMSaHiYFGvojs9mWo9CV7OkgxD1jqOk9G7H3i8XE0ZT1Q0JLtpxmnZoKm
      use-basic-authentication-with-access-code-grant: true

spring:
  web:
    cors:
      allowed-origin-patterns: "https://portal.covethealth.io"
      allowed-headers: "Content-Type,Authorization,x-api-key"
      allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: https://covet-health.us.auth0.com/
          audiences: https://backend.covethealth.io/api
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          batch_size: 50
          time_zone: UTC
        validator:
          apply_to_ddl: false
        order_inserts: true
        order_updates: true
        format_sql: true
        generate_statistics: false
  datasource:
    driverClassName: org.postgresql.Driver
    jdbcUrl: jdbc:postgresql://${RDS_HOSTNAME}:${RDS_PORT}/${RDS_DB_NAME}
    username: ${RDS_USERNAME}
    password: ${RDS_PASSWORD}
    connectionTimeout: 60000
    maximumPoolSize: 5
    minimumIdle: 1
    idleTimeout: 300000
    maxLifetime: 900000
  flyway:
    enabled: true
    validate-on-migrate: false
    migrate-on-startup: true
    exclude: sqlserver
  sql:
    init:
      platform: postgresql
  profiles:
    active: dev
    include: version
  servlet:
    multipart:
      max-file-size: 4MB
      max-request-size: 4MB
      enabled: true
  cloud:
    aws:
      s3:
        bucket: covet-bucket-1681637716192
        user-pool-id: us-east-1_xSCins8RL
        expire-days: 7
        region: us-east-1
  third-party:
    capital-rx:
      app-vendor-server: https://app-vendor.cap-rx.com
      adjudication-server:  https://adjudication-api.cap-rx.com
      secret-name: prod/Cap_Rx_API
    vba:
      domain: https://vbapi.vbasoftware.com/vbasoftware
      secret-name: prod/VBA-headers-info
      databaseSecret: VBAProdDatabase
    auth0:
      clientId: ${AUTH0_CLIENT_ID}
      clientSecret: ${AUTH0_CLIENT_SECRET}
      audience: https://covet-health.us.auth0.com/api/v2/
      domain: covet-health.us.auth0.com
    security:
      pre-registration-secret-name: prod/covet/preregistrationKey
    zendesk:
      domain: https://covethealth.zendesk.com
      group-id: 15330814426523
      secret-name-api: zendeskAPI
      secret-name-webhook-appointment: Zendesk/AppointmentWebhook
      appointment-endpoint-url: /api/zendesk/webhook-appointment
    mpx:
      username: ${MPX_USERNAME}
      password: ${MPX_PASSWORD}
    metabase:
      key: ${METABASE_KEY}
logging:
  level:
    root: INFO
    org:
      hibernate: ERROR

  #   org.springframework.web: INFO
 #   org.springframework.security: INFO
 #   org.springframework.security.oauth2: DEBUG
 #   org.springframework.boot.autoconfigure: DEBUG
