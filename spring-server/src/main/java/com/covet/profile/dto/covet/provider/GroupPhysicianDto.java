package com.covet.profile.dto.covet.provider;

import com.covet.profile.persistence.compositeKey.GroupPhysicianKey;
import com.covet.profile.persistence.model.GroupProvider;
import com.covet.profile.persistence.model.Physician;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.util.Date;
import java.util.Optional;

@JsonSerialize(as = ImmutableGroupPhysicianDto.class)
@JsonDeserialize(as = ImmutableGroupPhysicianDto.class, builder = ImmutableGroupPhysicianDto.Builder.class)
@Value.Immutable
public interface GroupPhysicianDto {

    @Value.Parameter
    @JsonProperty
    GroupPhysicianKey getGroupPhysicianId();

    @Value.Parameter
    @JsonProperty
    Date getCreatedDate();

    @Value.Parameter
    @JsonProperty
    Date getUpdatedDate();

    @Value.Parameter
    @JsonProperty
    Boolean getIsInGroup();

    @Value.Parameter
    @JsonProperty
    Optional<GroupProvider> getGroup();

    @Value.Parameter
    @JsonProperty
    Optional<Physician> getPhysician();
}
