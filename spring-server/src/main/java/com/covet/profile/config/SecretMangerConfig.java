package com.covet.profile.config;

import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueRequest;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueResponse;

@Component
public class SecretMangerConfig {

    @Value("${spring.cloud.aws.s3.region}")
    private String region;

    private SecretsManagerClient getSecretsManagerClient() {
        SecretsManagerClient client = SecretsManagerClient.builder()
                .region(Region.of(region))
                .build();
        return client;
    }

    public Map<String, String> getSecrets(String secretName) throws Exception {
        GetSecretValueRequest getSecretValueRequest = GetSecretValueRequest.builder()
                .secretId(secretName)
                .build();

        GetSecretValueResponse valueResponse = getSecretsManagerClient().getSecretValue(getSecretValueRequest);
        String rawSecretString = valueResponse.secretString();
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, String> properties = objectMapper.readValue(rawSecretString,
                new TypeReference<Map<String, String>>() {
                });
        return properties;
    }

}
