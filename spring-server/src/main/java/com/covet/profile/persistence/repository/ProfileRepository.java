package com.covet.profile.persistence.repository;


import com.covet.profile.dto.location.ICoordinate;
import com.covet.profile.persistence.model.Profile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.UUID;

public interface ProfileRepository extends JpaRepository<Profile, UUID> {

    @Modifying
    @Query(value = """
            update Profile p set p.latitude = :lat, p.longitude = :lng where p.cognitoId = :id
            """)
    void updateCoordinate(@Param("id") UUID id, @Param("lat") Double lat, @Param("lng") Double lng);

    @Query(value = """
            select p.latitude, p.longitude from profile p where p.cognito_id = :id
            """, nativeQuery = true)
    ICoordinate getCoordinate(@Param("id") UUID id);
}