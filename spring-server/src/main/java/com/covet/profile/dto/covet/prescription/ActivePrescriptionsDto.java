package com.covet.profile.dto.covet.prescription;

import com.covet.profile.dto.covet.pharmacy.PharmacyDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.net.URL;
import java.util.Date;
import java.util.Optional;

@JsonSerialize(as = ImmutableActivePrescriptionsDto.class)
@JsonDeserialize(as = ImmutableActivePrescriptionsDto.class, builder = ImmutableActivePrescriptionsDto.Builder.class)
@Value.Immutable
public interface ActivePrescriptionsDto {

    @Value.Parameter
    @JsonProperty
    String getPrescriptionName();

    @Value.Parameter
    @JsonProperty("prescriptionCode")
    String getPrescriptionCode();

    @Value.Parameter
    @JsonProperty("prescriptionImgURL")
    Optional<URL> getPrescriptionImgURL();

    @Value.Parameter
    @JsonProperty("quantity")
    int getQuantity();

    @Value.Parameter
    @JsonProperty("form")
    String getForm();

    @Value.Parameter
    @JsonProperty("strength")
    String getStrength();

    @Value.Parameter
    @JsonProperty("refills")
    long getRefills();

    @Value.Parameter
    @JsonProperty("initRefillDate")
    Optional<Date> getInitRefillDate();

    @Value.Parameter
    @JsonProperty("nextRefillDate")
    Optional<Date> getNextRefillDate();

    @Value.Parameter
    @JsonProperty("prescriber")
    String getPrescriber();

    @Value.Parameter
    @JsonProperty("pharmacy")
    Optional<PharmacyDto> getPharmacy();

    @Value.Parameter
    @JsonProperty("cost")
    Double getCost();
}