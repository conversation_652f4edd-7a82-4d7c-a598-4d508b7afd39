package com.covet.profile.persistence.specification;

import com.covet.profile.persistence.compositeKey.PrescriptionID;
import com.covet.profile.persistence.model.Prescription;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Optional;
import java.util.UUID;

public class PrescriptionSpec {
    public static Specification<Prescription> prescriptionByConditions(Optional<String> keyword, UUID patientID) {
        return (root, query, builder) -> {

            Predicate patientIdPredicate = builder.equal(root.get("prescriptionId").get("patientID"), patientID);

            if (keyword.isPresent() && !keyword.get().isEmpty()) {
                String keywordLowerCase = keyword.get().toLowerCase();
                Field[] fields = PrescriptionID.class.getDeclaredFields();

                Predicate[] fieldPredicates = Arrays.stream(fields)
                        .filter(field -> !field.getName().equals("patientID"))
                        .map(field -> {
                            Path<String> fieldPath = root.get("prescriptionId").get(field.getName());
                            return builder.like(builder.lower(fieldPath), "%" + keywordLowerCase + "%");
                        })
                        .toArray(Predicate[]::new);

                Predicate nameOrCodePredicate = builder.or(fieldPredicates);

                return builder.and(patientIdPredicate, nameOrCodePredicate);
            }

            return patientIdPredicate;
        };
    }

    public static Specification<Prescription> prescriptionByKeyword(
            Optional<String> keyword) {
        return (root, query, builder) -> {
            if (!keyword.isPresent()) {
                return null;
            }
            return builder.like(
                    builder.lower(root.get("prescriptionId").get("prescriptionName")),
                    "%" + keyword.get().toLowerCase() + "%");
        };
    }

}
