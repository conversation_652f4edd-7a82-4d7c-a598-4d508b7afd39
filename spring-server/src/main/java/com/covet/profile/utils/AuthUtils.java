package com.covet.profile.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.AuthorizationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

import java.nio.charset.StandardCharsets;
import java.util.UUID;

public class AuthUtils {
    private AuthUtils() {
    };

    // TODO: Now auth0 id converted to UUID rename not cognito
    public static UUID getCognitoId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (!(authentication instanceof JwtAuthenticationToken)) {
            throw new AuthorizationServiceException("Unauthorized - No JWT found");
        }

        Jwt jwt = (Jwt) authentication.getPrincipal();
        String auth0UserId = jwt.getClaimAsString("sub");
        if (StringUtils.isBlank(auth0UserId)) {
            throw new AuthorizationServiceException("Unauthorized - 'sub' claim is missing or blank");
        }

        return UUID.nameUUIDFromBytes(auth0UserId.getBytes(StandardCharsets.UTF_8));

        // var principal = (Jwt)
        // SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        // var cognitoId = principal.getClaims().get("sub");
        // if (!(cognitoId instanceof String) || StringUtils.isBlank((String)
        // cognitoId)) {
        // throw new AuthorizationServiceException("Unauthorized");
        // }
        // return UUID.fromString((String) cognitoId);

    }

    public static String getAuth0Id() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (!(authentication instanceof JwtAuthenticationToken)) {
            throw new AuthorizationServiceException("Unauthorized - No JWT found");
        }

        Jwt jwt = (Jwt) authentication.getPrincipal();
        String auth0UserId = jwt.getClaimAsString("sub");
        if (StringUtils.isBlank(auth0UserId)) {
            throw new AuthorizationServiceException("Unauthorized - 'sub' claim is missing or blank");
        }

        return auth0UserId;

    }
}
