package com.covet.profile.clients.vba.VBADtos.IDcard;

import com.covet.profile.clients.vba.VBADtos.IDcard.Spending.VBASpendingDto;
import org.immutables.value.Value;

import com.covet.profile.clients.vba.VBADtos.IDcard.Copays.VBACopayDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonSerialize(as = ImmutableVBAIDcardDto.class)
@JsonDeserialize(as = ImmutableVBAIDcardDto.class, builder = ImmutableVBAIDcardDto.Builder.class)
@Value.Immutable
public interface VBAIDcardDto {

    @Value.Parameter
    @JsonProperty
    String getIssued();

    @Value.Parameter
    @JsonProperty
    String getName();

    @Value.Parameter
    @JsonProperty
    String getSubscriberID();

    @Value.Parameter
    @JsonProperty
    String getMemberSeq();

    @Value.Parameter
    @JsonProperty
    VBACopayDto getCopays();

    @Value.Parameter
    @JsonProperty
    VBASpendingDto getSpending();

}
