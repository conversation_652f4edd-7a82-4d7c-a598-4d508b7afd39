package com.covet.profile.controller;

import com.covet.profile.dto.CapRxDto.drug.request.PriceDrugParamsDto;
import com.covet.profile.dto.CapRxDto.drug.response.AlternativeDrugsDto;
import com.covet.profile.dto.CapRxDto.drug.response.CapDrugPriceListResponseDto;
import com.covet.profile.dto.CapRxDto.drug.response.ConvertDrugListDto;
import com.covet.profile.dto.CapRxDto.drug.response.DrugListDto;
import com.covet.profile.dto.CapRxDto.pharmacy.*;
import com.covet.profile.persistence.repository.DrugRepository;
import com.covet.profile.service.CapitalRxService;
import com.covet.profile.systemEnum.ESortType;
import com.covet.profile.systemEnum.SysEnum;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.Parameter;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Validated
@RestController
@RequestMapping("/api/capital-rx")
public class CapitalRxController {

    private final CapitalRxService capitalRxService;
    private final DrugRepository drugRepository;

    @Autowired
    public CapitalRxController(CapitalRxService capitalRxService, DrugRepository drugRepository) {
        this.capitalRxService = capitalRxService;
        this.drugRepository = drugRepository;
    }

    @GetMapping("/drug/{drugId}")
    public ResponseEntity<AlternativeDrugsDto> getDrugById(@Valid @PathVariable("drugId") int drugId) {
        AlternativeDrugsDto result = capitalRxService.getDrugById(drugId);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @GetMapping("/drugs-new")
    public ResponseEntity<ConvertDrugListDto> getDrugList(@Parameter @Size(min = 3) @Nullable String textSearch,
            @Parameter @Min(0) int pageNumber) {
        ConvertDrugListDto result = capitalRxService.getDrugList(textSearch, pageNumber);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @GetMapping("/drugs-cache")
    public Page<String> getDrugFromDatabase(@Parameter @Size(min = 3) @Nullable String textSearch,
            @Parameter @Min(0) int pageNumber, @Parameter @Min(5) @Max(10) int pageSize) {
        var pageable = PageRequest.of(pageNumber, pageSize);
        return drugRepository.getDrugString(textSearch, pageable);
    }

    //Separate to prevent crash - will be removed later
    @GetMapping("/drugs")
    public ResponseEntity<DrugListDto> getDrugListOld(@Parameter @Size(min = 3) String textSearch,
                                                      @Parameter @Min(0) int pageNumber)
            throws JsonProcessingException {
        DrugListDto result = capitalRxService.getDrugListOld(textSearch, pageNumber);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PostMapping("/location/geolocate")
    public ResponseEntity<CapLocationResDto> getLocationGeolocate(
            @Valid @RequestBody final CapLocationReqDto capLocationDto)
            throws JsonProcessingException {
        CapLocationResDto result = capitalRxService.getLocation(capLocationDto);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @GetMapping("/drug/prices/list")
    public ResponseEntity<CapDrugPriceListResponseDto> getDrugPrice(
            @Valid @ParameterObject final PriceDrugParamsDto priceDrugParamsDto) {
        CapDrugPriceListResponseDto result = capitalRxService.getDrugPrice(priceDrugParamsDto);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @GetMapping("/pharmacy/list")
    public ResponseEntity<JsonNode> getPharmacies(@Parameter Optional<SysEnum.ESwitch> allPharmacies,
            @Parameter ESortType distance,
            @Parameter Optional<String> closestToAddress,
            @RequestParam(value = "243 = retail network, 38 = mail order network, 39 = preferred network") Optional<List<Integer>> networkIds,
            @Parameter Optional<Integer> pageNumber, @Parameter Optional<Integer> pageSize) {
        DistanceReqBodyDto distanceReqBodyDto = new DistanceReqBodyDto();
        distanceReqBodyDto.setDistance(distance.getValue());
        List<DistanceReqBodyDto> listDistanceReqBodyDto = new ArrayList<>();
        listDistanceReqBodyDto.add(distanceReqBodyDto);
        PharmacyReqBodyDto pharmacyReqBodyDto = new PharmacyReqBodyDto();
        pharmacyReqBodyDto.setAllPharmacy(allPharmacies.isPresent() ? allPharmacies.get().getValue() : null);
        pharmacyReqBodyDto.setClosestToAddress(closestToAddress.isPresent() ? closestToAddress.get() : null);
        pharmacyReqBodyDto.setOrderBy(listDistanceReqBodyDto);
        pharmacyReqBodyDto.setNetworkIds(networkIds.isPresent() ? networkIds.get() : null);
        pharmacyReqBodyDto.setPageNumber(pageNumber.isPresent() ? pageNumber.get() : 0);
        pharmacyReqBodyDto.setResultsPerPage(pageSize.isPresent() ? pageSize.get() : 20);

        JsonNode root = capitalRxService.getPharmacies(pharmacyReqBodyDto);

        return new ResponseEntity<>(root, HttpStatus.OK);
    }

    @GetMapping("/pharmacy/{providerId}")
    public ResponseEntity<CapPharmacyDto> getPharmacyById(@Valid @PathVariable("providerId") String providerId) {
                CapPharmacyDto result = capitalRxService.getPharmacyById(providerId);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

}