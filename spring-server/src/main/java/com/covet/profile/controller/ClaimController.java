package com.covet.profile.controller;

import com.covet.profile.service.ClaimService;
import com.covet.profile.clients.vba.VBAService.VBAClaimService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RestController
@RequestMapping("/api")
public class ClaimController {
    private ClaimService claimService;

    private VBAClaimService vbaClaimService;

    @Autowired
    public ClaimController(ClaimService claimService, VBAClaimService vbaClaimService) {
        this.claimService = claimService;
        this.vbaClaimService = vbaClaimService;
    }

}
