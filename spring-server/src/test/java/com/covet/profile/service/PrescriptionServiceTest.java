package com.covet.profile.service;

import com.covet.profile.dto.covet.prescription.ImmutablePrescriptionDto;
import com.covet.profile.persistence.compositeKey.ClaimPrescriptionID;
import com.covet.profile.persistence.compositeKey.PrescriptionID;
import com.covet.profile.persistence.compositeKey.PrescriptionPharmacyID;
import com.covet.profile.dto.covet.prescription.PrescriptionDto;
import com.covet.profile.persistence.model.*;
import com.covet.profile.persistence.repository.*;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.mock.web.MockMultipartFile;

import java.net.URL;
import java.util.Date;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

@SpringBootTest
public class PrescriptionServiceTest {
    @InjectMocks
	private PrescriptionService prescriptionService;

	@Mock
	private PrescriptionRepository prescriptionRepository;

	@Mock
	private PriorAuthorRepository priorAuthorRepository;

	@Mock
	private ClaimPrescriptionRepository claimPrescriptionRepository;

	@Mock
	private PrescriptionPharmacyRepository prescriptionPharmacyRepository;

	@Mock
	private PictureRepository pictureRepository;

	@Mock
	private PhysicianRepository physicianRepository;

	@Mock
	private S3Service s3Service;

	@MockBean
	private CapitalRxService capitalRxService;

	@Test
	public void getNumOfRefills_ValidParams_Success() {
		// Arrange
		PrescriptionID prescriptionId = new PrescriptionID("TestMedicine", "TestCode", UUID.randomUUID());
		long expectedRefills = 5L;

		// Assume that any specification passed to count will result in the expectedRefills
		when(priorAuthorRepository.count((Specification<PriorAuthor>) any())).thenReturn(expectedRefills);

		// Act
		long actualRefills = prescriptionService.getNumOfRefills(prescriptionId);

		// Assert
		assertEquals(expectedRefills, actualRefills, "The number of refills should match the expected value.");
	}

	@Test
	public void getPrescriptionById_ValidParams_Success() {
        // Arrange
        PrescriptionID prescriptionId = new PrescriptionID("TestName", "TestCode", UUID.randomUUID());
        Prescription prescription = new Prescription();
        prescription.setPrescriptionId(prescriptionId);
        prescription.setQuantity(1);
        prescription.setForm("Tablet");
        prescription.setStrength("500mg");
        prescription.setDrugID(123);

        PrescriptionDto expectedDto = ImmutablePrescriptionDto.builder()
                .prescriptionId(Optional.of(prescriptionId))
                .quantity(1)
                .form("Tablet")
                .strength("500mg")
                .drugID(123)
                .build();

        when(prescriptionRepository.findByPrescriptionId(any(PrescriptionID.class)))
                .thenReturn(Optional.of(prescription));

        // Act
        Optional<PrescriptionDto> result = prescriptionService.getPrescriptionById(prescriptionId);

        // Assert
        assertTrue(result.isPresent(), "Expected prescription to be present");
        assertEquals(expectedDto, result.get(), "The returned PrescriptionDto does not match the expected object");
	}

	@Test
	void createOrUpdatePrescription_ValidParams_Success() {
		// Arrange
		PrescriptionDto prescriptionDto = ImmutablePrescriptionDto.builder()
				.prescriptionId(Optional.of(new PrescriptionID("TestName", "TestCode", UUID.randomUUID())))
				.quantity(1)
				.form("Tablet")
				.strength("500mg")
				.drugID(123)
				.build();

		Prescription expectedPrescription = new Prescription();
		expectedPrescription.setPrescriptionId(prescriptionDto.getPrescriptionId().orElse(null));
		expectedPrescription.setQuantity(prescriptionDto.getQuantity());
		expectedPrescription.setForm(prescriptionDto.getForm());
		expectedPrescription.setStrength(prescriptionDto.getStrength());
		expectedPrescription.setDrugID(prescriptionDto.getDrugID());

		when(prescriptionRepository.save(any(Prescription.class))).thenReturn(expectedPrescription);

		// Act
		Prescription result = prescriptionService.createOrUpdatePrescription(prescriptionDto);

		// Assert
		assertEquals(expectedPrescription, result, "The saved Prescription does not match the expected result");
	}

	@Test
	void postImageForPrescription_ValidParams_Success() throws Exception {
		// Arrange
		MockMultipartFile file = new MockMultipartFile("image", "test.jpg", "image/jpeg", "test image content".getBytes());
		PrescriptionID prescriptionId = new PrescriptionID("TestName", "TestCode", UUID.randomUUID());
		String imageUUID = "expected-transformed-uuid"; // Adjust based on expected transformation
		URL expectedImageUrl = new URL("http://example.com/test.jpg");

		// Use argument matchers to ensure flexibility in matching the method call
		when(s3Service.getPresignedURL(anyString())).thenReturn(expectedImageUrl);

		// Act
		URL result = prescriptionService.postImageForPrescription(file, prescriptionId);

		// Assert
		assertEquals(expectedImageUrl.toString(), result != null ? result.toString() : null,
				"The URL returned by the method does not match the expected URL.");
	}

	@Test
	void getPrescriptionClaimDetail_ValidParams_Success() {
		// Arrange
		UUID patientId = UUID.randomUUID();
		String claimCode = "validClaimCode";
		String prescriptionName = "PrescriptionName";
		String prescriptionCode = "PrescriptionCode";
		String pharmacyId = "pharmacy123"; // Adjusting to String type for PharmacyID

		ClaimPrescription mockClaimPrescription = new ClaimPrescription();
		Prescription mockPrescription = new Prescription();
		PrescriptionID prescriptionID = new PrescriptionID(prescriptionName, prescriptionCode,patientId);


		Claim mockClaim = new Claim();
		Pharmacy mockPharmacy = new Pharmacy();
		Physician mockPhysician = new Physician();
		Profile mockProfile = new Profile();

		mockPrescription.setPrescriptionId(prescriptionID);
		mockPharmacy.setPharmacyId(pharmacyId);
		mockClaim.setClaimCode(claimCode);
		mockPhysician.setProfile(mockProfile);
		mockProfile.setFirstName("Doc");
		mockProfile.setLastName("Holiday");

		mockClaimPrescription.setPrescription(mockPrescription);
		mockClaimPrescription.setClaim(mockClaim);
		mockClaimPrescription.setPharmacy(mockPharmacy);
		mockClaimPrescription.setInitRefillDate(new Date());
		mockClaimPrescription.setNextRefillDate(new Date());
		mockClaim.setPhysician(mockPhysician);

		PrescriptionPharmacy mockPrescriptionPharmacy = new PrescriptionPharmacy();
		mockPrescriptionPharmacy.setCost(100.00);

		when(claimPrescriptionRepository.findById(any(ClaimPrescriptionID.class))).thenReturn(Optional.of(mockClaimPrescription));
		when(prescriptionPharmacyRepository.findById(any(PrescriptionPharmacyID.class))).thenReturn(Optional.of(mockPrescriptionPharmacy));

		// Act
		Map<String, Object> result = prescriptionService.getPrescriptionClaimDetail(claimCode, prescriptionName, prescriptionCode, patientId);

		// Assert
		assertNotNull(result);
	}

//	@Test
//	void getPrescriptionDetail() throws Exception {
//		// Arrange
//		String prescriptionName = "TestPrescription";
//		String prescriptionCode = "12345";
//		UUID patientId = UUID.randomUUID();
//
//		Prescription mockPrescription = new Prescription();
//		// Configure your mockPrescription as needed
//		URL mockPrescriptionImgUrl = new URL("http://example.com/prescription.jpg");
//
//		when(prescriptionRepository.findByPrescriptionIdPrescriptionNameAndPrescriptionIdPrescriptionCodeAndPrescriptionIdPatientID(prescriptionName, prescriptionCode, patientId))
//				.thenReturn(Optional.of(mockPrescription));
//
//		// Assuming getImgByNdc and getDefaultUrlMap are part of the service and can be mocked/spied if needed
//		// Example for mocking a direct service method, adjust based on actual implementation
//		when(prescriptionService.getImgByNdc(prescriptionCode)).thenReturn(mockPrescriptionImgUrl);
//
//		// Act
//		PrescriptionDetailDto result = prescriptionService.getPrescriptionDetail(prescriptionName, prescriptionCode, patientId);
//
//		// Assert
//		assertNotNull(result, "PrescriptionDetailDto should not be null");
//		assertEquals(mockPrescription, result.getPrescription(), "Prescription does not match expected value");
//		assertTrue(result.getIsVerified(), "isVerified should be true");
//		assertTrue(result.getIsPArequired(), "isPARequired should be true");
//		assertEquals(Optional.ofNullable(mockPrescriptionImgUrl), result.getPrescriptionImgUrl(), "Prescription image URL does not match expected value");
//	}
}
