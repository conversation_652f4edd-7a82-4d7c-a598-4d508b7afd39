# README #

This README would normally document whatever steps are necessary to get your application up and running.

### What is this repository for? ###
Covet Health backend. currently 2 clusters dev / prod

## Cluster managment ###

# Kubernetes Organization #
Clusters
A Kubernetes cluster is a collection of nodes, where the workloads (containers) are run. It consists of a control plane that manages the overall state of the cluster and worker nodes that run the applications.

Nodes
Nodes are virtual or physical machines that run containers. A node can host multiple pods, and each pod can host multiple containers.

Pods
Pods are the smallest deployable units in Kubernetes and are used to group one or more containers. Containers within the same pod share the same network IP, port space, and storage, allowing them to communicate easily and access shared data.

Containers
Containers are lightweight and standalone executables that package application code and dependencies. They run consistently across different computing environments.

Services
Services are an abstraction that defines a logical set of pods and a policy to access them. They enable external access to pods and can distribute load across a set of replicas.

Relationships
Cluster encompasses the entire system, including the master and worker nodes.
Nodes run the containers and are part of the cluster.
Pods run on nodes and contain one or more containers.
Containers are encapsulated within pods and are the actual running instances of the application.
Services allow communication with pods, enabling interaction with the application.

## Prerequisites

- **Docker**: Basic understanding of Docker.
- **AWS CLI**: Installed and configured with the necessary permissions.
- **EKS**: Amazon EKS installed.
- **eksctl**: Installed (optional, can be used as an alternative to AWS CLI for cluster management).

## Essential kubectl Commands

### Get Resources

Retrieve information about the different resources:

- Pods: `kubectl get pods`
- Services: `kubectl get services`
- Nodes: `kubectl get nodes`

### Describe Resources

Provide detailed information about the resource:

- Pod: `kubectl describe pod <pod-name>`
- Service: `kubectl describe service <service-name>`
- Node: `kubectl describe node <node-name>`

### View Logs

View the logs of a specific container within a pod:

`kubectl logs <pod-name> <container-name>`

## Working with EKS Clusters

### Create a Cluster With EKSCTL CLI

eksctl create cluster --name <cluster-name> --region <region>


# add cluster to kubeconfig #
eksctl utils write-kubeconfig --region region_name --cluster cluster_name

# Switch Between Clusters #
#see the avaliable kube contexts
kubectl config get-contexts

aws eks update-kubeconfig --name <cluster-name>

# or #

eksctl create cluster --name <cluster-name> --region <region>


# add url to cluster # 
kubectl get ingress my-ingress -o=jsonpath='{.status.loadBalancer.ingress[0].hostname}'


## Kubernetes Deployments

### Applying Deployments

Deployments in Kubernetes are a declarative way to ensure that the desired number of pod replicas are running at any given time. You can create and manage a deployment using a YAML or JSON file. Here's how to apply a deployment:

**Create a YAML File (e.g., deployment.yaml):**

   ```yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: my-app
   spec:
     replicas: 3
     selector:
       matchLabels:
         app: my-app
     template:
       metadata:
         labels:
           app: my-app
       spec:
         containers:
         - name: my-container
           image: my-image:1.0
    ```
Apply the Deployment: kubectl apply -f deployment.yaml

Update the Deployment: If you make changes to the deployment file, you can apply the updates with the same command.

Delete the Deployment: kubectl delete -f deployment.yaml