package com.covet.profile.data;

import lombok.NoArgsConstructor;

import java.time.Duration;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

@NoArgsConstructor
public class ProviderAvailableTimes {
    public static String start = "06:00";
    public static String finish = "22:00";

    public static int subIntervalCount = 32;

    public List<LocalTime> getDefaultAvailableTimes() {
        List<LocalTime> result = new ArrayList<>();

        LocalTime startTime = LocalTime.parse(start);
        LocalTime endTime = LocalTime.parse(finish);

        Duration totalTime = Duration.between(startTime, endTime);
        Duration subIntervalLength = totalTime.dividedBy(subIntervalCount);

        LocalTime currentTime = startTime;
        for (int i = 0; i < subIntervalCount; i++) {
            result.add(currentTime);
            currentTime = currentTime.plus(subIntervalLength);
        }

        return result;
    }
}
