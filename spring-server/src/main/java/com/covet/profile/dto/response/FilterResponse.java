package com.covet.profile.dto.response;

import com.covet.profile.dto.covet.provider.GroupProviderFilterOptionsDto;
import com.covet.profile.dto.covet.provider.ProviderFilterOptionsDto;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Builder
@Data
public class FilterResponse implements Serializable {
    GroupProviderFilterOptionsDto groupProvider;
    ProviderFilterOptionsDto provider;
}
