
create table physician_rating  (
                                   physician_id uuid not null,
                                   average_rating float check (average_rating between 0 and 5),
                                   total_reviews int not null,
                                   primary key (physician_id)
);

-- update average rating everytime a patient review and rating physician
CREATE OR REPLACE FUNCTION update_average_rating() RETURNS TRIGGER AS $update_average_rating$
BEGIN
    IF NEW.rating IS NOT NULL THEN
        INSERT INTO physician_rating VALUES (NEW.physician_id, (SELECT AVG(rating) FROM review WHERE physician_id = NEW.physician_id), 1)
        ON CONFLICT (physician_id) DO UPDATE SET (average_rating, total_reviews ) =  ((SELECT AVG(rating) FROM review WHERE physician_id = NEW.physician_id), ((SELECT COUNT(*) FROM review WHERE physician_id = NEW.physician_id) + 1))
                                      WHERE NEW.physician_id = physician_rating.physician_id;
        RAISE NOTICE 'updated physician average rating';
    END IF;
RETURN NULL; -- result is ignored since this is an AFTER trigger
END;
$update_average_rating$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER update_average_rating
    AFTER INSERT OR UPDATE OR DELETE ON review
    FOR EACH ROW EXECUTE FUNCTION update_average_rating();

