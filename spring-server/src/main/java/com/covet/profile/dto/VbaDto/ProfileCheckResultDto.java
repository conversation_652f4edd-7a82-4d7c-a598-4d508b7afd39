package com.covet.profile.dto.VbaDto;

public class ProfileCheckResultDto {
    
    private String email;
    private String subscriberID;
    private String memberSequence;

    // Default constructor
    public ProfileCheckResultDto() {
    }

    // Constructor with all fields
    public ProfileCheckResultDto(String email, String subscriberID, String memberSequence) {
        this.email = email;
        this.subscriberID = subscriberID;
        this.memberSequence = memberSequence;
    }

    // Getters and Setters
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSubscriberID() {
        return subscriberID;
    }

    public void setSubscriberID(String subscriberID) {
        this.subscriberID = subscriberID;
    }

    public String getMemberSequence() {
        return memberSequence;
    }

    public void setMemberSequence(String memberSequence) {
        this.memberSequence = memberSequence;
    }

    // toString method for debugging purposes
    @Override
    public String toString() {
        return "ProfileCheckResultDto{" +
               "email='" + email + '\'' +
               ", subscriberID='" + subscriberID + '\'' +
               ", memberSequence='" + memberSequence + '\'' +
               '}';
    }
}
