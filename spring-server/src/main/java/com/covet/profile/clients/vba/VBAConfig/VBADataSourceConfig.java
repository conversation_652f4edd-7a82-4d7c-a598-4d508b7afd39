package com.covet.profile.clients.vba.VBAConfig;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.retry.annotation.EnableRetry;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueRequest;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueResponse;
import lombok.extern.slf4j.Slf4j;
import javax.sql.DataSource;
import java.util.Map;

@Configuration
@EnableRetry
@Slf4j
public class VBADataSourceConfig {

    @Value("${spring.third-party.vba.databaseSecret}")
    private String secretName;

    private Region region = Region.of("us-east-1");


    public String getSecret() {
        // Create a Secrets Manager client
        SecretsManagerClient client = SecretsManagerClient.builder()
                .region(region)
                .build();

        GetSecretValueRequest getSecretValueRequest = GetSecretValueRequest.builder()
                .secretId(secretName)
                .build();

        GetSecretValueResponse getSecretValueResponse;

        try {
            getSecretValueResponse = client.getSecretValue(getSecretValueRequest);
        } catch (Exception e) {
            throw e;
        }

        String secret = getSecretValueResponse.secretString();

        return secret;

        // Your code goes here.
    }

    @Bean
    public DataSource vbaDataSource() throws JsonProcessingException {
        String driverClassName = "com.microsoft.sqlserver.jdbc.SQLServerDriver";

        var secretValue = getSecret();

        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, String> properties = objectMapper.readValue(secretValue, new TypeReference<Map<String,String>>(){});

        String url = properties.get("vba-db-test-url");
        String username = properties.get("vba-db-test-username");
        String password = properties.get("vba-db-test-password");
        log.info("vba-db-test-url: {}\n", url);
        log.info("vba-db-test-username: {}\n", username);

        return DataSourceBuilder.create()
                .driverClassName(driverClassName)
                .url(url)
                .username(username)
                .password(password)
                .build();
    }
}
