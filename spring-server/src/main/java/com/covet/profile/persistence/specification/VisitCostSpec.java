package com.covet.profile.persistence.specification;

import java.util.UUID;

import org.springframework.data.jpa.domain.Specification;

import com.covet.profile.persistence.model.VisitCost;

public class VisitCostSpec {
    public static Specification<VisitCost> getVisitCostByPhysicianId(UUID physicianId) {
        return (root, query, builder) -> {
            return builder.equal(root.get("physicianId"), physicianId);
        };
    }
}