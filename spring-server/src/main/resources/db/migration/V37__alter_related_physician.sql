DO $$
BEGIN
  IF NOT EXISTS(SELECT *
    FROM pg_type
    WHERE typname='source')
  THEN
      CREATE TYPE SOURCE AS ENUM ('vba', 'npi', 'other');
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS(SELECT *
    FROM information_schema.columns
    WHERE table_name='physician' and column_name='source')
  THEN
      ALTER TABLE "public"."physician" ADD source SOURCE;
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS(SELECT *
    FROM information_schema.columns
    WHERE table_name='group_provider' and column_name='source')
  THEN
      ALTER TABLE "public"."group_provider" ADD source SOURCE;
  END IF;
END $$;