package com.covet.profile.service;

import com.covet.profile.converter.notification.PrescriptionSchedulerConverter;
import com.covet.profile.persistence.model.Notification;
import com.covet.profile.persistence.repository.NotificationRepository;
import com.covet.profile.utils.FirebaseNotiModelUtils;
import com.covet.profile.utils.JsonUtils;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.FirebaseMessagingException;
import com.google.firebase.messaging.Message;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class FirebaseMessagingService {

    private final FirebaseMessaging firebaseMessaging;
    private final NotificationRepository notificationRepository;
    private final PrescriptionSchedulerConverter prescriptionSchedulerConverter;

    /**
     * Executes pushing messages to firebase with batches.
     * Store the the pushed notifications in to the notification table.
     *
     * @params List<String> registrationTokenList
     * @params List<NotificationTemplate> templateList
     * @params List<Notification> notificationList
     * @see messages are pushed up to firebase
     */
    public void sendNotification(List<String> registrationTokenList, List<Notification> notificationList) {

        List<UUID> successMessages = new ArrayList<>();
        if (!registrationTokenList.isEmpty()) {
            for (int i = 0; i < registrationTokenList.size(); i++) {
                var token = registrationTokenList.get(i);
                var medicineRes = prescriptionSchedulerConverter.convertToMedicineTemplateRes(notificationList.get(i));
                var msg = FirebaseNotiModelUtils.buildPrescriptionNotiMsg(medicineRes, token);
                try {
                    firebaseMessaging.send(msg);
                    successMessages.add(notificationList.get(i).getNotificationId());
                } catch (FirebaseMessagingException e) {
                    log.error("sendNotification: {}", e.getMessage());
                }
            }

            List<Notification> sentNotificationList = notificationRepository.findAllById(successMessages);
            if (!sentNotificationList.isEmpty()) {
                sentNotificationList.forEach(e -> e.setIsPushed(true));
                notificationRepository.saveAll(sentNotificationList);
            }
        }
    }

    public void testPushNoti(String token, Object body) {
        var notification = com.google.firebase.messaging.Notification.builder()
                .setTitle("Covet Health")
                .setBody("It's time to take some prescriptions")
                .build();
        var msg = Message.builder()
                .setToken(token)
                .setNotification(notification)
                .putData("body", JsonUtils.toJson(body))
                .putData("type", "active_prescription_scheduler")
                .build();

        try {
            firebaseMessaging.send(msg);
        } catch (FirebaseMessagingException e) {
            log.error("sendNotification: {}", e.getMessage());
        }
    }
}