package com.covet.profile.clients.vba.VBADtos.Member;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.Date;

import org.immutables.value.Value;
import org.springframework.lang.Nullable;

@JsonSerialize(as = ImmutableVBAMemberDto.class)
@JsonDeserialize(as = ImmutableVBAMemberDto.class, builder = ImmutableVBAMemberDto.Builder.class)
@Value.Immutable
public interface VBAMemberDto {

    @Value.Parameter
    @JsonProperty
    String getMemberSeq();

    @Value.Parameter
    @JsonProperty
    String getFirstName();

    @Value.Parameter
    @JsonProperty
    @Nullable
    String getMiddleName();

    @Value.Parameter
    @JsonProperty
    String getLastName();

    @Value.Parameter
    @JsonProperty
    String getRelationship();

    @Value.Parameter
    @JsonProperty
    Date getBirthDate();
}
