package com.covet.profile.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

public class ProviderProfileCheckDto {

    @NotBlank(message = "Claim ID is required")
    private String claimID;

    @NotBlank(message = "TIN is required")
    @Pattern(regexp = "^\\d{9}$", message = "Each TIN must be a valid 9-digit number")
    private String tin;

    // Getters

    public String getClaimID() {
        return claimID;
    }

    public String getTin() {
        return tin;
    }

    // Setters
    public void setClaimID(String claimID) {
        this.claimID = claimID;
    }

    public void setTins(String tin) {
        this.tin = tin;
    }
}
