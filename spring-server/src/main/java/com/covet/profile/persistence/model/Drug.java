package com.covet.profile.persistence.model;

import lombok.*;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "drug")
@FieldNameConstants
public class Drug extends BaseEntity<UUID> {
    @Id
    @Column(name = "id")
    UUID id;

    @Column(name = "ndc")
    String ndc;

    @Column(name = "label_name")
    String labelName;

    @Column(name = "brand_name")
    String brandName;

    @Column(name = "generic_name")
    String genericName;

    @Column(name = "tier")
    String tier;

    @Column(name = "tier_original")
    String tierOriginal;

    @Column(name = "prior_authorization")
    String priorAuthorization;

    @Column(name = "quantity_limit")
    String quantityLimit;

    @Column(name = "quantity_limit_over_time")
    String quantityLimitOverTime;

    @Column(name = "step_therapy")
    String stepTherapy;

    @Column(name = "step_therapy_number")
    String stepTherapyNumber;

    @Column(name = "minimum_age_limit")
    String minimumAgeLimit;

    @Column(name = "maximum_age_limit")
    String maximumAgeLimit;

    @Column(name = "gender")
    String gender;

    @Column(name = "specialty")
    String specialty;

    @Column(name = "preferred_specialty")
    String preferredSpecialty;

    @Column(name = "low_cost_generic")
    String lowCostGeneric;

    @Column(name = "high_cost_generic")
    String highCostGeneric;

    @Column(name = "high_cost_brand")
    String highCostBrand;

    @Column(name = "affordable_care_act")
    String affordableCareAct;

    @Column(name = "preventive")
    String preventive;

    @Column(name = "package_size")
    String packageSize;

    @Column(name = "package_size_unit_of_measure")
    String packageSizeUnitOfMeasure;

    @Column(name = "package_quantity")
    String packageQuantity;

    @Column(name = "unit_dose_code")
    String unitDoseCode;

    @Column(name = "package_description_code")
    String packageDescriptionCode;

    @Column(name = "maintenance")
    String maintenance;

    @Column(name = "compound_kit")
    String compoundKit;

    @Column(name = "rx_cap_plus")
    String rxCapPlus;

    @Column(name = "opioid")
    String opioid;

    @Column(name = "strength_per_unit")
    String strengthPerUnit;

    @Column(name = "mme_conversion_factor")
    String mmeConversionFactor;

    @Column(name = "medical_drug")
    String medicalDrug;

    @Column(name = "rx4less")
    String rx4less;

    @Column(name = "drug_application_type")
    String drugApplicationType;

    @Column(name = "cms_labeler_code")
    String cmsLabelerCode;

    @Column(name = "manufacturer_name")
    String manufacturerName;

    @Column(name = "add_date")
    LocalDate addDate;

    @Column(name = "effective_date")
    LocalDate effectiveDate;

    @Column(name = "formulary_name")
    String formularyName;
}
