package com.covet.profile.utils;


import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class MessageUtilsTest {


    @Test
    public void givenMap_WhenUsingStream_ThenResultingStringIsCorrect() {

        Map<String, String> input = new HashMap<>();
        input.put("a", "AAA AAA");
        input.put("b", "BBB BBB");
        input.put("c", "CCC CCC");

        String expected = "{[a] AAA AAA, [b] BBB BBB, [c] CCC CCC}";
        String mapAsString = MessageUtils.convertWithStream(input);

        assertEquals(expected, mapAsString);
    }

}
