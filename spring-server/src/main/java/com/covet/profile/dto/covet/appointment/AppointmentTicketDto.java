package com.covet.profile.dto.covet.appointment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Data
@AllArgsConstructor
public class AppointmentTicketDto {
    private UUID physicianId;
    private String providerName;
    private String npi;
    private List<TimeRange> timeRanges;
    private boolean isVirtual;
    private boolean isInPerson;
    @Schema(description = "CHECKUP(0), PROCEDURE(1), FOLLOWUP(2), SCREENING(3), TELEMEDICINE(4),\n" +
            "        OTHER_VISIT(5);")
    private int appointmentType;
    private String description;
    Optional<UUID> appointmentId;

    @Data
    @AllArgsConstructor
    public static class TimeRange {
        @Schema(type = "string", format = "HH:mm", example = "09:30")
        @DateTimeFormat(pattern = "HH:mm")
        private LocalTime startTime;
        @Schema(type = "string", format = "HH:mm", example = "09:30")
        @DateTimeFormat(pattern = "HH:mm")
        private LocalTime endTime;
        private String dayOfWeek;
    }
}
