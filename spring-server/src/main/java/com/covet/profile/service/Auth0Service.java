package com.covet.profile.service;

import com.covet.profile.config.Auth0Properties;
import com.covet.profile.dto.Auth0UserDTO;
import com.covet.profile.clients.vba.VBADtos.Member.SubscriberMemberDto;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nimbusds.jose.shaded.json.JSONObject;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AuthorizationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.UUID;

@Service
public class Auth0Service {

    @Autowired
    private Auth0Properties auth0Properties;

    // private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private String accessToken;
    private LocalDateTime accessTokenExpiry;
    private final OkHttpClient httpClient;
    private final Logger log = LoggerFactory.getLogger(Auth0Service.class);

    public Auth0Service() {
        // this.restTemplate = restTemplate;
        this.objectMapper = new ObjectMapper();
        this.httpClient = new OkHttpClient();
    }

    private synchronized String getAccessToken() throws Exception {
        if (accessToken == null || LocalDateTime.now().isAfter(accessTokenExpiry)) {
            log.info("Refreshing Auth0 Management api!");
            refreshToken();
        }
        return accessToken;
    }

    private synchronized void refreshToken() throws Exception {
        String url = "https://" + auth0Properties.getDomain() + "/oauth/token";

        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json; charset=utf-8");
        JSONObject json = new JSONObject();
        json.put("client_id", auth0Properties.getClientId());
        json.put("client_secret", auth0Properties.getClientSecret());
        json.put("audience", auth0Properties.getAudience());
        json.put("grant_type", "client_credentials");

        RequestBody body = RequestBody.create(json.toString(), mediaType);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }

            String responseBody = response.body().string();
            JsonNode jsonResponse = objectMapper.readTree(responseBody);

            accessToken = jsonResponse.get("access_token").asText();
            int expiresIn = jsonResponse.get("expires_in").asInt();
            accessTokenExpiry = LocalDateTime.now().plusSeconds(expiresIn - 300L); // Subtracting 5 minutes for clock skew
            log.info("Refreshed token with expiration: " + accessTokenExpiry);
        }
    }


    public String createUser(Auth0UserDTO userDto) throws Exception {
        String token = getAccessToken();
        String url = "https://" + auth0Properties.getDomain() + "/api/v2/users";


        okhttp3.MediaType mediaType = okhttp3.MediaType.get("application/json; charset=utf-8");
        String userJson = objectMapper.writeValueAsString(userDto);
        RequestBody body = RequestBody.create(userJson, mediaType);

        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("Authorization", "Bearer " + token)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            return response.body().string();
        }
    }

    public void deleteUser(String userId) throws Exception {
        String token = getAccessToken();
        String url = "https://" + auth0Properties.getDomain() + "/api/v2/users/" + userId;


        Request request = new Request.Builder()
                .url(url)
                .delete()
                .addHeader("Authorization", "Bearer " + token)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to delete user. Code: " + response.code() + ", Body: " + response.body().string());
            }
        }
    }

    public String getUserEmail(String userId) throws Exception {
        String token = getAccessToken();
        String url = "https://" + auth0Properties.getDomain() + "/api/v2/users/" + java.net.URLEncoder.encode(userId, StandardCharsets.UTF_8);


        Request request = new Request.Builder()
                .url(url)
                .get()
                .addHeader("Authorization", "Bearer " + token)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to get user. Code: " + response.code() + ", Body: " + response.body().string());
            }

            String responseBody = response.body().string();
            JsonNode userNode = objectMapper.readTree(responseBody);
            return userNode.has("email") ? userNode.get("email").asText() : null;
        }
    }

    public SubscriberMemberDto getSubscriberMemberID(String userId) throws Exception {
        String token = getAccessToken();
        String url = "https://" + auth0Properties.getDomain() + "/api/v2/users/" + java.net.URLEncoder.encode(userId, StandardCharsets.UTF_8);


        Request request = new Request.Builder()
                .url(url)
                .get()
                .addHeader("Authorization", "Bearer " + token)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to get user. Code: " + response.code() + ", Body: " + response.body().string());
            }

            String responseBody = response.body().string();
            // System.out.println("RESPONSE BODY: "+ responseBody);
            JsonNode userNode = objectMapper.readTree(responseBody);
            JsonNode memberIdNode = userNode.path("user_metadata").path("member_id");

            if (memberIdNode.isMissingNode()) {
                throw new IllegalStateException("Member ID not found in user metadata");
            }

            String memberId = memberIdNode.asText();
            String subscriberId = memberId.substring(0, memberId.length() - 2);
            String memberSequence = memberId.substring(memberId.length() - 2);

            return new SubscriberMemberDto(subscriberId, memberSequence);
        }
    }

    public String getAuth0Id() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (!(authentication instanceof JwtAuthenticationToken)) {
            throw new AuthorizationServiceException("Unauthorized - No JWT found");
        }

        Jwt jwt = (Jwt) authentication.getPrincipal();
        String auth0UserId = jwt.getClaimAsString("sub");
        if (StringUtils.isBlank(auth0UserId)) {
            throw new AuthorizationServiceException("Unauthorized - 'sub' claim is missing or blank");
        }

        return auth0UserId;
    }

    public UUID getCognitoId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (!(authentication instanceof JwtAuthenticationToken)) {
            throw new AuthorizationServiceException("Unauthorized - No JWT found");
        }

        Jwt jwt = (Jwt) authentication.getPrincipal();
        String auth0UserId = jwt.getClaimAsString("sub");
        if (StringUtils.isBlank(auth0UserId)) {
            throw new AuthorizationServiceException("Unauthorized - 'sub' claim is missing or blank");
        }

        return UUID.nameUUIDFromBytes(auth0UserId.getBytes(StandardCharsets.UTF_8));

    }
}
