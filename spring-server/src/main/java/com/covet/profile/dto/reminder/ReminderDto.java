package com.covet.profile.dto.reminder;

import com.covet.profile.dto.PrescriptionSchedulerDto.NotificationDto;
import com.covet.profile.dto.PrescriptionSchedulerDto.NotificationTemplateDto;
import com.covet.profile.dto.SchedulerDto;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.UUID;

@Data
@Builder
public class ReminderDto {
    NotificationDto notification;
    SchedulerDto scheduler;
    NotificationTemplateDto template;
}
