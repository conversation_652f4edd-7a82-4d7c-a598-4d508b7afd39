package com.covet.profile.persistence.compositeKey;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;
import java.util.UUID;

@Embeddable
@Data
@NoArgsConstructor
public class GroupPhysicianKey implements Serializable {

    @Column(name = "group_id")
    UUID groupId;

    @Column(name = "physician_id")
    UUID physicianId;
}
