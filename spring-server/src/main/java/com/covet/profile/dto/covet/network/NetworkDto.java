package com.covet.profile.dto.covet.network;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.util.UUID;

@JsonSerialize(as = ImmutableNetworkDto.class)
@JsonDeserialize(as = ImmutableNetworkDto.class, builder = ImmutableNetworkDto.Builder.class)
@Value.Immutable
public interface NetworkDto {
    @Value.Parameter
    @JsonProperty
    UUID getId();

    @Value.Parameter
    @JsonProperty
    String getNetworkId();
}
