package com.covet.profile.dto.CapRxDto.drug;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import org.immutables.value.Value;
import org.springframework.lang.Nullable;

@JsonSerialize(as = ImmutableDrugListReqDto.class)
@JsonDeserialize(as = ImmutableDrugListReqDto.class, builder = ImmutableDrugListReqDto.Builder.class)
@Value.Immutable
public interface DrugListReqDto {

    @Value.Parameter
    @JsonProperty("search")
    @Nullable
    String getSearch();

    @Value.Parameter
    @JsonProperty("page_number")
    int getPageNumber();

}
