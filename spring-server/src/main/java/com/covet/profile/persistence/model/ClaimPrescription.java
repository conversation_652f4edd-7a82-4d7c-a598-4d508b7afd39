package com.covet.profile.persistence.model;

import com.covet.profile.persistence.compositeKey.ClaimPrescriptionID;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "claim_prescription")

public class ClaimPrescription implements Serializable {

    @EmbeddedId
    private ClaimPrescriptionID claimPrescriptionId;

    // relationship
    @JsonIgnore
    @ManyToOne(cascade = CascadeType.ALL)
    private Prescription prescription;

    // relationship
    @JsonIgnore
    @MapsId("claimCode")
    @JoinColumn(name = "claim_code")
    @ManyToOne(cascade = CascadeType.ALL)
    private Claim claim;

    // relationship
    @JsonIgnore
    @JoinColumn(name = "pharmacy_id", referencedColumnName = "pharmacy_id")
    @OneToOne(cascade = CascadeType.ALL)
    private Pharmacy pharmacy;

    @Column(name = "init_refill_date")
    private Date initRefillDate;

    @Column(name = "next_refill_date")
    private Date nextRefillDate;
}
