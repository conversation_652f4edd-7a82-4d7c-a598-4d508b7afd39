package com.covet.profile.dto.reminder;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;
import java.util.UUID;
@Data
@NoArgsConstructor
public class PhysicianProfileDto implements Serializable {
    UUID cognitoId;
    String firstName;
    String lastName;
    String middleName;
    String socialSecurityNumber;
    Date dateOfBirth;
    String address;
    String addressLine;
    String city;
    String state;
    String zipCode;
    Double latitude;
    Double longitude;
    UUID physicianId;
    Boolean isAcceptNewPatient;
    Boolean isVirtual;
    private Set<String> specialties;
    Double value;
    Double quality;
    Double efficiency;
    String facilityName;
    String npi;
}
