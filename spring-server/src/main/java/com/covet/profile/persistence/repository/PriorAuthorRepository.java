package com.covet.profile.persistence.repository;

import java.util.Date;
import java.util.List;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.covet.profile.persistence.model.PriorAuthor;

public interface PriorAuthorRepository extends JpaRepository<PriorAuthor, UUID>, JpaSpecificationExecutor<PriorAuthor> {
        List<PriorAuthor> findByPhysicianId(UUID physicianId);

        @Query("SELECT pa FROM PriorAuthor pa LEFT JOIN Profile p ON pa.physicianId = p.cognitoId WHERE (:priorAuthorStatus is null or pa.authorStatus = :priorAuthorStatus) and (((cast(:startDate as date) is null ) or (cast(:endDate as date) is null )) or (DATE(pa.createdDate) >= :startDate AND DATE(pa.createdDate) <= :endDate)) and (:keyword is null or (p.firstName Like CONCAT('%', :keyword, '%') or p.address Like CONCAT('%', :keyword, '%')))")
        List<PriorAuthor> getPriorAuthorList(@Param("priorAuthorStatus") String priorAuthorStatus,
                        @Param("startDate") Date startDate, @Param("endDate") Date endDate,
                        @Param("keyword") String keyword);

}