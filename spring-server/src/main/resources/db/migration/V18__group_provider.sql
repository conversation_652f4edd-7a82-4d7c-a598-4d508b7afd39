select *
into temp table physiciansOldData
from physician
where physician_id not in (select physician_id from physician_rating);

insert into physician_rating (physician_id, average_rating, total_reviews)
select p.physician_id, 0, 0
from physiciansOldData p;

drop table physiciansOldData;

create table group_provider(
    group_id uuid not null,
    group_name varchar (265),
    address varchar (256),
    email varchar(320),
    phone_number varchar(10),
    is_virtual_only boolean default false,
    description varchar (2000),
    created_date timestamp,
    updated_date timestamp,
    primary key (group_id)
);

create table group_physician  (
    group_id uuid not null,
    physician_id uuid not null,
    in_group boolean default false,
    created_date timestamp,
    updated_date timestamp,

    primary key (group_id, physician_id, in_group),
    constraint fk_group_id foreign key (group_id) references group_provider(group_id),
    constraint fk_group_physician_id foreign key (physician_id) references physician(physician_id)
);

alter table review add column group_id uuid;
alter table review add constraint fk_review_group foreign key (group_id) references group_provider;
alter table review alter column physician_id drop not null;

create table group_rating  (
                                   group_id uuid not null,
                                   average_rating float check (average_rating between 0 and 5) default 0,
                                   total_reviews int not null default 0,
                                   primary key (group_id)
);

alter table physician_rating add constraint fk_physician_rating foreign key (physician_id) references physician(physician_id);
alter table group_rating add constraint fk_group_rating foreign key (group_id) references group_provider;

-- update average rating everytime a patient review and rating physician
CREATE OR REPLACE FUNCTION update_group_average_rating() RETURNS TRIGGER AS $update_group_average_rating$
BEGIN
    IF (NEW.rating IS NOT NULL AND NEW.group_id IS NOT NULL) THEN
        INSERT INTO group_rating VALUES (NEW.group_id, (SELECT AVG(rating) FROM review WHERE group_id = NEW.group_id), 1)
        ON CONFLICT (group_id) DO UPDATE SET (average_rating, total_reviews ) =  ((SELECT AVG(rating) FROM review WHERE group_id = NEW.group_id), ((SELECT COUNT(*) FROM review WHERE group_id = NEW.group_id) + 1))
                                      WHERE NEW.group_id = group_rating.group_id;
RAISE NOTICE 'updated group average rating';
END IF;
RETURN NULL; -- result is ignored since this is an AFTER trigger
END;
$update_group_average_rating$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER update_group_average_rating
    AFTER INSERT OR UPDATE OR DELETE ON review
    FOR EACH ROW EXECUTE FUNCTION update_group_average_rating();


-- update average rating everytime a patient review and rating physician
CREATE OR REPLACE FUNCTION update_average_rating() RETURNS TRIGGER AS $update_average_rating$
BEGIN
    IF (NEW.rating IS NOT NULL AND NEW.physician_id IS NOT NULL) THEN
        INSERT INTO physician_rating VALUES (NEW.physician_id, (SELECT AVG(rating) FROM review WHERE physician_id = NEW.physician_id), 1)
        ON CONFLICT (physician_id) DO UPDATE SET (average_rating, total_reviews ) =  ((SELECT AVG(rating) FROM review WHERE physician_id = NEW.physician_id), ((SELECT COUNT(*) FROM review WHERE physician_id = NEW.physician_id) + 1))
        WHERE NEW.physician_id = physician_rating.physician_id;
        RAISE NOTICE 'updated physician average rating';
    END IF;
    RETURN NULL; -- result is ignored since this is an AFTER trigger
END;
$update_average_rating$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER update_average_rating
    AFTER INSERT OR UPDATE OR DELETE ON review
    FOR EACH ROW EXECUTE FUNCTION update_average_rating();

--Trigger to insert default physician rating
-- CREATE OR REPLACE FUNCTION insert_default_physician_rating() RETURNS TRIGGER AS $insert_default_physician_rating$
-- BEGIN
--     INSERT INTO physician_rating VALUES(new.physician_id, 0.0, 0);
--     RETURN new;
-- END;
-- $insert_default_physician_rating$ language plpgsql;
--
-- CREATE OR REPLACE TRIGGER insert_default_physician_rating
--     AFTER INSERT ON physician
--     FOR EACH ROW EXECUTE FUNCTION insert_default_physician_rating();

-- --Trigger to insert default group rating
-- CREATE OR REPLACE FUNCTION insert_default_group_rating() RETURNS TRIGGER AS $insert_default_group_rating$
-- BEGIN
-- INSERT INTO group_rating VALUES(new.group_id, 0.0, 0);
-- RETURN new;
-- END;
-- $insert_default_group_rating$ language plpgsql;
--
-- CREATE OR REPLACE TRIGGER insert_default_group_rating
--     AFTER INSERT ON group_provider
--     FOR EACH ROW EXECUTE FUNCTION insert_default_group_rating();