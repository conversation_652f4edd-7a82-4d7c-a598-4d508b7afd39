package com.covet.profile.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Creates the abstract to parse the notification payload.
 * notifications have many types. Json type is the their data format.
 * We can define a DTO to map with the fields and parse to the specific object
 * dynamically.
 */
public abstract class TemplateNotificationParser {
    public ObjectMapper objectMapper;

    public TemplateNotificationParser() {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public abstract Object parseTemplate(String json) throws JsonProcessingException;
}
