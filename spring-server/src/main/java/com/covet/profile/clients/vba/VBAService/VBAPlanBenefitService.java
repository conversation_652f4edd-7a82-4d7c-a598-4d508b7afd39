package com.covet.profile.clients.vba.VBAService;

import com.covet.profile.clients.vba.VBADtos.Plans.VBAPlaceCoPayItemDto;
import com.covet.profile.clients.vba.VBAModels.Plans.VBABenefit;
import com.covet.profile.clients.vba.VBAModels.Plans.VBAPlaceCoInsuranceItem;
import com.covet.profile.clients.vba.VBAModels.Plans.VBAPlaceCoPayItem;
import com.covet.profile.clients.vba.VBARepository.VBAPlanRepository;
import com.covet.profile.converter.PlanBenefitConverter;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Service
public class VBAPlanBenefitService {
    private final VBAPlanRepository vbaPlanRepository;
    public VBAPlanBenefitService(VBAPlanRepository vbaPlanRepository) {
        this.vbaPlanRepository = vbaPlanRepository;
    }

    public List<VBAPlaceCoPayItemDto> getPlanBenefitList(String planId) {
        List<VBABenefit> benefits = vbaPlanRepository.getBenefitListByPlanId(planId);

        List<CompletableFuture<List<VBAPlaceCoPayItemDto>>> futures = new ArrayList<>();

        for (VBABenefit benefit : benefits) {
            CompletableFuture<List<VBAPlaceCoPayItem>> copaysFuture = CompletableFuture.supplyAsync(() -> vbaPlanRepository.getPlaceCopayList(planId, benefit.getBenefitCode()));

            CompletableFuture<List<VBAPlaceCoInsuranceItem>> coInsuranceFuture = copaysFuture.thenComposeAsync(copays -> {
                List<CompletableFuture<VBAPlaceCoInsuranceItem>> coInsuranceFutures = new ArrayList<>();
                for (VBAPlaceCoPayItem copay : copays) {

                    coInsuranceFutures.add(CompletableFuture.supplyAsync(() -> vbaPlanRepository.getPlaceCoInsurance(planId, copay.getBenefitCode(), copay.getPlaceCode())));
                }

                CompletableFuture<Void> allOf = CompletableFuture.allOf(coInsuranceFutures.toArray(new CompletableFuture[0]));

                return allOf.thenApply(ignored -> coInsuranceFutures.stream()
                        .map(CompletableFuture::join)
                        .toList());
            });

            CompletableFuture<List<VBAPlaceCoPayItemDto>> combinedFuture = copaysFuture.thenCombineAsync(
                    coInsuranceFuture,
                    (copays, coInsurances) -> {
                        // Extract specific fields from copays and coInsurances
                        List<VBAPlaceCoPayItemDto> combinedDataList = new ArrayList<>();

                        for (int i = 0; i < copays.size(); i++) {
                            VBAPlaceCoPayItem copay = copays.get(i);
                            VBAPlaceCoInsuranceItem coInsurance = coInsurances.get(i);

                            // Check if copay or coInsurance is null
                            // Leave this to check log in pods as acc Cuong cannot view Pods in AWS UI
                            if (copay == null || coInsurance == null) {
                                if (copay != null) {
                                    // System.out.println(copay.getBenefitCode() + " " + copay.getPlaceCode());
                                }
                                else {
                                    System.out.println("copay is null");
                                }
                                continue;
                            }

                            // Extract specific fields from copay and coInsurance
                            BigDecimal coInsuranceValue = coInsurance.getCoInsurance();
                            BigDecimal coInsuranceNetValue = coInsurance.getCoInsuranceNet();


                            VBAPlaceCoPayItemDto combinedData = PlanBenefitConverter.toPlanBenefitDto(
                                    copay,
                                    coInsuranceValue,
                                    coInsuranceNetValue
                            );

                            combinedDataList.add(combinedData);
                        }

                        return combinedDataList;
                    }
            );
            
            futures.add(combinedFuture);
        }

        CompletableFuture<Void> allOf = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
        );

        // Wait for all CompletableFuture instances to complete
        try {
            allOf.get();
        } catch (InterruptedException | ExecutionException e) {
            // Handle exceptions
            Thread.currentThread().interrupt();
        }

        
        
        List<VBAPlaceCoPayItemDto> listOfAll = futures.stream()
                .map(CompletableFuture::join)
                .flatMap(List::stream)
                .toList();

        System.out.println(listOfAll);        
        // Collect the results
        return listOfAll;
    }
}
