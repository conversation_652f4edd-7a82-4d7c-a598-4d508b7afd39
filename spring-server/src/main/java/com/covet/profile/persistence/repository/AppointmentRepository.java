package com.covet.profile.persistence.repository;

import com.covet.profile.persistence.model.Appointment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.UUID;

/**
 * extend QuerydslPredicateExecutor so that we can use Predicates later to
 * filter search results
 * generated Q-type for the Appointment entity.
 */
public interface AppointmentRepository extends JpaRepository<Appointment, UUID>, JpaSpecificationExecutor<Appointment> {
    List<Appointment> findByPatientId(UUID patientId);

}