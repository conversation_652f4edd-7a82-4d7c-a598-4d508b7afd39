package com.covet.profile.dto.zendeskDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
public class ZendeskAppointmentDto {
    @JsonProperty("appointment_Id")
    private UUID appointmentId;

    @JsonProperty("provider_name")
    private String providerName;

    @JsonProperty("provider_npi")
    private String providerNpi;

    @JsonProperty("appointment_date")
    private LocalDate appointmentDate;

    @JsonProperty("appointment_time")
    private LocalTime appointmentTime;

    @JsonProperty("appointment_type")
    private int appointmentType;
}
