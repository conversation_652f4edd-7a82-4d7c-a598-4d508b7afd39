package com.covet.profile.FactoryMethod;

import com.covet.profile.FactoryMethod.CreateFavorite.IPatientMethod;
import com.covet.profile.dto.CreateFavoriteRequest;
import com.covet.profile.systemEnum.EFavoriteType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.EnumMap;
import java.util.Set;

@Service
public class CreateFavoriteFactoryMethod<T> {
    private final EnumMap<EFavoriteType, IPatientMethod<T>> methodEnumMap;

    @Autowired
    public CreateFavoriteFactoryMethod(Set<IPatientMethod<T>> methods) {
        this.methodEnumMap = new EnumMap<>(EFavoriteType.class);
        for (var method : methods) {
            this.methodEnumMap.put(method.getFavoriteType(), method);
        }
    }

    public IPatientMethod<T> createMethod(CreateFavoriteRequest request) {
        return methodEnumMap.get(request.favoriteType());
    }
}
