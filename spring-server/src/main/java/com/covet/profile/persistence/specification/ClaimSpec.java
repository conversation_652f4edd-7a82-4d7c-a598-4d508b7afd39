package com.covet.profile.persistence.specification;

import com.covet.profile.persistence.model.Claim;
import com.covet.profile.persistence.model.Physician;
import com.covet.profile.persistence.model.Profile;
import com.covet.profile.searchCriteria.ClaimSearchCriteria;
import com.covet.profile.systemEnum.SysEnum;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

public class ClaimSpec {
    private ClaimSpec() {}

    public static Specification<Claim> hasFilterCriteriaIn(ClaimSearchCriteria criteria) {
        return ((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (criteria.getClaimType().isPresent()) {
                Set<Integer> values = Arrays.stream(SysEnum.EClaimType.values()).map(SysEnum.EClaimType::getValue).collect(Collectors.toSet());
                boolean isClaimTypeContained = criteria.getClaimType().get().stream().anyMatch(values::contains);

                if (isClaimTypeContained) {
                    predicates.add(root.get("claimType").in(criteria.getClaimType().get()));
                }
            }

            criteria.getIsPaid().ifPresent(paid -> predicates.add(criteriaBuilder.equal(root.get("isPaid"), paid)));
            criteria.getPrice().ifPresent(price -> predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("totalClaim"), price)));
            criteria.getFromDate().ifPresent(fromDate -> predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createdDate"), fromDate)));
            criteria.getToDate().ifPresent(toDate -> predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createdDate"), toDate)));


            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        });
    }

    public static Specification<Claim> hasClaimIdLike(String keyword) {
        return (root, query, criteriaBuilder) -> criteriaBuilder.like(criteriaBuilder.lower(root.get("claimId")), "%" + keyword.toLowerCase() + "%");
    }
    public static Specification<Claim> hasPhysicianFirstNameLike(String keyword) {
        return getClaimSpecification(keyword, "firstName");
    }
    public static Specification<Claim> hasPhysicianMiddleNameLike(String keyword) {
        return getClaimSpecification(keyword, "middleName");
    }

    public static Specification<Claim> hasPhysicianLastNameLike(String keyword) {
        return getClaimSpecification(keyword, "lastName");
    }

    private static Specification<Claim> getClaimSpecification(String keyword, String attributeName) {
        return (root, query, criteriaBuilder) -> {
            Join<Claim, Physician> claimPhysician = root.join("physician");
            Join<Physician, Profile> physicianProfile = claimPhysician.join("profile");
            return criteriaBuilder.like(criteriaBuilder.lower(physicianProfile.get(attributeName)), "%" + keyword.toLowerCase() + "%");
        };
    }

    public static Specification<Claim> hasUserRoleIsPhysician(UUID cognitoId) {
        return (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("physicianId"), cognitoId);
    }

    public static Specification<Claim> hasUserRoleIsPatient(UUID cognitoId) {
        return (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("patientId"), cognitoId);
    }
}
