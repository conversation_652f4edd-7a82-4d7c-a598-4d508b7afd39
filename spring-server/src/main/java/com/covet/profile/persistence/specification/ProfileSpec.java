package com.covet.profile.persistence.specification;

import org.springframework.data.jpa.domain.Specification;

import com.covet.profile.persistence.model.Profile;

public class ProfileSpec {
    public static Specification<Profile> searchByColumn(String columnName, String keyword) {
        return (root, query, builder) -> {
            return builder.like(root.get(columnName), keyword);
        };
    }
}
