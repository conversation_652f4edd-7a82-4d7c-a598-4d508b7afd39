BEGIN;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'network'
    ) THEN
CREATE TABLE public.network (
                                id uuid PRIMARY KEY,
                                network_code VARCHAR(64) NOT NULL,
                                network_decs VARCHAR(1000),
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                created_by <PERSON><PERSON><PERSON><PERSON>(64),
                                updated_by VARCHAR(64)
);
ELSE
        RAISE NOTICE 'Table already exists.';
END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'physician_network'
    ) THEN
CREATE TABLE public.physician_network (
                                     physician_id UUID NOT NULL,
                                     network_id UUID NOT NULL,
                                     PRIMARY KEY (physician_id, network_id),
                                     <PERSON>OR<PERSON><PERSON><PERSON> KEY (physician_id) REFERENCES public.physician(physician_id) ON DELETE CASCADE,
                                     FOREIG<PERSON> KEY (network_id) REFERENCES public.network(id) ON DELETE CASCADE
);
ELSE
        RAISE NOTICE 'Table already exists.';
END IF;
END $$;

COMMIT;
