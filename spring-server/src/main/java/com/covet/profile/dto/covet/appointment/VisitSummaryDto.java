package com.covet.profile.dto.covet.appointment;

import com.covet.profile.dto.covet.provider.PhysicianDto;
import com.covet.profile.persistence.model.PostVisit;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;



@JsonSerialize(as = ImmutableVisitSummaryDto.class)
@JsonDeserialize(as = ImmutableVisitSummaryDto.class, builder = ImmutableVisitSummaryDto.Builder.class)
@Value.Immutable
public interface VisitSummaryDto {

    @Value.Parameter
    @JsonProperty
    PostVisit postVisit();

    @Value.Parameter
    @JsonProperty
    PhysicianDto physicianDto();


}