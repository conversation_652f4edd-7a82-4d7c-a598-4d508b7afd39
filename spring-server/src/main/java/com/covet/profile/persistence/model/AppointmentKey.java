package com.covet.profile.persistence.model;

import com.sun.istack.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

@Embeddable
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AppointmentKey implements Serializable {

    @Column(name = "patient_id")
    @NotNull
    private UUID patientId;

    @Column(name = "physician_id")
    @NotNull
    private UUID physicianId;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "start_time")
    @NotNull
    private Date startTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "end_time")
    @NotNull
    private Date endTime;

}