package com.covet.profile.service;

import com.covet.profile.dto.CapRxDto.pharmacy.DistanceReqBodyDto;
import com.covet.profile.dto.CapRxDto.pharmacy.PharmacyReqBodyDto;
import com.covet.profile.dto.covet.pharmacy.PharmacyDetailDto;
import com.covet.profile.dto.covet.pharmacy.PreferredPharmacyDto;
import com.covet.profile.dto.covet.prescription.PrescriptionPharmacyDto;
import com.covet.profile.dto.covet.profile.ProfileDto;
import com.covet.profile.persistence.compositeKey.PreferredPharmacyID;
import com.covet.profile.persistence.compositeKey.PrescriptionID;
import com.covet.profile.persistence.compositeKey.PrescriptionPharmacyID;
import com.covet.profile.converter.PharmacyConverter;
import com.covet.profile.converter.PrescriptionPharmacyConverter;
import com.covet.profile.dto.CapRxDto.claim.CapClaimPricingItemDto;
import com.covet.profile.dto.CapRxDto.drug.request.CapDrugPriceItemDto;
import com.covet.profile.dto.CapRxDto.drug.response.CapDrugPriceListResponseDto;
import com.covet.profile.dto.CapRxDto.pharmacy.CapPharmacyDto;
import com.covet.profile.dto.ProviderDto.LocationDto;
import com.covet.profile.dto.location.ICoordinate;
import com.covet.profile.exception.ResourceNotFoundException;
import com.covet.profile.helper.Coordinate;
import com.covet.profile.persistence.model.*;
import com.covet.profile.persistence.repository.*;
import com.covet.profile.persistence.specification.PreferredPharmacySpec;
import com.covet.profile.persistence.specification.SalePrescriptionSpec;
import com.covet.profile.systemEnum.ESortType;
import com.covet.profile.systemEnum.SysEnum;
import com.covet.profile.utils.AuthUtils;
import com.covet.profile.utils.ImageUtils;
import com.covet.profile.utils.ObjectToUuidUtils;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.EntityManager;
import javax.persistence.EntityNotFoundException;
import javax.persistence.PersistenceContext;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service
public class PharmacyService {
    @PersistenceContext
    private EntityManager entityManager;
    private final PharmacyRepository pharmacyRepository;
    private final PrescriptionPharmacyRepository prescriptionPharmacyRepository;
    private final ProfileRepository profileRepository;
    private final PreferredPharmacyRepository preferredPharmacyRepository;
    private final PictureRepository pictureRepository;

    private final S3Service s3Service;

    private final CapitalRxService capitalRxService;

    //TODO: create separate constant class for the app's message
    private static final String pharmacy_message_001 = "No pharmacies prices found for this drug";

    @Autowired
    PharmacyService(PharmacyRepository pharmacyRepository, PrescriptionPharmacyRepository prescriptionPharmacyRepository,
                    ProfileRepository profileRepository, PreferredPharmacyRepository preferredPharmacyRepository,
                    PictureRepository pictureRepository, S3Service s3Service, CapitalRxService capitalRxService) {
        this.pharmacyRepository = pharmacyRepository;
        this.prescriptionPharmacyRepository = prescriptionPharmacyRepository;
        this.profileRepository = profileRepository;
        this.preferredPharmacyRepository = preferredPharmacyRepository;
        this.pictureRepository = pictureRepository;
        this.s3Service = s3Service;
        this.capitalRxService = capitalRxService;
    }

    public Pharmacy createOrUpdatePharmacy(Pharmacy pharmacy)
            throws EntityNotFoundException {
        return pharmacyRepository.save(pharmacy);
    }

    public PrescriptionPharmacy createPrescriptionPharmacy(PrescriptionPharmacyDto prescriptionPharmacyDto)
            throws EntityNotFoundException {
        var newPrescriptionPharmacy = prescriptionPharmacyDto.getPrescriptionPharmacy();
        if (newPrescriptionPharmacy.isPresent()) {
            var prescriptionPharmacyId = newPrescriptionPharmacy.get();
            String prescriptionName = prescriptionPharmacyId.getPrescriptionName();
            String prescriptionCode = prescriptionPharmacyId.getPrescriptionCode();
            UUID patientID = prescriptionPharmacyId.getPatientID();
            PrescriptionID prescriptionID = new PrescriptionID(prescriptionName,
                    prescriptionCode, patientID);
            Prescription prescription = entityManager.getReference(Prescription.class, prescriptionID);
            PrescriptionPharmacy prescriptionPharmacy = PrescriptionPharmacyConverter.dtoToPrescriptionPharmacy(prescriptionPharmacyDto, prescription);
            return prescriptionPharmacyRepository.save(prescriptionPharmacy);
        } else {
            throw new EntityNotFoundException();
        }
    }

    public Page<PreferredPharmacyDto> getPharmaciesWithAdditionInfo(PrescriptionID prescriptionID,
            Optional<SysEnum.EPharmacy> ePharmacy, Pageable pageable, Optional<Integer> maxDistance) {
        UUID patientId = AuthUtils.getCognitoId();

        Coordinate coordinate = new Coordinate();

        var profile = profileRepository.findById(patientId);
        if (profile.isPresent()) {
            coordinate.setLatitude(profile.get().getLatitude());
            coordinate.setLongitude(profile.get().getLongitude());
        }
        Page<PrescriptionPharmacy> salePrescriptions = prescriptionPharmacyRepository
                .findAll(SalePrescriptionSpec.getPharmaciesByPrescriptionId(prescriptionID), pageable);
        List<UUID> pharmacyIds = new ArrayList<>();
        List<Picture> images = new ArrayList<>();

        if (!salePrescriptions.isEmpty()) {
            if (maxDistance.isPresent()) {
                List<PrescriptionPharmacy> prescriptionPharmacyList = salePrescriptions.getContent().stream()
                        .filter(salePrescription -> {
                            var pharmacyId = capitalRxService.getPharmacyById(salePrescription.getId().getPharmacyId());
                            return calculateDistance(pharmacyId, coordinate.getLatitude(), coordinate.getLongitude()) < (double) maxDistance.get();
                        })
                        .toList();
                salePrescriptions = new PageImpl<>(prescriptionPharmacyList, pageable, prescriptionPharmacyList.size());
            }

            pharmacyIds = salePrescriptions.getContent().stream()
                    .map(salePrescription -> ObjectToUuidUtils.hashToUUID(salePrescription.getId().getPharmacyId())).toList();
        }

        if (!(pharmacyIds.isEmpty() || pharmacyIds == null)) {
            images = pictureRepository.findAllById(pharmacyIds);
        }

        List<Map<UUID, URL>> imgURLs = ImageUtils.getImgURLs(s3Service.getExpiredDays(),Optional.ofNullable(images));

        List<PreferredPharmacyDto> result = salePrescriptions.stream().map(item -> {
            var pharmacyId = item.getId().getPharmacyId();

            CapPharmacyDto pharmacy = capitalRxService.getPharmacyById(pharmacyId);

            Double distance = pharmacy != null ? calculateDistance(pharmacy, coordinate.getLatitude(), coordinate.getLongitude()) : 0;

            Optional<URL> imgURL = imgURLs.stream()
                    .flatMap(map -> map.entrySet().stream())
                    .filter(entry -> entry.getKey().toString().equals(item.getId().getPharmacyId()) && entry.getValue() != null)
                    .map(Map.Entry::getValue)
                    .findFirst();

            return PharmacyConverter.toPreferredPharmacyDto(
                    pharmacy, imgURL.orElse(null), null, distance, item.getCost());
        }).toList();

        List<PreferredPharmacyDto> resultSort = new ArrayList<>();
        if (!result.isEmpty()) {
            resultSort = result.stream().sorted((e1, e2) -> {
                String keySort = ePharmacy.isPresent() ? ePharmacy.get().getValue() : "distance";
                Double target1;
                Double target2;
                if (keySort.equals(SysEnum.EPharmacy.distance.getValue())) {
                    target1 = e1.getDistance();
                    target2 = e2.getDistance();
                }
                else {
                    target1 = e1.getCost();
                    target2 = e2.getCost();
                }
                return target1 >= target2 ? 1 : -1;
            }).toList();
        }

        return new PageImpl<>(resultSort, pageable, resultSort.size());
    }

    public Double calculateDistance(CapPharmacyDto pharmacy, Double latitude, Double longitude) {
        Double pharmacyLongitude = pharmacy.getLongitude();
        Double pharmacyLatitude = pharmacy.getLatitude();
        Double result = Math
                .sqrt(Math.pow((latitude - pharmacyLatitude), 2) + Math.pow((longitude - pharmacyLongitude), 2));
        return Math.round(result * 100.0) / 100.0;
    }

    public void deletePreferredPharmacy() {
        UUID patientId = AuthUtils.getCognitoId();
        List<PreferredPharmacyID> ids = preferredPharmacyRepository
                .findAll(PreferredPharmacySpec.prescriptionByConditions(patientId)).stream()
                .map(item -> item.getPreferredPharmacyID()).toList();
        preferredPharmacyRepository.deleteAllById(ids);
    }

    public PreferredPharmacy createPreferredPharmacy(Optional<String> pharmacyId) {
        if (!pharmacyId.isPresent()) {
            throw new ResourceNotFoundException("pharmacy ID not provided!");
        }
        UUID patientId = AuthUtils.getCognitoId();
        Optional<CapPharmacyDto> pharmacy = Optional.ofNullable(capitalRxService.getPharmacyById(pharmacyId.get()));
        if (!pharmacy.isPresent()) {
            throw new ResourceNotFoundException("Not found pharmacy id: " + pharmacyId.get());
        }
        deletePreferredPharmacy();
        Profile patient = entityManager.find(Profile.class, patientId);
        PreferredPharmacyID id = new PreferredPharmacyID(patientId, pharmacyId.get());
        PreferredPharmacy target = new PreferredPharmacy(id, patient);
        return preferredPharmacyRepository.save(target);
    }

    /**
     * Retrieves a pageable list of preferred pharmacies for a given patient.
     *
     * @param eSortType   An optional sort type for the result.
     * @param pageable    The pagination information for the result.
     * @param maxDistance An optional maximum distance for filtering pharmacies.
     * @param patientId   The unique identifier of the patient.
     * @return A pageable list of PreferredPharmacyDto objects.
     */
    public Page<PreferredPharmacyDto> getPreferredPharmacies(Optional<SysEnum.ESortType> eSortType, Pageable pageable,
                                                             Optional<Integer> maxDistance, UUID patientId, Optional<String> closestToAddress,
                                                             Optional<String> latitude, Optional<String> longitude) {
        Profile profile = entityManager.find(Profile.class, patientId);
        Double userLatitude;
        Double userLongitude;
        //TODO:/patient/pharmacy/active-prescription dto refused to parse the lat long indo doubles directly. fix later
        if (latitude.isPresent() && longitude.isPresent()){
            userLatitude = Double.parseDouble(latitude.get());
            userLongitude = Double.parseDouble(longitude.get());
        } else{
             userLatitude = profile.getLatitude();
             userLongitude = profile.getLongitude();
        }

        int typeSort = eSortType.isPresent() ? eSortType.get().getValue() : 1;

        List<Integer> networkIds = Arrays.asList(SysEnum.ENetworkId.PreferredNetWork.getValue());

        PharmacyReqBodyDto pharmacyReqBodyDto = generateCapRxPharmacyRequestBody(typeSort, closestToAddress,
            Optional.of(pageable.getPageNumber()), Optional.of(pageable.getPageSize()), Optional.of(networkIds),
            latitude, longitude);

        // Find preferred pharmacy associated with the patient
        var currentPreferredPharmacy = getPreferredPharmacyDetail(patientId);

        // Initialize variables for pharmacy mapping and result lists
        Page<CapPharmacyDto> pharmacies = Page.empty();
        List<CapPharmacyDto> pharmacyList = capitalRxService.getCapitalRxPharmacies(pharmacyReqBodyDto);

        List<PreferredPharmacyDto> result = new ArrayList<>();
        List<PreferredPharmacyDto> resultSort = new ArrayList<>();

        if (!pharmacyList.isEmpty()) {
            if (currentPreferredPharmacy.isPresent()) {
                pharmacyList.removeIf(item -> item.getNpi().equals(currentPreferredPharmacy.get().getPharmacy().getNpi()));
            }

            if (maxDistance.isPresent()) {
        // Filter pharmacies based on maximum distance and user location
        List<CapPharmacyDto> pharmacyListFiltered = pharmacyList.stream()
                .filter(pharmacy -> calculateDistance(pharmacy, userLatitude, userLongitude) <= (double) maxDistance.get()).toList();

                    pharmacies = new PageImpl<>(pharmacyListFiltered, pageable, pharmacyListFiltered.size());
                } else {
                    pharmacies = new PageImpl<>(pharmacyList, pageable, pharmacyList.size());
                }
        }

        if (!pharmacies.isEmpty()) {
            // Retrieve images for the selected pharmacies
            List<UUID> pharmacyIds = pharmacies.stream().map(pharmacyCapRx -> ObjectToUuidUtils.hashToUUID(pharmacyCapRx.getNpi())).toList();
            List<Picture> images = pictureRepository.findAllById(pharmacyIds);

            // Get image URLs for the pharmacies
            List<Map<UUID, URL>> imgURLs = ImageUtils.getImgURLs(s3Service.getExpiredDays(), Optional.ofNullable(images));

            pharmacies.forEach(pharmacy -> {

                UUID pharmacyId = ObjectToUuidUtils.hashToUUID(pharmacy.getNpi());
                Optional<URL> imgURL = imgURLs.stream()
                        .flatMap(map -> map.entrySet().stream())
                        .filter(entry -> entry.getKey().equals(pharmacyId) && entry.getValue() != null)
                        .map(Map.Entry::getValue)
                        .findFirst();
                PreferredPharmacyDto item = PharmacyConverter.toPreferredPharmacyDto(pharmacy, imgURL.isPresent() ? imgURL.get() : null,
                        false, calculateDistance(pharmacy, userLatitude, userLongitude), 0.0);
                result.add(item);
            });

            if (!result.isEmpty()) {
                // Sort the result based on distance and the provided sort type
                resultSort = result.stream().sorted((e1, e2) -> {
                    Double target1 = e1.getDistance();
                    Double target2 = e2.getDistance();
                    return target1 >= target2 ? typeSort : -typeSort;

                }).toList();
            }
        }

        return new PageImpl<>(resultSort, pageable, resultSort.size());
    }

    private PharmacyReqBodyDto generateCapRxPharmacyRequestBody(int sortTypeByDistance, Optional<String> address,
                                                                Optional<Integer> pageNumber, Optional<Integer> pageSize,
                                                                Optional<List<Integer>> networkIds,
                                                                Optional<String> latitude, Optional<String> longitude) {

        String distance = sortTypeByDistance == SysEnum.ESortType.ASC.getValue() ? ESortType.ASC.getValue() : ESortType.DESC.getValue();
        DistanceReqBodyDto distanceReqBodyDto = new DistanceReqBodyDto();
        distanceReqBodyDto.setDistance(distance);
        List<DistanceReqBodyDto> listDistanceReqBodyDto = new ArrayList<>();
        listDistanceReqBodyDto.add(distanceReqBodyDto);
        PharmacyReqBodyDto pharmacyReqBodyDto = new PharmacyReqBodyDto();
        pharmacyReqBodyDto.setAllPharmacy(SysEnum.ESwitch.Yes.getValue());
        pharmacyReqBodyDto.setClosestToAddress(address.orElse(null));
        pharmacyReqBodyDto.setOrderBy(listDistanceReqBodyDto);
        pharmacyReqBodyDto.setNetworkIds(networkIds.isPresent() ? networkIds.get() : null);
        pharmacyReqBodyDto.setPageNumber(pageNumber.isPresent() ? pageNumber.get() : 0);
        pharmacyReqBodyDto.setResultsPerPage(pageSize.isPresent() ? pageSize.get() : 20);

         // Add user latitude and longitude to the request body if present
        latitude.ifPresent(pharmacyReqBodyDto::setUserLatitude);
        longitude.ifPresent(pharmacyReqBodyDto::setUserLongitude);

        return pharmacyReqBodyDto;
    }

    //Get prescription detail with pharmacy info (if any)
    public PharmacyDetailDto getPharmacyByIdAndPrescriptionNameAndCode(PrescriptionID prescriptionID, Coordinate profileCoordinate, String pharmacyID) {
        UUID patientId = prescriptionID.getPatientID();
        Double longitude = profileCoordinate.getLongitude();
        Double latitude = profileCoordinate.getLatitude();

        PharmacyDetailDto pharmacyDetailDto = null;
        //If is added new - check user already add preferred? -? yes -> show preferred -> No? -> show from cap rx price list
        PrescriptionPharmacyID prescriptionPharmacyID;

        prescriptionPharmacyID = new PrescriptionPharmacyID(prescriptionID.getPrescriptionName(), prescriptionID.getPrescriptionCode(), pharmacyID, patientId);
            pharmacyDetailDto = prescriptionPharmacyRepository.findPrescriptionPharmacyById(prescriptionPharmacyID).map(prescriptionPharmacy -> {

                        var foundPharmacy = capitalRxService.getPharmacyById(prescriptionPharmacy.getId().getPharmacyId());
                        var pharmacyDistance = calculateDistance(foundPharmacy, latitude, longitude);
                        URL pharmacyImageURL = ImageUtils.getImageUrl(s3Service.getExpiredDays(),
                                pictureRepository.findById(ObjectToUuidUtils.hashToUUID(foundPharmacy.getNpi())));
                        if (pharmacyImageURL == null) {
                            pharmacyImageURL =  s3Service.getPresignedURL(foundPharmacy.getProviderId());
                        }

                        return PharmacyConverter.pharmacyDetailToDto(foundPharmacy, prescriptionPharmacy.getCost(), pharmacyImageURL, pharmacyDistance);
                    }
            ).orElseThrow(() -> new ResourceNotFoundException("Prescription Pharmacy not found"));


        return pharmacyDetailDto;
    }

    //CH-116: Get pharmacy list when add new prescription
    public PharmacyListWithMessage getCapRxPharmacyListByPrescriptionToAdd(
            PrescriptionID prescriptionID,
            Optional<CapDrugPriceListResponseDto> drugPrice,
            LocationDto locationDto
    ) {
        if (drugPrice.isEmpty()) {
            throw new ResourceNotFoundException(pharmacy_message_001);
        }
        List<PharmacyDetailDto> results = new ArrayList<>();

        UUID patientId = prescriptionID.getPatientID();
        Double longitude = Double.parseDouble(locationDto.getLongitude());
        Double latitude = Double.parseDouble(locationDto.getLatitude());

        List<CapDrugPriceItemDto> filteredPrices = filterValidPrices(drugPrice.get());
        if (filteredPrices.isEmpty()) {
            String message = extractRejectMessage(drugPrice);
            return new PharmacyListWithMessage(Collections.emptyList(), message);
        }

        Optional<PharmacyDetailDto> preferredPharmacyDetail = getPreferredPharmacyDetail(patientId);

        if (preferredPharmacyDetail.isEmpty()) {
            results.addAll(getPharmaciesFromFilteredPrices(filteredPrices, latitude, longitude));
        } else {
            PharmacyDetailDto preferredPharmacyDto = preferredPharmacyDetail.get();

            //If prefer pharmacy not exists in drug price list then show drug price list, otherwise show only prefer pharmacy
            Optional<CapDrugPriceItemDto> matchingDrugPricePharmacy = filteredPrices.stream()
                    .filter(drugPriceItemDto -> preferredPharmacyDto.getPharmacy().getNpi().equals(drugPriceItemDto.getPharmacy().getNpi()))
                    .findFirst();

            if (matchingDrugPricePharmacy.isEmpty()) {
                results.addAll(getPharmaciesFromFilteredPrices(filteredPrices, latitude, longitude));
            } else {
                results.add(preferredPharmacyDto);
            }
        }

        return new PharmacyListWithMessage(results, "");
    }

    private String extractRejectMessage(Optional<CapDrugPriceListResponseDto> drugPrice) {
        if (drugPrice.isPresent()) {
            for (CapDrugPriceItemDto item : drugPrice.get().getPrices()) {
                if (item.getClaim() != null && item.getClaim().getResponse() != null) {
                    CapClaimPricingItemDto response = item.getClaim().getResponse();
                    if (response.getResponseStatus() != null) {
                        return response.getResponseStatus().getRejectMessage();
                    }
                }
            }
        }
        return "No transaction response status found";
    }

    public List<PharmacyDetailDto> getCapRxPharmacyListByPrescription(
            Optional<CapDrugPriceListResponseDto> drugPrice,
            ProfileDto profile,
            String pharmacyID, Optional<SysEnum.EPharmacy> ePharmacy
    ) {
        Double longitude = profile.getLongitude().orElse(0.0);
        Double latitude = profile.getLatitude().orElse(0.0);

        List<PharmacyDetailDto> results = new ArrayList<>();

        if (!drugPrice.isPresent()) {
            throw new ResourceNotFoundException(pharmacy_message_001);
        }

        List<CapDrugPriceItemDto> filteredPrices = filterValidPrices(drugPrice.get());

        if (filteredPrices.isEmpty()) {
            return Collections.emptyList();
        }

        filteredPrices.removeIf(item -> pharmacyID.equals(item.getPharmacy().getNpi()));
        results.addAll(getPharmaciesFromFilteredPrices(filteredPrices, latitude, longitude));

        if (results.isEmpty()) {
            return results;
        }

        Comparator<PharmacyDetailDto> comparator = Comparator.comparingDouble(
                ePharmacy.isPresent() && ePharmacy.get() == SysEnum.EPharmacy.distance
                        ? PharmacyDetailDto::getDistance
                        : PharmacyDetailDto::getCost
        );

        return results.stream().sorted(comparator).collect(Collectors.toList());
    }

    public PharmacyListWithMessage getCapRxPharmacyListByPrescriptionMessage(
            Optional<CapDrugPriceListResponseDto> drugPrice,
            LocationDto location,
            String pharmacyID, Optional<SysEnum.EPharmacy> ePharmacy
    ) {
        if (drugPrice.isEmpty()) {
            throw new ResourceNotFoundException(pharmacy_message_001);
        }

        List<CapDrugPriceItemDto> filteredPrices = filterValidPrices(drugPrice.get());
        if (filteredPrices.isEmpty()) {
            System.out.println("Filtered PRICES are EMPTY!!!");

            String message = extractRejectMessage(drugPrice);
            return new PharmacyListWithMessage(Collections.emptyList(), message);
        }

        Double longitudeValue = Double.parseDouble(location.getLongitude());
        Double latitudeValue = Double.parseDouble(location.getLatitude());

        filteredPrices.removeIf(item -> pharmacyID.equals(item.getPharmacy().getNpi()));
        List<PharmacyDetailDto> results = new ArrayList<>(getPharmaciesFromFilteredPrices(filteredPrices, latitudeValue, longitudeValue));
        if (results.isEmpty()) {
            return new PharmacyListWithMessage(Collections.emptyList(), "");
        }

        Comparator<PharmacyDetailDto> comparator = Comparator.comparingDouble(
                ePharmacy.isPresent() && ePharmacy.get() == SysEnum.EPharmacy.distance
                        ? PharmacyDetailDto::getDistance
                        : PharmacyDetailDto::getCost
        );

        return new PharmacyListWithMessage(results.stream().sorted(comparator).collect(Collectors.toList()), "");
    }

    private List<CapDrugPriceItemDto> filterValidPrices(CapDrugPriceListResponseDto drugPrice) {
        return drugPrice.getPrices().stream()
                .filter(item -> item.getClaim() != null && item.getClaim().getResponse() != null && item.getClaim().getResponse().getClaimPricing() != null)
                .collect(Collectors.toList());
    }

    //CH-116
    private List<PharmacyDetailDto> getPharmaciesFromFilteredPrices(List<CapDrugPriceItemDto> filteredPrices, Double latitude, Double longitude) {
        List<PharmacyDetailDto> pharmacies = new ArrayList<>();
        var executor = Executors.newFixedThreadPool(20);

        List<CompletableFuture<CapPharmacyDto>> capPharmacyDtoListFuture = new LinkedList<>();
        List<CompletableFuture<URL>> urlsFuture = new LinkedList<>();

        for (CapDrugPriceItemDto item : filteredPrices) {
            var pharmacyFuture = CompletableFuture.supplyAsync(() ->
                    capitalRxService.getPharmacyById(item.getPharmacy().getNpi()), executor
            );
            capPharmacyDtoListFuture.add(pharmacyFuture);

            var urlFuture = CompletableFuture.supplyAsync(() -> {
                URL imageURL = ImageUtils.getImageUrl(s3Service.getExpiredDays(),
                        pictureRepository.findById(ObjectToUuidUtils.hashToUUID(item.getPharmacy().getNpi())));
                if (imageURL == null) {
                    imageURL = s3Service.getPresignedURL(item.getPharmacy().getNpi());
                }
                return imageURL;
            }, executor);
            urlsFuture.add(urlFuture);
        }
        List<CompletableFuture<?>> mergedFutures = new ArrayList<>();
        mergedFutures.addAll(capPharmacyDtoListFuture);
        mergedFutures.addAll(urlsFuture);
        CompletableFuture<Void> futures = CompletableFuture.allOf(mergedFutures.toArray(new CompletableFuture[0]));
        futures.join();

        for (int i = 0; i < filteredPrices.size(); i++) {
            var pharmacy = capPharmacyDtoListFuture.get(i).join();
            var imageURL = urlsFuture.get(i).join();

            Double distance = calculateDistance(pharmacy, latitude, longitude);
            Double cost = filteredPrices.get(i).getClaim().getResponse().getClaimPricing().getPatientPayAmount();
            PharmacyDetailDto pharmacyDetailDto = PharmacyConverter.pharmacyDetailToDto(pharmacy, cost, imageURL, distance);
            pharmacies.add(pharmacyDetailDto);
        }
        return pharmacies;
    }

    public URL postImageForPharmacy(@NonNull MultipartFile file, @NonNull UUID pharmacyId) {
        String imageUUID = pharmacyId.toString();
        s3Service.deleteFile(imageUUID);
        s3Service.putFile(file, imageUUID);
        URL imageUrl = s3Service.getPresignedURL(imageUUID);
        pictureRepository.save(ImageUtils.getSavedImage(pictureRepository.findById(pharmacyId), pharmacyId, imageUrl));
        return imageUrl;
    }

    public Optional<PharmacyDetailDto> getPreferredPharmacyDetail(UUID patientId) {
        return preferredPharmacyRepository.findPreferredPharmaciesByPatientId(patientId).map(preferredPharmacy -> {
            List<CompletableFuture<?>> futures = new LinkedList<>();

            CompletableFuture<ICoordinate> coordinateFuture = CompletableFuture.supplyAsync(() ->
                    profileRepository.getCoordinate(patientId)
            );
            CompletableFuture<CapPharmacyDto> pharmacyFuture = CompletableFuture.supplyAsync(() ->
                    capitalRxService.getPharmacyById(preferredPharmacy.getPreferredPharmacyID().getPharmacyId())
            );
            futures.add(coordinateFuture);
            futures.add(pharmacyFuture);

            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allOf.join();

            ICoordinate coordinate = coordinateFuture.join();
            var latitude = coordinate.getLatitude();
            var longitude = coordinate.getLongitude();

            var pharmacy = pharmacyFuture.join();

                    double distance = calculateDistance(pharmacy, latitude, longitude);
                    URL imageURL = ImageUtils.getImageUrl(s3Service.getExpiredDays(),
                            pictureRepository.findById(ObjectToUuidUtils.hashToUUID(pharmacy.getProviderId())));
                    if (imageURL == null) {
                        imageURL = s3Service.getPresignedURL(pharmacy.getProviderId());
                    }

                    return PharmacyConverter.pharmacyDetailToDto(pharmacy, 0.0, imageURL, distance);
                }
        );
    }

    public Long countPreferredPharmacy(UUID patientId) {
        return preferredPharmacyRepository.countPreferredPharmaciesByPatientId(patientId);
    }
}
