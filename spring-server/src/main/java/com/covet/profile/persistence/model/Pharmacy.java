package com.covet.profile.persistence.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
//@Table(name = "pharmacy")

public class Pharmacy implements Serializable {

    @Id
    @Column(name = "pharmacy_id")
    private String pharmacyId;

    @Column(name = "latitude")
    @NotNull
    private Double latitude;

    @Column(name = "longitude")
    @NotNull
    private Double longitude;

    @Column(name = "pharmacy_name")
    @NotNull
    private String pharmacyName;

    @Column(name = "address")
    @NotNull
    private String address;

    @Column(name = "phone_number")
    @NotNull
    private String phoneNumber;
}
