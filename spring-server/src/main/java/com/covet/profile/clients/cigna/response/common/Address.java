package com.covet.profile.clients.cigna.response.common;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
public class Address {
    private String use;
    private List<String> line;
    private String city;
    private String district;
    private String state;
    private String postalCode;
    private String country;
    private List<Map<String, Object>> extension;
}
