package com.covet.profile.dto.CapRxDto.drug.response;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
public class ConvertDrugDto {
    @JsonProperty
    Integer id;

    @JsonProperty("drug_name")
    String drugName;

    @JsonProperty("drug_type")
    String drugType;

    @JsonProperty("is_existed")
    Boolean isExisted;

    @JsonProperty
    Map<String, List<DrugConfigDto>> configurations;

}
