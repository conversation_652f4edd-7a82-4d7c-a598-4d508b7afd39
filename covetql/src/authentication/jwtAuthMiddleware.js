import jwt from 'jsonwebtoken';
import jwksClient from 'jwks-rsa';

const client = jwksClient({
  jwksUri: 'https://dev-covet.us.auth0.com/.well-known/jwks.json',
});

function getKey(header) {
  return new Promise((resolve, reject) => {
    client.getSigningKey(header.kid, function(err, key) {
      if (err) {
        console.log("Error getting the signing key: ", err);
        reject(err);
        return;
      }
      const signingKey = key.publicKey || key.rsaPublicKey;
      resolve(signingKey);
    });
  });
}

async function verifyToken(token) {
  
  const decodedToken = jwt.decode(token, { complete: true });
  if (!decodedToken) {
    throw new Error('Invalid token');
  }

  const signingKey = await getKey(decodedToken.header);
  return jwt.verify(token, signingKey);
}

const jwtAuthMiddleware = async (req, res, next) => {
  if (req.path === '/health') {
    return next();
  }
  
  // Check for 'createClient' mutation in the GraphQL request (to make account before registering with auth0 and add usaerID to metadata)
  if (req.path === '/graphql' && req.body.query.includes('mutation createClient')) {
    return next();
  }
  
  const authHeader = req.headers.authorization || '';
  const match = authHeader.match(/Bearer (.+)/);
  
  if (!match) {
    console.log("NO TOKEN")
    res.status(401).send('Unauthorized');
    return; 
  }

  try {
    const token = match[1];
    const decoded = await verifyToken(token);
    req.user = decoded;
    next();
  } catch (error) {
    console.error('Token verification failed', error);
    res.status(401).send('Unauthorized');
  }
};

export { jwtAuthMiddleware, verifyToken };
