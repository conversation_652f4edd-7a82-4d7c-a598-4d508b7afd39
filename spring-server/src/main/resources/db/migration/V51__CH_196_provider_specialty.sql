BEGIN;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'specialty'
    ) THEN
        CREATE TABLE public.specialty (
            specialty_id UUID PRIMARY KEY,
            name VARCHA<PERSON>(255) NOT NULL,
            created_at timestamp,
            updated_at timestamp
        );
ELSE
        RAISE NOTICE 'Table already exists.';
END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'physician_specialty'
    ) THEN
        CREATE TABLE public.physician_specialty (
            physician_id UUID NOT NULL,
            specialty_id UUID NOT NULL,
            PRIMARY KEY (physician_id, specialty_id),
            FOREIGN KEY (physician_id) REFERENCES public.physician(physician_id) ON DELETE CASCADE,
            FOREIGN KEY (specialty_id) REFERENCES public.specialty(specialty_id) ON DELETE CASCADE
        );
ELSE
        RAISE NOTICE 'Table already exists.';
END IF;
END $$;

COMMIT;