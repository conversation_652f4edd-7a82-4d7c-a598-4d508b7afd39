package com.covet.profile.persistence.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sun.istack.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "appointment")
public class Appointment implements Serializable {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @Column(name = "patient_id")
    @NotNull
    private UUID patientId;

    @Column(name = "physician_id")
    @NotNull
    private UUID physicianId;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "start_time")
    @NotNull
    private Date startTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "end_time")
    @NotNull
    private Date endTime;

    @Column(name = "is_virtual")
    @ColumnDefault(value = "false")
    private Boolean isVirtual;

    @Column(name = "appointment_type")
    private int appointmentType; // annual checkup, ear infection, covid, etc

    @Column(name = "description")
    private String description;

    @Column(name = "status")
    private String status; // enum : complete, pending, expired, etc

    @Column(name = "pre_visit")
    private String preVisit;

    @Column(name = "post_visit")
    private String postVisit;

    @Column(name = "daysOfWeek")
    private String daysOfWeek;

    @Column(name = "appointmentDate")
    private LocalDateTime appointmentDate;

    @JsonIgnore
    @ManyToOne
    @MapsId("patientId")
    @JoinColumn(name = "patient_id")
    private Profile profile;

    @JsonIgnore
    @ManyToOne
    @MapsId("physicianId")
    @JoinColumn(name = "physician_id")
    private Physician physician;

}