# Use the official Maven image as the base image
FROM maven:3.8.1-openjdk-17-slim AS build

# Set the working directory inside the container
WORKDIR /app

# Copy the local source code to the container
COPY src ./src
COPY pom.xml ./

# Package the application
RUN mvn clean package

# Use the official OpenJDK image to create a lean executable container
FROM openjdk:17-jdk-slim

# Copy the packaged application JAR from the build stage
COPY --from=build /app/target/*.jar app.jar

# Set the port number the container should expose
EXPOSE 5000

# Run the application
CMD ["java", "-jar", "-Dserver.port=5000", "app.jar"]

