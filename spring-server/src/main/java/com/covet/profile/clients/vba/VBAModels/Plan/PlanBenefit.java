package com.covet.profile.clients.vba.VBAModels.Plan;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Data
@Getter
@Setter
public class PlanBenefit {
    @JsonProperty("plan_ID")
    String planID;
    @JsonProperty("benefit_Code")
    String benefitCode;
    @JsonProperty("accident_Only")
    String accidentOnly;
    @JsonProperty("always_Pay_In_Network")
    String alwaysPayInNetwork;
    @JsonProperty("annual_Max")
    String annualMax;
    @JsonProperty("annual_Max_Member")
    String annualMaxMember;
    @JsonProperty("annual_Max_Net")
    String annualMaxNet;
    @JsonProperty("annual_Max_Net_Member")
    String annualMaxNetMember;
    @JsonProperty("annual_Max_OON")
    String annualMaxOON;
    @JsonProperty("annual_Max_OON_Member")
    String annualMaxOONMember;
    @JsonProperty("coIns_In_To_Out")
    String coInsInToOut;
    @JsonProperty("coIns_Max_Mem")
    String coInsMaxMem;
    @JsonProperty("coIns_Max_Net_Mem")
    String coInsMaxNetMem;
    @JsonProperty("coIns_Max_Net_Sub")
    String coInsMaxNetSub;
    @JsonProperty("coIns_Max_OON_Mem")
    String coInsMaxOONMem;
    @JsonProperty("coIns_Max_OON_Sub")
    String coInsMaxOONSub;
    @JsonProperty("coIns_Max_Sub")
    String coInsMaxSub;
    @JsonProperty("coIns_Out_To_In")
    String coInsOutToIn;
    @JsonProperty("coverage_24Hour")
    String coverage24Hour;
    @JsonProperty("covered")
    String covered;
    @JsonProperty("covered_In_Net_Only")
    String coveredInNetOnly;
    @JsonProperty("ded_In_To_Out")
    String dedInToOut;
    @JsonProperty("ded_Out_To_In")
    String dedOutToIn;
    @JsonProperty("deductible_Before_CoPay")
    String deductibleBeforeCoPay;
    @JsonProperty("effective_Date")
    String effectiveDate;
    @JsonProperty("entry_Date")
    String entryDate;
    @JsonProperty("entry_User")
    String entryUser;
    @JsonProperty("excluded")
    String excluded;
    @JsonProperty("hospital_1stDay")
    String hospital1stDay;
    @JsonProperty("include_Amt_In_Plan")
    String includeAmtInPlan;
    @JsonProperty("include_CoIns_In_Plan")
    String includeCoInsInPlan;
    @JsonProperty("include_Ded_In_Plan")
    String includeDedInPlan;
    @JsonProperty("include_OOP_In_Plan")
    String includeOOPInPlan;
    @JsonProperty("ltC_Applies_To_Elimination_Period")
    String ltCAppliesToEliminationPeriod;
    @JsonProperty("ltC_Applies_To_WP")
    String ltCAppliesToWP;
    @JsonProperty("ltC_Apply_Inflation")
    String ltCApplyInflation;
    @JsonProperty("ltC_Base_Benefit")
    String ltCBaseBenefit;
    @JsonProperty("ltC_Daily_Benefit")
    String ltCDailyBenefit;
    @JsonProperty("ltC_Daily_Benefit_Range_From")
    String ltCDailyBenefitRangeFrom;
    @JsonProperty("ltC_Daily_Benefit_Range_Increment")
    String ltCDailyBenefitRangeIncrement;
    @JsonProperty("ltC_Daily_Benefit_Range_Thru")
    String ltCDailyBenefitRangeThru;
    @JsonProperty("ltC_Daily_Benefit_Type")
    String ltCDailyBenefitType;
    @JsonProperty("ltC_Inflation_Benefit")
    String ltCInflationBenefit;
    @JsonProperty("ltC_Inflation_Compound")
    String ltCInflationCompound;
    @JsonProperty("ltC_Inflation_Frequency")
    String ltCInflationFrequency;
    @JsonProperty("ltC_Inflation_Frequency_Start")
    String ltCInflationFrequencyStart;
    @JsonProperty("ltC_Inflation_Frequency_Type")
    String ltCInflationFrequencyType;
    @JsonProperty("ltC_Inflation_Limit")
    String ltCInflationLimit;
    @JsonProperty("ltC_Inflation_Limit_Type")
    String ltCInflationLimitType;
    @JsonProperty("ltC_Inflation_Pct")
    String ltCInflationPct;
    @JsonProperty("ltC_Inflation_Type")
    String ltCInflationType;
    @JsonProperty("ltC_Lifetime_Max_Days_From")
    String ltCLifetimeMaxDaysFrom;
    @JsonProperty("ltC_Lifetime_Max_Days_Thru")
    String ltCLifetimeMaxDaysThru;
    @JsonProperty("ltC_Lifetime_Max_From")
    String ltCLifetimeMaxFrom;
    @JsonProperty("ltC_Lifetime_Max_Increment")
    String ltCLifetimeMaxIncrement;
    @JsonProperty("ltC_Lifetime_Max_Thru")
    String ltCLifetimeMaxThru;
    @JsonProperty("ltC_Lifetime_Max_Type")
    String ltCLifetimeMaxType;
    @JsonProperty("ltC_Lifetime_Max_Years_From")
    String ltCLifetimeMaxYearsFrom;
    @JsonProperty("ltC_Lifetime_Max_Years_Thru")
    String ltCLifetimeMaxYearsThru;
    @JsonProperty("ltC_Monthly_Benefit")
    String ltCMonthlyBenefit;
    @JsonProperty("ltC_Monthly_Benefit_Range_From")
    String ltCMonthlyBenefitRangeFrom;
    @JsonProperty("ltC_Monthly_Benefit_Range_Increment")
    String ltCMonthlyBenefitRangeIncrement;
    @JsonProperty("ltC_Monthly_Benefit_Range_Thru")
    String ltCMonthlyBenefitRangeThru;
    @JsonProperty("ltC_Monthly_Benefit_Type")
    String ltCMonthlyBenefitType;
    @JsonProperty("ltC_Pct_Of_Benefit")
    String ltCPctOfBenefit;
    @JsonProperty("ltC_Pct_Of_Benefit_Code")
    String ltCPctOfBenefitCode;
    @JsonProperty("ltC_Shared_Benefit")
    String ltCSharedBenefit;
    @JsonProperty("ltC_Use_Benefit_DBA")
    String ltCUseBenefitDBA;
    @JsonProperty("ltC_Use_Benefit_Lifetime_Max")
    String ltCUseBenefitLifetimeMax;
    @JsonProperty("ltC_Use_Benefit_MBA")
    String ltCUseBenefitMBA;
    @JsonProperty("ltC_Use_Benefit_Type")
    String ltCUseBenefitType;
    @JsonProperty("ltC_Use_Pct_Of_Benefit")
    String ltCUsePctOfBenefit;
    @JsonProperty("lifetime_Max")
    String lifetimeMax;
    @JsonProperty("lifetime_Max_Member")
    String lifetimeMaxMember;
    @JsonProperty("lifetime_Max_Net")
    String lifetimeMaxNet;
    @JsonProperty("lifetime_Max_Net_Member")
    String lifetimeMaxNetMember;
    @JsonProperty("lifetime_Max_OON")
    String lifetimeMaxOON;
    @JsonProperty("lifetime_Max_OON_Member")
    String lifetimeMaxOONMember;
    @JsonProperty("maternity")
    String maternity;
    @JsonProperty("member_Ded_OON")
    String memberDedOON;
    @JsonProperty("member_Ded_PPO")
    String memberDedPPO;
    @JsonProperty("member_Deductible")
    String memberDeductible;
    @JsonProperty("ooP_Calc_Option")
    String ooPCalcOption;
    @JsonProperty("ooP_In_To_Out")
    String ooPInToOut;
    @JsonProperty("ooP_Max_Mem")
    String ooPMaxMem;
    @JsonProperty("ooP_Max_Net_Mem")
    String ooPMaxNetMem;
    @JsonProperty("ooP_Max_Net_Sub")
    String ooPMaxNetSub;
    @JsonProperty("ooP_Max_OON_Mem")
    String ooPMaxOONMem;
    @JsonProperty("ooP_Max_OON_Sub")
    String ooPMaxOONSub;
    @JsonProperty("ooP_Max_Sub")
    String ooPMaxSub;
    @JsonProperty("ooP_Out_To_In")
    String ooPOutToIn;
    @JsonProperty("occupational")
    String occupational;
    @JsonProperty("pcP_Only")
    String pcPOnly;
    @JsonProperty("parent_Benefit")
    String parentBenefit;
    @JsonProperty("pend_Ex_Code")
    String pendExCode;
    @JsonProperty("pend_Flag")
    String pendFlag;
    @JsonProperty("rider")
    String rider;
    @JsonProperty("subscriber_Ded_OON")
    String subscriberDedOON;
    @JsonProperty("subscriber_Ded_PPO")
    String subscriberDedPPO;
    @JsonProperty("subscriber_Deductible")
    String subscriberDeductible;
    @JsonProperty("term_Date")
    String termDate;
    @JsonProperty("update_Date")
    String updateDate;
    @JsonProperty("update_User")
    String updateUser;
    @JsonProperty("use_Plan_CoIns")
    String usePlanCoIns;
    @JsonProperty("use_Plan_Ded")
    String usePlanDed;
    @JsonProperty("use_Plan_Max")
    String usePlanMax;
    @JsonProperty("use_Plan_OOP")
    String usePlanOOP;

}
