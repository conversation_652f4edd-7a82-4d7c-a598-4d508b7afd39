package com.covet.profile.service;


import com.covet.profile.clients.vba.VBADtos.Member.SubscriberMemberDto;
import com.covet.profile.dto.CapRxDto.drug.DrugListReqDto;
import com.covet.profile.dto.CapRxDto.drug.ImmutableDrugListReqDto;
import com.covet.profile.dto.CapRxDto.drug.request.PriceDrugParamsDto;
import com.covet.profile.dto.CapRxDto.drug.response.AlternativeDrugsDto;
import com.covet.profile.dto.CapRxDto.drug.response.*;
import com.covet.profile.dto.CapRxDto.drug.response.DrugItemDto;
import com.covet.profile.dto.CapRxDto.drug.response.CapDrugPriceListResponseDto;
import com.covet.profile.dto.CapRxDto.pharmacy.CapLocationReqDto;
import com.covet.profile.dto.CapRxDto.pharmacy.CapLocationResDto;
import com.covet.profile.dto.CapRxDto.pharmacy.CapPharmacyDto;
import com.covet.profile.dto.CapRxDto.pharmacy.PharmacyReqBodyDto;
import com.covet.profile.dto.CapRxDto.plan.CapRxMemberListResponseDto;
import com.covet.profile.persistence.repository.PrescriptionRepository;
import com.covet.profile.service.interfaces.ICapitalRxService;
import com.covet.profile.utils.AuthUtils;
import com.covet.profile.utils.ImageUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.nimbusds.jose.shaded.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class CapitalRxService implements ICapitalRxService {

    @Value("${spring.third-party.capital-rx.app-vendor-server}")
    private String appVendorServerUrl;
    @Value("${spring.third-party.capital-rx.adjudication-server}")
    private String adjudicationServer;

    private final RestTemplate restTemplate;

    private final ObjectMapper objectMapper;

    private final PrescriptionRepository prescriptionRepository;
    private final S3Service s3Service;

    public CapitalRxService(RestTemplate restTemplate, PrescriptionRepository prescriptionRepository, S3Service s3Service) {
        this.restTemplate = restTemplate;
        this.prescriptionRepository = prescriptionRepository;
        this.objectMapper = new ObjectMapper();
        this.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.s3Service = s3Service;
    }

    private String buildResponseAsString(String endpoint, String strJsonObject) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> requestEntity = new HttpEntity<>(strJsonObject, headers);
        return restTemplate.postForObject(endpoint, requestEntity, String.class);
    }

    public AlternativeDrugsDto getDrugById(int drugId) {
        String endpoint = appVendorServerUrl + "/drug/get";
        JSONObject personJsonObject = new JSONObject();
        personJsonObject.put("drug_id", drugId);
        String responseEntity = buildResponseAsString(endpoint, personJsonObject.toString());
        AlternativeDrugsDto alternativeDrugsDto = null;
        try {
            alternativeDrugsDto = objectMapper.readValue(responseEntity, AlternativeDrugsDto.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        return alternativeDrugsDto;

    }

    public JsonNode getPharmacies(PharmacyReqBodyDto pharmacyReqBodyDto) {
        String endpoint = adjudicationServer + "/pharmacy/list";
        String requestBody = null;
        try {
            requestBody = objectMapper.writeValueAsString(pharmacyReqBodyDto);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);
        ResponseEntity<String> responseEntityStr = restTemplate.postForEntity(endpoint, requestEntity, String.class);
        JsonNode root = null;
        try {
            root = objectMapper.readTree(responseEntityStr.getBody());
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        return root;

    }

    public ConvertDrugListDto getDrugList(String textSearch, int pageNumber) {
        var start = System.currentTimeMillis();
        UUID id = AuthUtils.getCognitoId();
        var getDrugFeature = CompletableFuture.supplyAsync(() -> {
            try {
                String endpoint = appVendorServerUrl + "/drug/list";
                DrugListReqDto drugListReqDto = ImmutableDrugListReqDto.of(textSearch, pageNumber);
                String requestBody = objectMapper.writeValueAsString(drugListReqDto);
                String responseEntity = buildResponseAsString(endpoint, requestBody);
                return objectMapper.readValue(responseEntity, DrugListDto.class);
            } catch (JsonProcessingException e) {
                return null;
            }
        });
        var getPrescriptionListFeature = CompletableFuture.supplyAsync(() ->
                prescriptionRepository.findAllByPrescriptionIdPatientID(id)
        );

        var getUrlMapFeature = CompletableFuture.supplyAsync(s3Service::getDefaultUrlMap);

        CompletableFuture.allOf(getDrugFeature, getPrescriptionListFeature, getUrlMapFeature).join();

        var result = getDrugFeature.join();
        var prescriptionList = getPrescriptionListFeature.join();
        var urlMap = getUrlMapFeature.join();

        List<ConvertDrugItemDto> convertDrugItemDto = new ArrayList<>();
        ConvertDrugListDto finalResult = new ConvertDrugListDto(convertDrugItemDto);

        for (DrugItemDto drugItem : result.getDrugList()) {
            List<DrugConfigDto> drugConfigurationList = drugItem.getDrug().getConfigurations();
            Map<String, List<DrugConfigDto>> configByForm = new HashMap<>();
            for (DrugConfigDto config : drugConfigurationList) {
                var form = ImageUtils.getMatchFormKey(config.getDosageForm().toLowerCase());
                config.setImgUrl(urlMap.get(form));
                if (configByForm.containsKey(config.getDosageForm())) {
                    configByForm.get(config.getDosageForm()).add(config);
                } else {
                    List<DrugConfigDto> configList = new ArrayList<>();
                    configList.add(config);
                    configByForm.put(config.getDosageForm(), configList);
                }
            }
            Boolean isExisted = !prescriptionList.isEmpty() && prescriptionList.stream()
                    .anyMatch(obj -> obj.getDrugID() == drugItem.getDrug().getId());

            ConvertDrugDto convertDrug = new ConvertDrugDto(drugItem.getDrug().getId(),
                    drugItem.getDrug().getDrugName(), drugItem.getDrug().getDrugType(), isExisted, configByForm);
            ConvertDrugItemDto convertDrugItem = new ConvertDrugItemDto(convertDrug);
            finalResult.getDrugItem().add(convertDrugItem);
        }

        var end = System.currentTimeMillis();
        log.info("done at {}", end - start);
        return finalResult;
    }

    public CapDrugPriceListResponseDto getDrugPrice(PriceDrugParamsDto priceDrugParamsDto) {
        String endpoint = appVendorServerUrl + "/drug/prices/list";
        String requestBody = null;
        try {
            requestBody = objectMapper.writeValueAsString(priceDrugParamsDto);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        String responseEntity = buildResponseAsString(endpoint, requestBody);
        CapDrugPriceListResponseDto result = null;
        try {
            result = objectMapper.readValue(responseEntity,
            CapDrugPriceListResponseDto.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        return result;
    }

    public CapLocationResDto getLocation(CapLocationReqDto capLocationResDto)
            throws JsonMappingException, JsonProcessingException {
        String endpoint = adjudicationServer + "/location/geolocate";
        String requestBody = objectMapper.writeValueAsString(capLocationResDto);
        String responseEntity = buildResponseAsString(endpoint, requestBody);
        CapLocationResDto test = objectMapper.readValue(responseEntity, CapLocationResDto.class);
        return test;
    }

    public CapPharmacyDto getPharmacyById(String npi) {
        String endpoint = adjudicationServer + "/pharmacy/get";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("npi", npi);
        String responseEntity = buildResponseAsString(endpoint, jsonObject.toString());
        CapPharmacyDto pharmacy = null;
        try {
            pharmacy = objectMapper.readValue(responseEntity, CapPharmacyDto.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        return pharmacy;
    }

    public List<CapPharmacyDto> mapJsonToPharmacies(JsonNode jsonNode) {
        List<CapPharmacyDto> pharmacies = new ArrayList<>();

        JsonNode resultsNode = jsonNode.get("results");
        if (resultsNode != null && resultsNode.isArray()) {
            ArrayNode arrayNode = (ArrayNode) resultsNode;

            for (JsonNode pharmacyNode : arrayNode) {
                JsonNode pharmacyDetailsNode = pharmacyNode.get("pharmacy");
                if (pharmacyDetailsNode != null) {
                    CapPharmacyDto pharmacy = null;
                    try {
                        pharmacy = objectMapper.treeToValue(pharmacyDetailsNode, CapPharmacyDto.class);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                    pharmacies.add(pharmacy);
                }
            }
        }

        return pharmacies;
    }

    public List<CapPharmacyDto> getCapitalRxPharmacies(PharmacyReqBodyDto pharmacyReqBodyDto) {
        JsonNode rootNode = getPharmacies(pharmacyReqBodyDto);
         List<CapPharmacyDto> pharmacies = mapJsonToPharmacies(rootNode);
        return pharmacies;
    }

    public CapRxMemberListResponseDto getMemberList() {
        String endpoint = adjudicationServer + "/member/list";
        String requestBody = "{}";

        String responseEntity = buildResponseAsString(endpoint, requestBody);
        CapRxMemberListResponseDto result = null;

        try {
            result = objectMapper.readValue(responseEntity,
                    CapRxMemberListResponseDto.class);

        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        return result;
    }

    public CapRxMemberListResponseDto getMember(SubscriberMemberDto subscriberID) {
        String endpoint = adjudicationServer + "/member/list";
        String requestBody;
        JSONObject jsonObject = new JSONObject();

        jsonObject.put("external_member_id", subscriberID.getSubscriberId());
        jsonObject.put("person_code", subscriberID.getMemberSequence());

        try {
            requestBody = objectMapper.writeValueAsString(jsonObject);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        String responseEntity = buildResponseAsString(endpoint, requestBody);
        CapRxMemberListResponseDto result;

        try {
            result = objectMapper.readValue(responseEntity,
                    CapRxMemberListResponseDto.class);

        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        return result;
    }
    public DrugListDto getDrugListOld(String textSearch, int pageNumber)
            throws JsonProcessingException {
        String endpoint = appVendorServerUrl + "/drug/list";
        DrugListReqDto drugListReqDto = ImmutableDrugListReqDto.of(textSearch, pageNumber);
        String requestBody = objectMapper.writeValueAsString(drugListReqDto);
        String responseEntity = buildResponseAsString(endpoint, requestBody);
        return objectMapper.readValue(responseEntity, DrugListDto.class);
    }

}
