package com.covet.profile.persistence.compositeKey;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

@Embeddable
@Data
@NoArgsConstructor
public class PrescriptionID implements Serializable {
    @Column(name = "prescription_name")
    private String prescriptionName;

    @Column(name = "prescription_code")
    private String prescriptionCode;

    //CH-116: Add drug as active prescription
    @JsonIgnore
    @Column(name = "patient_id")
    private UUID patientID;

    // default constructor

    public PrescriptionID(String prescriptionName, String prescriptionCode) {
        this.prescriptionName = prescriptionName;
        this.prescriptionCode = prescriptionCode;
    }

    public PrescriptionID(String prescriptionName, String prescriptionCode, UUID patientID) {
        this.prescriptionName = prescriptionName;
        this.prescriptionCode = prescriptionCode;
        this.patientID = patientID;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null) {
            return false;
        }
        if (getClass() != o.getClass()) {
            return false;
        }
        PrescriptionID prescription = (PrescriptionID) o;
        return Objects.equals(getPrescriptionName(), prescription.prescriptionName)
                && Objects.equals(getPrescriptionCode(), prescription.prescriptionCode) && Objects.equals(getPatientID(), prescription.patientID);
    }

    @Override
    public int hashCode() {
        return Objects.hash(getPrescriptionName(), getPrescriptionCode());
    }
}
