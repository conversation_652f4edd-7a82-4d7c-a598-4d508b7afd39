DO
$$
BEGIN
    CREATE TABLE IF NOT EXISTS patient_favorite (
              id uuid not null,
              patient_id uuid not null,
              favorite_id uuid not null,
              is_active boolean default true,
              favorite_type varchar(64) not null,
              created_at timestamp,
              updated_at timestamp,
              primary key (id),
              constraint fk_favorite_patient_id foreign key (patient_id) references profile
        );

    CREATE UNIQUE INDEX IF NOT EXISTS target_favorite_id ON patient_favorite (patient_id, favorite_id);
END
$$;