package com.covet.profile.service.google;

import com.google.maps.GeoApiContext;
import com.google.maps.OkHttpRequestHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class GoogleGeoApiContext {
    @Bean
    public GeoApiContext instantiateGeoContext() {
        try {
            return new GeoApiContext.Builder(
                    new OkHttpRequestHandler.Builder()
            )
                    .apiKey("google-api-key")
                    .build();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
