package com.covet.profile.dto.CapRxDto.member;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MemberDto implements Serializable {
    @JsonProperty("action_type")
    private String actionType;

    @JsonProperty("address_line_1")
    private String addressLine1;

    @JsonProperty("address_line_2")
    private String addressLine2;

    @JsonProperty("address_line_3")
    private String addressLine3;

    @JsonProperty("alternate_written_communication_mode")
    private String alternateWrittenCommunicationMode;

    @JsonProperty("beneficiary_identifier")
    private String beneficiaryIdentifier;

    @JsonProperty("birth_date")
    private Date birthDate;

    @JsonProperty("city")
    private String city;

    @JsonProperty("country")
    private String country;

    @JsonProperty("coverage_status")
    private String coverageStatus;

    @JsonProperty("coverage_type")
    private String coverageType;

    @JsonProperty("created_at")
    private Date createdAt;

    @JsonProperty("death_date")
    private Date deathDate;

    @JsonProperty("disenrollment_reason")
    private String disenrollmentReason;

    @JsonProperty("effective_date")
    private Date effectiveDate;

    @JsonProperty("email")
    private String email;

    @JsonProperty("encounter_member_id")
    private String encounterMemberId;

    @JsonProperty("encounter_member_id_qualifier")
    private String encounterMemberIdQualifier;

    @JsonProperty("end_date")
    private String endDate;

    @JsonProperty("external_member_id")
    private String externalMemberId;

    @JsonProperty("file_lock_end_date")
    private Date fileLockEndDate;

    @JsonProperty("file_lock_start_date")
    private Date fileLockStartDate;

    @JsonProperty("first_name")
    private String firstName;

    @JsonProperty("group_id")
    private int groupId;

    @JsonProperty("handicap_indicator")
    private String handicapIndicator;

    @JsonProperty("hios_id_naic")
    private String hiosIdNaic;

    @JsonProperty("id")
    private String id;

    @JsonProperty("identity")
    private String identity;

    @JsonProperty("is_allocated")
    private String isAllocated;

    @JsonProperty("language_code")
    private String languageCode;

    @JsonProperty("large_print")
    private boolean largePrint;

    @JsonProperty("last_name")
    private String lastName;

    @JsonProperty("legal_representative_address_line_1")
    private String legalRepresentativeAddressLine1;

    @JsonProperty("legal_representative_address_line_2")
    private String legalRepresentativeAddressLine2;

    @JsonProperty("legal_representative_city")
    private String legalRepresentativeCity;

    @JsonProperty("legal_representative_country")
    private String legalRepresentativeCountry;

    @JsonProperty("legal_representative_first_name")
    private String legalRepresentativeFirstName;

    @JsonProperty("legal_representative_last_name")
    private String legalRepresentativeLastName;

    @JsonProperty("legal_representative_state")
    private String legalRepresentativeState;

    @JsonProperty("legal_representative_zip_code")
    private String legalRepresentativeZipCode;

    @JsonProperty("line_of_business")
    private String lineOfBusiness;

    @JsonProperty("location")
    private String location;

    @JsonProperty("marketplace_metal_level")
    private String marketplaceMetalLevel;

    @JsonProperty("medicaid_id")
    private String medicaidId;

    @JsonProperty("medicare_beneficiary_identifier")
    private String medicareBeneficiaryIdentifier;

    @JsonProperty("medicare_indicator")
    private String medicareIndicator;

    @JsonProperty("miscellaneous_id")
    private String miscellaneousId;

    @JsonProperty("multiple_birth_code")
    private String multipleBirthCode;

    @JsonProperty("new_enrollee_effective_date")
    private String newEnrolleeEffectiveDate;

    @JsonProperty("other_payer_bin_number")
    private String otherPayerBinNumber;

    @JsonProperty("other_payer_effective_date")
    private String otherPayerEffectiveDate;

    @JsonProperty("other_payer_group_number")
    private String otherPayerGroupNumber;

    @JsonProperty("other_payer_member_identification_number")
    private String otherPayerMemberIdentificationNumber;

    @JsonProperty("other_payer_processor_control_number")
    private String otherPayerProcessorControlNumber;

    @JsonProperty("other_payer_termination_date")
    private String otherPayerTerminationDate;

    @JsonProperty("person_code")
    private String personCode;

    @JsonProperty("phone_number")
    private String phoneNumber;

    @JsonProperty("phone_number_qualifier")
    private String phoneNumberQualifier;

    @JsonProperty("ppo_name")
    private String ppoName;

    @JsonProperty("program")
    private String program;

    @JsonProperty("protected_member_address")
    private String protectedMemberAddress;

    @JsonProperty("relationship_code")
    private String relationshipCode;

    @JsonProperty("rn")
    private String rn;

    @JsonProperty("service_area_county")
    private String serviceAreaCounty;

    @JsonProperty("service_area_state")
    private String serviceAreaState;

    @JsonProperty("sex")
    private String sex;

    @JsonProperty("social_security_number")
    private String socialSecurityNumber;

    // @JsonProperty("spans")
    // private String spans;

    @JsonProperty("start_date")
    private Date startDate;

    @JsonProperty("state")
    private String state;

    @JsonProperty("termination_date")
    private Date terminationDate;

    @JsonProperty("test_member")
    private boolean testMember;

    @JsonProperty("udf_1")
    private String udf1;

    @JsonProperty("udf_10")
    private String udf10;

    @JsonProperty("udf_10_name")
    private String udf10Name;

    @JsonProperty("udf_11")
    private String udf11;

    @JsonProperty("udf_11_name")
    private String udf11Name;

    @JsonProperty("udf_12")
    private String udf12;

    @JsonProperty("udf_12_name")
    private String udf12Name;

    @JsonProperty("udf_13")
    private String udf13;

    @JsonProperty("udf_13_name")
    private String udf13Name;

    @JsonProperty("udf_14")
    private String udf14;

    @JsonProperty("udf_14_name")
    private String udf14Name;

    @JsonProperty("udf_15")
    private String udf15;

    @JsonProperty("udf_15_name")
    private String udf15Name;

    @JsonProperty("udf_1_name")
    private String udf1Name;

    @JsonProperty("udf_2")
    private String udf2;

    @JsonProperty("udf_2_name")
    private String udf2Name;

    @JsonProperty("udf_3")
    private String udf3;

    @JsonProperty("udf_3_name")
    private String udf3Name;

    @JsonProperty("udf_4")
    private String udf4;

    @JsonProperty("udf_4_name")
    private String udf4Name;

    @JsonProperty("udf_5")
    private String udf5;

    @JsonProperty("udf_5_name")
    private String udf5Name;

    @JsonProperty("udf_6")
    private String udf6;

    @JsonProperty("udf_6_name")
    private String udf6Name;

    @JsonProperty("udf_7")
    private String udf7;

    @JsonProperty("udf_7_name")
    private String udf7Name;

    @JsonProperty("udf_8")
    private String udf8;

    @JsonProperty("udf_8_name")
    private String udf8Name;

    @JsonProperty("udf_9")
    private String udf9;

    @JsonProperty("udf_9_name")
    private String udf9Name;

    @JsonProperty("verbal_language")
    private String verbalLanguage;

    @JsonProperty("written_language")
    private String writtenLanguage;

    @JsonProperty("zip_code")
    private String zipCode;
}
