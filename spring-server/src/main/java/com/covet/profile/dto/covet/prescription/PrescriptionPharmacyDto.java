package com.covet.profile.dto.covet.prescription;

import com.covet.profile.persistence.compositeKey.PrescriptionPharmacyID;
import org.immutables.value.Value;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.util.Optional;

@JsonSerialize(as = ImmutablePrescriptionPharmacyDto.class)
@JsonDeserialize(as = ImmutablePrescriptionPharmacyDto.class, builder = ImmutablePrescriptionPharmacyDto.Builder.class)
@Value.Immutable
public interface PrescriptionPharmacyDto {
    @Value.Parameter
    @JsonProperty
    Optional<PrescriptionPharmacyID> getPrescriptionPharmacy();

    @Value.Parameter
    @JsonProperty
    Double getCost();
}
