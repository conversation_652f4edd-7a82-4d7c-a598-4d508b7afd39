package com.covet.profile.converter.cigna;

import com.covet.profile.clients.cigna.response.common.Address;
import com.covet.profile.clients.cigna.response.practitioner.Name;
import com.covet.profile.clients.cigna.response.Resource;
import com.covet.profile.clients.cigna.response.practitioner.PractitionerResource;
import com.covet.profile.clients.npi.response.NpiProvider;
import com.covet.profile.dto.covet.profile.ImmutableProfileDto;
import com.covet.profile.dto.covet.provider.ImmutablePhysicianDetailDto;
import com.covet.profile.dto.covet.provider.PhysicianDetailDto;
import com.covet.profile.utils.FormatUtils;
import com.covet.profile.utils.ObjectToUuidUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;

@Component
public class PractitionerConverter {
    public PhysicianDetailDto convertToPhysicianDetailDto(PractitionerResource practitionerResource, Map<String, NpiProvider> npiInfoMap) {
        var name = CollectionUtils.isEmpty(practitionerResource.getName()) ? new Name() : practitionerResource.getName().get(0);
        var dateFmt = new SimpleDateFormat("yyyy-MM-dd");
        var birthDate = FormatUtils.formatDateTime(dateFmt, practitionerResource.getBirthDate());
        var profileAddress = Optional.ofNullable(practitionerResource.getAddress()).map(addressList -> addressList.stream()
                .filter(address -> !CollectionUtils.isEmpty(address.getLine()))
                .findFirst()
                .orElse(new Address())
        ).orElse(new Address());

        var addressText = CollectionUtils.isEmpty(profileAddress.getLine()) ? "unknown" : String.format("%s %s %s %s %s %s",
                profileAddress.getLine().get(0),
                profileAddress.getDistrict(),
                profileAddress.getCity(),
                profileAddress.getCountry(),
                profileAddress.getPostalCode(),
                profileAddress.getCountry());

        var profile = ImmutableProfileDto.of(
                Optional.empty(),
                CollectionUtils.isEmpty(name.getGiven()) ? "unknown": name.getGiven().get(0),
                Optional.empty(),
                name.getFamily(),

                Optional.empty(),
                Optional.empty(),

                "111-11-1111",
                Optional.ofNullable(birthDate),
                addressText,
                Optional.of(CollectionUtils.isEmpty(profileAddress.getLine()) ? "unknown": String.join(", ", profileAddress.getLine())),

                Optional.ofNullable(profileAddress.getCity()),
                Optional.ofNullable(profileAddress.getState()),
                Optional.ofNullable(profileAddress.getPostalCode()),
                Optional.empty(),
                Optional.empty(),
                Optional.empty());
        var identifier = Optional.ofNullable(practitionerResource.getIdentifier()).orElse(Collections.emptyList());
        var cignaNpiInfo = identifier.stream()
                .filter(item -> Objects.nonNull(item.getType())
                        && !CollectionUtils.isEmpty(item.getType().getCoding())
                        && item.getType().getCoding().stream().anyMatch(coding -> coding.getCode().equals("NPI"))
                )
                .findFirst();
        String npiValue = null;
        if (cignaNpiInfo.isPresent()) {
            npiValue = cignaNpiInfo.get().getValue();
        }

        return ImmutablePhysicianDetailDto.of(
                Optional.of(ObjectToUuidUtils.hashToUUID(practitionerResource.getId())),
                Optional.ofNullable(practitionerResource.getId()),
                true,
                true,
                Collections.emptyList(),
                addressText,
                Optional.of(CollectionUtils.isEmpty(profileAddress.getLine()) ? "unknown": String.join(", ", profileAddress.getLine())),
                Optional.ofNullable(profileAddress.getCity()),
                Optional.ofNullable(profileAddress.getState()),
                Optional.ofNullable(profileAddress.getPostalCode()),
                Optional.ofNullable(npiValue),
                Optional.of(0.0),
                Optional.of(0.0),
                Optional.of(0.0),
                Optional.ofNullable("CIGNA"),
                Optional.ofNullable("CONTINUE"),
                Optional.empty(),
                false,
                false,
                profile,
                Optional.empty(),
                Collections.emptyList(),
                Optional.ofNullable(npiInfoMap.get(npiValue)),
                Optional.empty()
        );
    }
}
