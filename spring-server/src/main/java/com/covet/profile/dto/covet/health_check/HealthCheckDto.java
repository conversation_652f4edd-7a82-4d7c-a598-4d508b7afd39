
package com.covet.profile.dto.covet.health_check;

import com.covet.profile.persistence.compositeKey.HealthCheckID;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

@JsonSerialize(as = ImmutableHealthCheckDto.class)
@JsonDeserialize(as = ImmutableHealthCheckDto.class, builder = ImmutableHealthCheckDto.Builder.class)
@Value.Immutable
public interface HealthCheckDto {
    @Value.Parameter
    @JsonProperty
    HealthCheckID getHealthCheckId();
}
