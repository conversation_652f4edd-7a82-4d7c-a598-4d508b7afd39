package com.covet.profile.converter;

import com.covet.profile.dto.CreateFavoriteRequest;
import com.covet.profile.persistence.model.PatientFavorite;

public class FavoriteConverter {

    public static PatientFavorite mapToEntity(CreateFavoriteRequest request) {
        var entity = new PatientFavorite();
        entity.setFavoriteType(request.favoriteType().getValue());
        entity.setIsActive(true);
        entity.setFavoriteId(request.favoriteId());
        return entity;
    }
}
