package com.covet.profile.dto.CapRxDto.plan;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CapRxPlanAccountDto {
    @JsonProperty("created_at")
    private String createdAt;

    @JsonProperty("end_date")
    private String endDate;

    @JsonProperty("external_account_id")
    private String externalAccountId;

    @JsonProperty("id")
    private int id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("plan_account_id")
    private int planAccountId;

    @JsonProperty("plan_id")
    private int planId;

    @JsonProperty("rn")
    private String rn;

    @JsonProperty("start_date")
    private String startDate;
}
