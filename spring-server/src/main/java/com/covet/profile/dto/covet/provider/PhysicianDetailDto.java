package com.covet.profile.dto.covet.provider;

import com.covet.profile.dto.covet.profile.ProfileDto;
import com.covet.profile.persistence.model.Network;
import com.covet.profile.persistence.model.PhysicianRating;
import com.covet.profile.persistence.model.Specialty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

@JsonSerialize(as = ImmutablePhysicianDetailDto.class)
@JsonDeserialize(as = ImmutablePhysicianDetailDto.class, builder = ImmutablePhysicianDetailDto.Builder.class)
@Value.Immutable
public interface PhysicianDetailDto {
    @Value.Parameter
    @JsonProperty
    Optional<UUID> getPhysicianId();

    @Value.Parameter
    @JsonProperty
    Optional<String> getExternalId();

    @Value.Parameter
    @JsonProperty
    Boolean getIsAcceptNewPatient();

    @Value.Parameter
    @JsonProperty
    Boolean getIsVirtual();

    @Value.Parameter
    @JsonProperty
    Set<Specialty> getSpecialties();

    @Value.Parameter
    @JsonProperty
    @NotBlank(message = "Address is mandatory")
    String getAddress();

    @Value.Parameter
    @JsonProperty
    Optional<String> getAddressLine();

    @Value.Parameter
    @JsonProperty
    Optional<String> getCity();

    @Value.Parameter
    @JsonProperty
    Optional<String> getState();

    @JsonInclude()
    @Value.Parameter
    @JsonProperty
    Optional<@Pattern(regexp = "^(\\d{5}|^$)$", message = "Check zip code format: xxxxx") String> getZipCode();

    @Value.Parameter
    @JsonProperty
    Optional<String> getNpi();

    @Value.Parameter
    @JsonProperty
    Optional<Double> getValue();

    @Value.Parameter
    @JsonProperty
    Optional<Double> getQuality();

    @Value.Parameter
    @JsonProperty
    Optional<Double> getEfficiency();

    @Value.Parameter
    @JsonProperty
    Optional<String> getSource();

    @Value.Parameter
    @JsonProperty
    Optional<String> getFacilityName();

    @Value.Parameter
    @JsonProperty
    Optional<Double> getDistance();

    @Value.Parameter
    @JsonProperty
    boolean getIsProviderInNetwork();

    @Value.Parameter
    @JsonProperty
    boolean getShowInNetworkTag();

    @Value.Parameter
    @JsonProperty
    ProfileDto getProfile();

    @Value.Parameter
    @JsonProperty
    Optional<PhysicianRating> getPhysicianRating();

    @Value.Parameter
    @JsonProperty
    Set<Network> getNetworks();

    @Value.Parameter
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    Optional<Object> npiInfo();

    @Value.Parameter
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    Optional<Boolean> isFavorite();
}