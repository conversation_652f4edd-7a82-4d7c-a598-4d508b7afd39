package com.covet.profile.persistence.specification;

import com.covet.profile.persistence.model.GroupProvider;
import com.covet.profile.persistence.model.GroupRating;
import com.covet.profile.searchCriteria.GroupProviderSearchCriteria;
import com.covet.profile.systemEnum.SysEnum;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.Order;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

public class GroupProviderSpec {
    private GroupProviderSpec() {

    }
    public static Specification<GroupProvider> hasFilterCriteriaIn(GroupProviderSearchCriteria criteria) {
        return ((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (criteria.getGroupSpecialty().isPresent()) {
                Set<String> values = Arrays.stream(SysEnum.EGroupSpecialty.values())
                        .map(SysEnum.EGroupSpecialty::getValue)
                        .collect(Collectors.toSet());
                boolean isGroupSpecialtyContained = criteria.getGroupSpecialty()
                        .get()
                        .stream()
                        .map(String::toLowerCase)
                        .anyMatch(values::contains);

                if (isGroupSpecialtyContained) {
                    predicates.add(
                            criteriaBuilder.lower(root.get("groupSpecialty")).in(criteria.getGroupSpecialty().get())
                    );
                }
            }

            Join<GroupProvider, GroupRating> groupRating = root.join("groupRating");
            criteria.getRating().ifPresent(
                    rating -> predicates.add(
                            criteriaBuilder.between(groupRating.get("averageRating"), rating, rating + 0.99)
                    )
            );
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        });
    }

    public static Specification<GroupProvider> hasSpecialtyLike(String keyword) {
        return (root, query, criteriaBuilder) ->
                criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("groupSpecialty")), "%" + keyword.toLowerCase() + "%"
                );
    }

    public static Specification<GroupProvider> hasGroupNameLike(String keyword) {
        return getGroupProviderSpecification(keyword, "groupName");
    }
    public static Specification<GroupProvider> hasGroupDescriptionLike(String keyword) {
        return getGroupProviderSpecification(keyword, "groupDescription");
    }

    private static Specification<GroupProvider> getGroupProviderSpecification(String keyword, String attributeName) {
        return (root, query, criteriaBuilder) ->
             criteriaBuilder.like(criteriaBuilder.lower(root.get(attributeName)), "%" + keyword.toLowerCase() + "%");
    }

    public static Specification<GroupProvider> hasRatingEqual(double rating) {
        return (root, query, criteriaBuilder) -> {
            Join<GroupProvider, GroupRating> groupRating = root.join("groupRating");
            return criteriaBuilder.between(groupRating.get("averageRating"), rating, rating + 0.99);
        };
    }

    public static Specification<GroupProvider> filterOnNumberValue(GroupProviderSearchCriteria criteria,
            Double lat, Double lng) {
        return (root, query, criteriaBuilder) -> {
            List<Order> sorters = new LinkedList<>();
            List<Predicate> predicates = new LinkedList<>();

            if (Objects.nonNull(criteria.getValue())) {

                sorters.add(criteriaBuilder.desc(root.get(GroupProvider.Fields.value)));

                predicates.add(criteriaBuilder.greaterThanOrEqualTo(
                        root.get(GroupProvider.Fields.value),
                        criteria.getValue()
                ));
            }

            if (Objects.nonNull(criteria.getDistance())) {

                Expression<Double> x_2 = criteriaBuilder.prod(
                        criteriaBuilder.diff(root.get(GroupProvider.Fields.latitude), lat),
                        criteriaBuilder.diff(root.get(GroupProvider.Fields.latitude), lat)
                );

                Expression<Double> y_2 = criteriaBuilder.prod(
                        criteriaBuilder.diff(root.get(GroupProvider.Fields.longitude), lng),
                        criteriaBuilder.diff(root.get(GroupProvider.Fields.longitude), lng)
                );

                Predicate distancePredicate = criteriaBuilder.lessThanOrEqualTo(
                        criteriaBuilder.sum(x_2, y_2),
                        Math.pow(criteria.getDistance(), 2)
                );

                Predicate coordinatesPredicate = criteriaBuilder.and(
                        criteriaBuilder.notEqual(root.get(GroupProvider.Fields.latitude), 0),
                        criteriaBuilder.notEqual(root.get(GroupProvider.Fields.longitude), 0)
                );

                sorters.add(criteriaBuilder.asc(criteriaBuilder.sum(x_2, y_2)));

                predicates.add(criteriaBuilder.and(distancePredicate, coordinatesPredicate));
            }

            query.orderBy(sorters);
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<GroupProvider> combineSpecs(List<Specification<GroupProvider>> specsList) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicatesList = new LinkedList<>();
            for (var item : specsList) {
                predicatesList.add(item.toPredicate(root, query, criteriaBuilder));
            }
            return criteriaBuilder.and(predicatesList.toArray(new Predicate[0]));
        };
    }
}
