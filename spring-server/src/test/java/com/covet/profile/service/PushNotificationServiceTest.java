package com.covet.profile.service;


import com.covet.profile.persistence.compositeKey.PrescriptionID;
import com.covet.profile.converter.NotificationConverter;
import com.covet.profile.converter.NotificationTemplateConverter;
import com.covet.profile.converter.SchedulerConverter;
import com.covet.profile.converter.notification.MedicineConverter;
import com.covet.profile.dto.reminder.template.MedicineTemplateDto;
import com.covet.profile.dto.PrescriptionSchedulerDto.*;
import com.covet.profile.dto.SchedulerDto;
import com.covet.profile.persistence.model.Notification;
import com.covet.profile.persistence.model.NotificationTemplate;
import com.covet.profile.persistence.model.Profile;
import com.covet.profile.persistence.model.Scheduler;
import com.covet.profile.persistence.repository.NotificationRepository;
import com.covet.profile.persistence.repository.NotificationTemplateRepository;
import com.covet.profile.persistence.repository.ProfileRepository;
import com.covet.profile.persistence.repository.SchedulerRepository;
import com.covet.profile.systemEnum.EDayOfWeek;
import com.covet.profile.systemEnum.ETemplateType;
import com.covet.profile.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.test.context.support.WithMockUser;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@SpringBootTest
@Slf4j
public class PushNotificationServiceTest {

    private final int pageNumberDefault = 0;
    private final int pageSizeDefault = 100;
    @InjectMocks
    PushNotificationService pushNotificationService;
    @Mock
    private FirebaseMessagingService messagingService;
    @Mock
    private SchedulerRepository schedulerRepository;
    @Mock
    private NotificationRepository notificationRepository;
    @Mock
    private NotificationTemplateRepository notificationTemplateRepository;
    @Mock
    private ProfileRepository profileRepository;
    @Mock
    private NotificationTemplateConverter notificationTemplateConverter;
    @Mock
    private SchedulerConverter schedulerConverter;
    @Mock
    private MedicineConverter medicineConverter;

    @Mock
    private NotificationConverter notificationConverter;

    @Mock
    Auth0Service service;

    @Test
    @WithMockUser
    void saveSchedulerSchemesSuccess() {

        UUID cognitoId = UUID.randomUUID();

        Profile profile = new Profile(cognitoId, "Huy", "Quoc", "Nguyen", null, null, "123456789", null,
                "2588 El Camino Real, Carlsbad, California, 92008", null, null, null, null, 0.0, 0.0);

        var activePrescription = ShortCutActivePrescription.builder()
                .prescriptionName("Skyrizi Pen")
                .prescriptionCode("00074210001")
                .quantity(50)
                .form("Tablet")
                .strength("1ml of 150mg/ml")
                .build();

        PushNotificationDto pushNotificationDto = new  PushNotificationDto();
        pushNotificationDto.setStartDate(LocalDate.now());
        pushNotificationDto.setEndDate(LocalDate.now());
        pushNotificationDto.setDayOfWeeks(List.of(EDayOfWeek.ALL));
        pushNotificationDto.setDosageTimes(List.of(DosageTimeDto.builder().time("10:00").dosage("1ml of 150mg/ml").build()));
        pushNotificationDto.setActivePrescription(activePrescription);
        pushNotificationDto.setTemplateType(ETemplateType.MEDICINE);
        pushNotificationDto.setRoute("ACTIVE_PRESCRIPTION");
        pushNotificationDto.setNote("take after eating");

        UUID schedulerId = UUID.randomUUID();
        var scheduler = new Scheduler(null,
                pushNotificationDto.getStartDate(),
                pushNotificationDto.getEndDate(),
                JsonUtils.toJson(pushNotificationDto.getDayOfWeeks()),
                null);

        when(schedulerRepository.save(eq(scheduler))).thenReturn(scheduler);
        when(profileRepository.getReferenceById(eq(cognitoId))).thenReturn(profile);

        List<Notification> notifications = new ArrayList<>();
        List<UUID> notiIds = new ArrayList<>();
        for (var item : pushNotificationDto.getDosageTimes()) {

            LocalTime realTime = LocalTime.parse(item.getTime());

            var dosage = item.getDosage();
            var note = pushNotificationDto.getNote();
            var medicineBuilder = medicineConverter.activePrescriptionToMedicineTemplateBuilder(
                    pushNotificationDto.getActivePrescription(), note, dosage
            );
            UUID templateId = UUID.randomUUID();

            var template = new NotificationTemplate(templateId,
                    pushNotificationDto.getTemplateType(),
                    pushNotificationDto.getRoute(),
                    UUID.randomUUID(),
                    JsonUtils.toJson(medicineBuilder),
                    null);
            when(notificationTemplateRepository.save(eq(template))).thenReturn(template);

            UUID notiId = UUID.randomUUID();
            Notification noti = new Notification(
                    notiId,
                    cognitoId,
                    templateId,
                    schedulerId,
                    realTime,
                    false,
                    false,
                    true,
                    scheduler,
                    profile,
                    template);
            notiIds.add(notiId);
            notifications.add(noti);
        }
        when(notificationRepository.saveAll(eq(notifications))).thenReturn(notifications);
        assertEquals(notiIds, notifications.stream().map(Notification::getNotificationId).toList());
    }


    @Test
    void updateSchedulerSuccessful() {

        UUID schedulerId = UUID.randomUUID();
        UUID notificationId = UUID.randomUUID();
        UUID cognitoId = UUID.fromString("60f28b19-f98e-3259-9938-b10e6bce8f6f");
        UUID templateId = UUID.randomUUID();

        SchedulerDto scheduler = SchedulerDto.builder()
                .schedulerId(schedulerId)
                .startDate(LocalDate.now())
                .endDate(LocalDate.now())
                .dayOfWeeks(List.of(EDayOfWeek.ALL))
                .build();

        NotificationDto notification = NotificationDto.builder()
                .notificationId(notificationId)
                .cognitoId(cognitoId)
                .templateId(templateId)
                .schedulerId(schedulerId)
                .startTime("09:00")
                .isPushed(true)
                .isRead(false)
                .isActive(true)
                .build();

        PrescriptionID prescriptionID = new PrescriptionID("Skyrizi Pen", "00074210001");

        MedicineTemplateDto medicine = MedicineTemplateDto.builder()
                .prescriptionId(prescriptionID)
                .quantity(50)
                .form("Tablet")
                .strength("1ml of 150mg/ml")
                .note("take after eating")
                .dosage("Dosage")
                .build();

        NotificationTemplateDto templateDto = NotificationTemplateDto.builder()
                .templateId(templateId)
                .templateType(ETemplateType.MEDICINE)
                .route("ACTIVE_PRESCRIPTION")
                .notificationInfo(medicine)
                .build();

        List<NotificationDto> notifications = List.of(notification);
        SchedulerPayloadDto schedulerPayloadDto = SchedulerPayloadDto.builder()
                .scheduler(scheduler)
                .notifications(notifications)
                .template(templateDto)
                .build();

        Scheduler schedulerEntity = schedulerConverter.toScheduler(scheduler);
        var notificationTemplateEntity = notificationTemplateConverter.toNotificationTemplate(templateDto);


        when(schedulerRepository
                .save(eq(schedulerEntity)))
                .thenReturn(schedulerEntity);

        when(notificationTemplateRepository
                .save(notificationTemplateConverter.toNotificationTemplate(eq(schedulerPayloadDto.getTemplate()))))
                .thenReturn(notificationTemplateEntity);

        Profile profile = new Profile(cognitoId, "Huy",
                "Quoc",
                "Nguyen",
                null,
                null,
                "123456789",
                null,
                "2588 El Camino Real, Carlsbad, California, 92008",
                null,
                null,
                null,
                null,
                0.0,
                0.0);

        when(profileRepository.getReferenceById(eq(cognitoId))).thenReturn(profile);

        List<Notification> notificationList = schedulerPayloadDto.getNotifications().stream()
                .map(dto -> notificationConverter.toNotification(dto, schedulerEntity, profile, notificationTemplateEntity))
                .toList();

        when(notificationRepository.saveAll(eq(notificationList))).thenReturn(notificationList);

        doNothing().when(mock(PushNotificationService.class)).updateScheduler(isA(SchedulerPayloadDto.class));

    }

}
