package com.covet.profile.dto.CapRxDto.pharmacy;

import com.covet.profile.dto.CapRxDto.plan.CapZipCodeDto;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

@JsonSerialize(as = ImmutableCapLocationReqDto.class)
@JsonDeserialize(as = ImmutableCapLocationReqDto.class, builder = ImmutableCapLocationReqDto.Builder.class)
@Value.Immutable
public interface CapLocationReqDto extends CapLocationResDto, CapZipCodeDto {

}
