package com.covet.profile.persistence.repository;

import com.covet.profile.persistence.model.Notification;
import com.covet.profile.systemEnum.ETemplateType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import java.util.UUID;

public interface NotificationRepository extends JpaRepository<Notification, UUID> {

    @Query("""
            select n from Notification n
            join fetch Scheduler s on n.schedulerId = s.schedulerId
            join fetch NotificationTemplate nt on nt.templateId = n.templateId
            where n.cognitoId = :cognitoId and s.startDate <= current_date
            and s.endDate >= current_date and nt.templateType = :type
            and n.isActive = true
            order by n.startTime
            """)
    Page<Notification> filterDailyMedicineNotification(@Param("cognitoId") UUID cognitoId,
            ETemplateType type,
            Pageable pageable);

    @Query("""
            select count(n) from Notification n
            join Scheduler s on n.schedulerId = s.schedulerId
            join NotificationTemplate nt on nt.templateId = n.templateId
            where n.cognitoId = :cognitoId and s.startDate <= current_date
            and s.endDate >= current_date and nt.templateType = :type
            and n.isActive = true and n.isRead = true
            """)
    Long countTaken(@Param("cognitoId") UUID cognitoId, ETemplateType type);

    @Query("""
            select count(n) from Notification n
            join Scheduler s on n.schedulerId = s.schedulerId
            join NotificationTemplate nt on nt.templateId = n.templateId
            where n.cognitoId = :cognitoId and s.startDate <= current_date
            and s.endDate >= current_date and nt.templateType = :type
            and n.isActive = true and n.isRead = false and n.startTime > current_time
            """)
    Long countRemain(@Param("cognitoId") UUID cognitoId, ETemplateType type);

    @Query("""
            select count(n) from Notification n
            join Scheduler s on n.schedulerId = s.schedulerId
            join NotificationTemplate nt on nt.templateId = n.templateId
            where n.cognitoId = :cognitoId and s.startDate <= current_date
            and s.endDate >= current_date and nt.templateType = :type
            and n.isActive = true and n.isRead = false and n.startTime < current_time
            """)
    Long countOverdue(@Param("cognitoId") UUID cognitoId, ETemplateType type);

    @Query("""
            select n from Notification n
            left join fetch Scheduler s on n.schedulerId = s.schedulerId
            left join fetch NotificationTemplate nt on n.templateId = nt.templateId
            where
            n.cognitoId = :cognitoId
            and s.startDate <= current_date
            and s.endDate >= current_date
            and n.isActive = true
            and n.isRead = :isRead
            order by n.startTime
            """)
    Page<Notification> filterDailyNotification(@Param("cognitoId") UUID cognitoId,
            @Param("isRead") boolean isRead,
            Pageable pageable);

    @Query("""
            select n from Notification n
            left join fetch Scheduler s on n.schedulerId = s.schedulerId
            left join fetch NotificationTemplate nt on n.templateId = nt.templateId
            where
            n.cognitoId = :cognitoId
            and s.startDate <= current_date
            and s.endDate >= current_date
            and n.isActive = true
            order by n.startTime
            """)
    Page<Notification> getAllDailyNotification(@Param("cognitoId") UUID cognitoId, Pageable pageable);

    @Query("""
            select n from Notification n
            left join fetch Scheduler s on n.schedulerId = s.schedulerId
            left join fetch NotificationTemplate nt on n.templateId = nt.templateId
            where
            n.cognitoId = :cognitoId
            and n.notificationId = :notificationId
            """)
    Notification dailyNotificationDetail(@Param("cognitoId") UUID cognitoId,
            @Param("notificationId") UUID notificationId);

    @Query("""
            select count(n) from Notification n
            join Scheduler s on n.schedulerId = s.schedulerId
            where n.cognitoId = :cognitoId
            and s.startDate <= current_date
            and s.endDate >= current_date
            and n.isActive = true
            and n.isRead = true
            """)
    Long countAllTaken(@Param("cognitoId") UUID cognitoId);

    @Query("""
            select count(n) from Notification n
            join Scheduler s on n.schedulerId = s.schedulerId
            where
            n.cognitoId = :cognitoId
            and s.startDate <= current_date
            and s.endDate >= current_date and n.isActive = true
            and n.isRead = false
            and n.startTime <= current_time
            """)
    Long countAllRemain(@Param("cognitoId") UUID cognitoId);

    @Query("""
            select count(n) from Notification n
            join Scheduler s on n.schedulerId = s.schedulerId
            where
            n.cognitoId = :cognitoId
            and s.startDate <= current_date
            and s.endDate >= current_date
            and n.isActive = true
            and n.isRead = false
            and n.startTime > current_time
            """)
    Long countAllOverdue(@Param("cognitoId") UUID cognitoId);
}