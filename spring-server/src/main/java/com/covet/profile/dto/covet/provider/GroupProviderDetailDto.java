package com.covet.profile.dto.covet.provider;

import com.covet.profile.persistence.model.Review;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.util.Optional;

@JsonSerialize(as = ImmutableGroupProviderDetailDto.class)
@JsonDeserialize(as = ImmutableGroupProviderDetailDto.class, builder = ImmutableGroupProviderDetailDto.Builder.class)
@Value.Immutable
public interface GroupProviderDetailDto {
    @Value.Parameter
    @JsonProperty
    GroupProviderDto getGroupProvider();

    @Value.Parameter
    @JsonProperty
    Optional<Review> getGroupTopReview();

    @Value.Parameter
    @JsonProperty
    long getGroupTotalReviews();
}
