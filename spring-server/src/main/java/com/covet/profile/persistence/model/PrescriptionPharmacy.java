package com.covet.profile.persistence.model;

import com.covet.profile.persistence.compositeKey.PrescriptionPharmacyID;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "prescription_pharmacy")

public class PrescriptionPharmacy implements Serializable {

    @EmbeddedId
    private PrescriptionPharmacyID id;

    @Column(name = "cost")
    @NotNull
    private Double cost;

    // relationship
    @JsonIgnore
    @ManyToOne()
    @JoinColumns({
            @JoinColumn(name = "prescription_name", referencedColumnName = "prescription_name", insertable = false, updatable = false),
            @JoinColumn(name = "prescription_code", referencedColumnName = "prescription_code", insertable = false, updatable = false),
            @JoinColumn(name = "patient_id", referencedColumnName = "patient_id", insertable = false, updatable = false)
    })
    private Prescription prescription;
}
