package com.covet.profile.dto.NpiDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class NpiAddress implements Serializable {
    @JsonProperty("country_code")
    private String countryCode;

    @JsonProperty("country_name")
    private String countryName;

    @JsonProperty("address_purpose")
    private String addressPurpose;

    @JsonProperty("address_type")
    private String addressType;

    @JsonProperty("address_1")
    private String Address1;

    @JsonProperty("city")
    private String city;

    @JsonProperty("state")
    private String state;

    @JsonProperty("postal_code")
    private String postalCode;

    @JsonProperty("telephone_number")
    private String telephoneNumber;

    @JsonProperty("fax_number")
    private String faxNumber;
}
