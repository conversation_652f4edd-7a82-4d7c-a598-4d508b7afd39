package com.covet.profile.responses;

import java.util.Date;
import java.util.UUID;

public class PriorAuthorResponse {
    private UUID priorAuthorId;
    private String requestMedication;
    private String requestOperation;
    private Date createdDate;
    private String physicianName;
    private String location;
    private String priorAuthorStatus;

    public PriorAuthorResponse(UUID priorAuthorId, String requestMedication, String requestOperation, Date createdDate,
            String physicianName, String location, String priorAuthorStatus) {
        this.priorAuthorId = priorAuthorId;
        this.requestMedication = requestMedication;
        this.requestOperation = requestOperation;
        this.createdDate = createdDate;
        this.physicianName = physicianName;
        this.location = location;
        this.priorAuthorStatus = priorAuthorStatus;
    }

    public UUID getPriorAuthorId() {
        return this.priorAuthorId;
    }

    public String getRequestMedication() {
        return this.requestMedication;
    }

    public void setRequestMedication(String requestMedication) {
        this.requestMedication = requestMedication;
    }

    public String getRequestOperation() {
        return this.requestOperation;
    }

    public void setRequestOperation(String requestOperation) {
        this.requestOperation = requestOperation;
    }

    public Date getCreatedDate() {
        return this.createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getPhysicianName() {
        return this.physicianName;
    }

    public void setPhysicianName(String physicianName) {
        this.physicianName = physicianName;
    }

    public String getLocation() {
        return this.location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getPriorAuthorStatus() {
        return this.priorAuthorStatus;
    }

    public void setPriorAuthorStatus(String priorAuthorStatus) {
        this.priorAuthorStatus = priorAuthorStatus;
    }
}
