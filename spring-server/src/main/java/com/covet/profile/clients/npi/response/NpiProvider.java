package com.covet.profile.clients.npi.response;

import com.covet.profile.dto.NpiDto.NpiAddress;
import com.covet.profile.dto.NpiDto.NpiPracticeLocation;
import com.covet.profile.dto.NpiDto.TaxonomyInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class NpiProvider implements Serializable {

    @JsonProperty("addresses")
    private List<NpiAddress> addresses;

    @JsonProperty("created_epoch")
    private String createdEpoch;

    @JsonProperty("enumeration_type")
    private String enumerationType;

    @JsonProperty("last_updated_epoch")
    private String lastUpdatedEpoch;

    @JsonProperty("number")
    private String number;

    private List<NpiPracticeLocation> practiceLocations;

    private List<TaxonomyInfo> taxonomies;
}
