package com.covet.profile.clients.cigna.response.location;

import com.covet.profile.clients.cigna.response.common.BaseMeta;
import com.covet.profile.clients.cigna.response.common.Link;
import com.covet.profile.clients.cigna.response.practitioner.PractitionerEntry;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

@Data
@NoArgsConstructor
public class LocationResponse {
    public String resourceType;
    public BaseMeta meta;
    public String type;
    public int total;
    public List<Link> link = Collections.emptyList();
    public List<LocationEntry> entry = Collections.emptyList();
}
