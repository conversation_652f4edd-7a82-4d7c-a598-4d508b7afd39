--Clone from VBA.IDCODE_GROUP
BEGIN;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'network_group'
    ) THEN
CREATE TABLE public.network_group (
                                network_id uuid,
                                group_id VARCHAR(30) NOT NULL,
                                network_code VARCHAR(64),
                                network_code_configured int,
                                network_code_detail VARCHAR(255),
                                created_at timestamp,
                                updated_at timestamp
);

BEGIN
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01002', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01002', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01002', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01002', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01002', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01002', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01002', N'T1_NETWORK', 1, N'Ohio PPO Connect');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01002', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01002', N'VBAGATEWAY', 1, N'E4E8D2E961BA4F3DA268B57B77B27A10');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01003', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01003', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01003', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01003', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01003', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01003', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01003', N'T1_NETWORK', 1, N'Ohio PPO Connect');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01003', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01003', N'VBAGATEWAY', 1, N'7878071265F744F8A2DA758296FB912A');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01004', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01004', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01004', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01004', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01004', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01004', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01004', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01004', N'T1_NETWORK', 1, N'Quality Care Partners Primary');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01004', N'T2_NETWORK', 1, N'Ohio PPO Connect');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01004', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01004', N'VBAGATEWAY', 1, N'8A13D2DADE714CD4A72EC3D260B099E2');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01005', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01005', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01005', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01005', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01005', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01005', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01005', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01005', N'T1_NETWORK', 1, N'Quality Care Partners Plus');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01005', N'T2_NETWORK', 1, N'Ohio PPO Connect');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01005', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01005', N'VBAGATEWAY', 1, N'4CFE0959EFD943A997EB6C880E2C710A');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01006', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01006', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01006', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01006', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01006', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01006', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01006', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01006', N'T1_NETWORK', 1, N'Quality Care Partners Plus');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01006', N'T2_NETWORK', 1, N'Ohio PPO Connect');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01006', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01006', N'VBAGATEWAY', 1, N'406A4792D1BA45728F1BD0560A6AB730');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01007', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01007', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01007', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01007', N'VBAGATEWAY', 1, N'E98807E925D846579C9F22D2F01EF751');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01008', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01008', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01008', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01008', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01008', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01008', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01008', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01008', N'T1_NETWORK', 1, N'Quality Care Partners Plus');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01008', N'T2_NETWORK', 1, N'Ohio PPO Connect');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01008', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01008', N'VBAGATEWAY', 1, N'4B0D29320ABF48DFA78CD1A582ED175D');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01009', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01009', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01009', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01009', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01009', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01009', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01009', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01009', N'T1_NETWORK', 1, N'Quality Care Partners Plus');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01009', N'T2_NETWORK', 1, N'Ohio PPO Connect');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01009', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01009', N'VBAGATEWAY', 1, N'8279FAF4237A433B9DB41FB58A3CB639');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01010', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01010', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01010', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01010', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01010', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01010', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01010', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01010', N'T1_NETWORK', 1, N'Quality Care Partners Plus');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01010', N'T2_NETWORK', 1, N'Ohio PPO Connect');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01010', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01010', N'VBAGATEWAY', 1, N'43DC52E1C00142D3BDBDAFA1D69F0BBC');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01011', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01011', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01011', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01011', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01011', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01011', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01011', N'T1_NETWORK', 1, N'Quality Care Partners Plus');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01011', N'T2_NETWORK', 1, N'Ohio PPO Connect');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01011', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01011', N'VBAGATEWAY', 1, N'2193B48F1A214D9D9BDA0D3A58E0ED83');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01012', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01012', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01012', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01012', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01012', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01012', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01012', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01012', N'T1_NETWORK', 1, N'Quality Care Partners Plus');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01012', N'T2_NETWORK', 1, N'Ohio PPO Connect');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01012', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01012', N'VBAGATEWAY', 1, N'E012BDAC275047429187EA30611008DD');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01013', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01013', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01013', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01013', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01013', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01013', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01013', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01013', N'T1_NETWORK', 1, N'Quality Care Partners Plus');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01013', N'T2_NETWORK', 1, N'Ohio PPO Connect');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01013', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01013', N'VBAGATEWAY', 1, N'799A2A7E083D4027A951AD87D364F10B');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01014', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01014', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01014', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01014', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01014', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01014', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01014', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01014', N'T1_NETWORK', 1, N'Quality Care Partners Plus');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01014', N'T2_NETWORK', 1, N'Ohio PPO Connect');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01014', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01014', N'VBAGATEWAY', 1, N'8DB4F5610CAF43FDB9F9E629519BCC41');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01015', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01015', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01015', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01015', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01015', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01015', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01015', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01015', N'T1_NETWORK', 1, N'Quality Care Partners Plus');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01015', N'T2_NETWORK', 1, N'Ohio PPO Connect');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01015', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01015', N'VBAGATEWAY', 1, N'350D3AE83AC844D185B4437CB9F6824B');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01016', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01016', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01016', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01016', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01016', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01016', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01016', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01016', N'T1_NETWORK', 1, N'Quality Care Partners Plus');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01016', N'T2_NETWORK', 1, N'Ohio PPO Connect');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01016', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01016', N'VBAGATEWAY', 1, N'425207F54CA843819F44D85E9315E5CB');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01017', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01017', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01017', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01017', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01017', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01017', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01017', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01017', N'T1_NETWORK', 1, N'Quality Care Partners Plus');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01017', N'T2_NETWORK', 1, N'Ohio PPO Connect');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01017', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01017', N'VBAGATEWAY', 1, N'70CB93E8C0D94B068E150E898ECE6282');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01018', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01018', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01018', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01018', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01018', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01018', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01018', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01018', N'T1_NETWORK', 1, N'Quality Care Partners Plus');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01018', N'T2_NETWORK', 1, N'Ohio PPO Connect');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01018', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'01018', N'VBAGATEWAY', 1, N'F8B8F8AA01D24840BD8F8532F44AB7AF');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'CG001', N'CIGNA', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'CG001', N'CONGRUITY', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'CG001', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'CG001', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'CG001', N'RXPREFERRED', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'CG001', N'T1_NETWORK', 1, N'Cigna');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'CG001', N'VBAGATEWAY', 1, N'9F62FD23FE5542B88AA0A4BBDACF0E4D');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'CG002', N'CIGNA', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'CG002', N'CONGRUITY', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'CG002', N'DIATHRIVE', 1, N'Option OFF');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'CG002', N'HEALTH_ROSETTA', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'CG002', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'CG002', N'RXPREFERRED', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'CG002', N'T1_NETWORK', 1, N'Cigna');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'CG002', N'VBAGATEWAY', 1, N'2737CA13B6CB4CDB8A47E8A24E9297CA');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'COVETINTERNAL', N'VBAGATEWAY', 1, N'BC26BA4E98A7446FAFD87B14DC533BE2');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'HP001', null, 0, N'Not Configured');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR001', N'CAPRX_TIER', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR001', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR001', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR001', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR001', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR001', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR001', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR001', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR001', N'T1_NETWORK', 1, N'Quality Care Partners (Mary Rutan)');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR001', N'T2_NETWORK', 1, N'Ohio PPO Connect, Quality Care Partners Plus');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR001', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR001', N'VBAGATEWAY', 1, N'7236FECE68AA484A930147A20AD317A1');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR002', N'CAPRX_TIER', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR002', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR002', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR002', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR002', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR002', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR002', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR002', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR002', N'T1_NETWORK', 1, N'Quality Care Partners (Mary Rutan)');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR002', N'T2_NETWORK', 1, N'Ohio PPO Connect, Quality Care Partners Plus');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR002', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR002', N'VBAGATEWAY', 1, N'321B1F8BC5634CAEB003D283BB5D74DF');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR003', N'CAPRX_TIER', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR003', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR003', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR003', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR003', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR003', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR003', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR003', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR003', N'T1_NETWORK', 1, N'Quality Care Partners (Mary Rutan)');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR003', N'T2_NETWORK', 1, N'Ohio PPO Connect, Quality Care Partners Plus');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR003', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR003', N'VBAGATEWAY', 1, N'EF11508847674CF39BA8AF7BC7C0E761');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR004', N'CAPRX_TIER', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR004', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR004', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR004', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR004', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR004', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR004', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR004', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR004', N'T1_NETWORK', 1, N'Quality Care Partners (Mary Rutan)');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR004', N'T2_NETWORK', 1, N'Ohio PPO Connect, Quality Care Partners Plus');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR004', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR004', N'VBAGATEWAY', 1, N'FE4264ED95884E27BC991F838C27E7B1');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR005', N'CAPRX_TIER', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR005', N'DIATHRIVE', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR005', N'LEAP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR005', N'MEDMO', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR005', N'OPPOC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR005', N'QCP', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR005', N'RXVALET', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR005', N'SDS', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR005', N'T1_NETWORK', 1, N'Quality Care Partners (Mary Rutan)');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR005', N'T2_NETWORK', 1, N'Ohio PPO Connect, Quality Care Partners Plus');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR005', N'TPAC', 1, N'Option ON');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR005', N'VBAGATEWAY', 1, N'2E39A845516E4A09AB373CF65B6D5442');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'MR006', N'VBAGATEWAY', 1, N'88F40D929A5B46DFB548B8275010DE87');
        INSERT INTO public.network_group (group_id, network_code, network_code_configured, network_code_detail) VALUES (N'SBS24', null, 0, N'Not Configured');
END;
BEGIN
        UPDATE public.network_group
        SET network_id = n.id
        FROM public.network n
        WHERE n.network_code = network_group.network_code;
END;
    ELSE
        RAISE NOTICE 'Table network_group already exists.';
END IF;
END $$;
END;
