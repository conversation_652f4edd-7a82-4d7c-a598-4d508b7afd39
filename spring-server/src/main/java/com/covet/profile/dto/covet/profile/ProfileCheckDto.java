package com.covet.profile.dto.covet.profile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;


public class ProfileCheckDto {

    @NotBlank(message = "First name is required")
    private String firstName;

    @NotBlank(message = "Last name is required")
    private String lastName;

    @NotBlank(message = "SSN is required")
    @Pattern(regexp = "^\\d{9}$", message = "Invalid SSN format")
    private String ssn;

    // @Past(message = "Birthday must be in the past")
    // @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @NotBlank(message = "Birthday is required")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\\.\\d{3}$", message = "Invalid birthday format, expected yyyy-MM-dd HH:mm:ss.SSS")
    private String birthday;

    // Getters
    public String getFirstName() {
        return firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public String getSsn() {
        return ssn;
    }
    public String getBirthday() {
        return birthday;
    }

    // Setters
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public void setSsn(String ssn) {
        this.ssn = ssn;
    }
    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }
}
