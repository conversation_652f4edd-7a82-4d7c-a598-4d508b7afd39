package com.covet.profile.clients.vba.VBAModels.Financial;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class VBAMemberAccum {


    @JsonProperty("subscriber_ID")
    private String subscriberID;

    @JsonProperty("member_Seq")
    private String memberSeq;

    @JsonProperty("group_ID")
    private String groupID;

    @JsonProperty("plan_Year")
    private String planYear;

    @JsonProperty("plan_Type")
    private String planType;

    @JsonProperty("benefit_Code")
    private String benefitCode;

    @JsonProperty("added_Accum")
    private String addedAccum;

    @JsonProperty("allowed_Amt")
    private BigDecimal allowedAmt;

    @JsonProperty("allowed_Amt_Net")
    private BigDecimal allowedAmtNet;

    @JsonProperty("allowed_Amt_OON")
    private BigDecimal allowedAmtOON;

    @JsonProperty("benefit_Used")
    private BigDecimal benefitUsed;

    @JsonProperty("benefit_Used_Net")
    private BigDecimal benefitUsedNet;

    @JsonProperty("benefit_Used_OON")
    private BigDecimal benefitUsedOON;

    @JsonProperty("billed_Amt")
    private BigDecimal billedAmt;

    @JsonProperty("billed_Amt_Net")
    private BigDecimal billedAmtNet;

    @JsonProperty("billed_Amt_OON")
    private BigDecimal billedAmtOON;

    @JsonProperty("coB_Amt")
    private BigDecimal coBAmt;

    @JsonProperty("coB_Amt_Net")
    private BigDecimal coBAmtNet;

    @JsonProperty("coB_Amt_OON")
    private BigDecimal coBAmtOON;

    @JsonProperty("coins_Amt")
    private BigDecimal coinsAmt;

    @JsonProperty("coins_Amt_Net")
    private BigDecimal coinsAmtNet;

    @JsonProperty("coins_Amt_OON")
    private BigDecimal coinsAmtOON;

    @JsonProperty("copay_Amt")
    private BigDecimal copayAmt;

    @JsonProperty("copay_Amt_Net")
    private BigDecimal copayAmtNet;

    @JsonProperty("copay_Amt_OON")
    private BigDecimal copayAmtOON;

    @JsonProperty("ded_Credit_OON_Used")
    private BigDecimal dedCreditOONUsed;

    @JsonProperty("ded_Credit_PPO_Used")
    private BigDecimal dedCreditPPOUsed;

    @JsonProperty("ded_Credit_Used")
    private BigDecimal dedCreditUsed;

    @JsonProperty("ded_PPO_Used")
    private BigDecimal dedPPOUsed;

    @JsonProperty("deductible_Used")
    private BigDecimal deductibleUsed;

    @JsonProperty("deductible_Used_OON")
    private BigDecimal deductibleUsedOON;

    @JsonProperty("not_Covered_Amt")
    private BigDecimal notCoveredAmt;

    @JsonProperty("not_Covered_Amt_Net")
    private BigDecimal notCoveredAmtNet;

    @JsonProperty("not_Covered_Amt_OON")
    private BigDecimal notCoveredAmtOON;

    @JsonProperty("out_Of_Pocket")
    private BigDecimal outOfPocket;

    @JsonProperty("out_Of_Pocket_Net")
    private BigDecimal outOfPocketNet;

    @JsonProperty("out_Of_Pocket_OON")
    private BigDecimal outOfPocketOON;
}
