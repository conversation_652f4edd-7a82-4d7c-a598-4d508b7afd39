package com.covet.profile.persistence.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "physician_address")
public class PhysicianAddress {

    @Id
    @Column(name = "id")
    private UUID id;

    @Column(name = "address")
    private String address;

}