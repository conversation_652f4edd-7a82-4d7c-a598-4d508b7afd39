package com.covet.profile.dto.CapRxDto.drug.response;

import com.covet.profile.dto.CapRxDto.drug.response.DrugConfigDto;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

import java.util.List;

@Data
public class DrugDto {

    @JsonProperty
    Integer id;

    @JsonProperty("drug_name")
    String drugName;

    @JsonProperty("drug_type")
    String drugType;

    @JsonProperty
    List<DrugConfigDto> configurations;

}
