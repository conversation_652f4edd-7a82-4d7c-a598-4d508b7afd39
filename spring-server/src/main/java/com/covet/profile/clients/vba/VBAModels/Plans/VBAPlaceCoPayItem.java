package com.covet.profile.clients.vba.VBAModels.Plans;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

import javax.persistence.Column;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class VBAPlaceCoPayItem {
    @Column(name = "Plan_ID")
    private String planId;

    @Column(name = "Benefit_Code")
    private String benefitCode;

    @Column(name = "Place_Code")
    private String placeCode;

    @Column(name = "Copay_Amt")
    private BigDecimal copayAmount;

    @Column(name = "Copay_Amt_Net")
    private BigDecimal copayAmountNet;
}
