package com.covet.profile.persistence.specification;

import com.covet.profile.persistence.model.Claim;
import com.covet.profile.persistence.model.ClaimPrescription;
import com.covet.profile.persistence.model.Prescription;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.Predicate;
import java.util.Optional;
import java.util.UUID;

public class ClaimPrescriptionSpec {
    public static Specification<ClaimPrescription> prescriptionClaims() {
        return (root, query, builder) -> {
            Join<ClaimPrescription, Claim> claimPrescription = root.join("claim");
            return builder.equal(claimPrescription.get("claimType"), 0);
        };
    }

    public static Specification<ClaimPrescription> prescriptionClaimsWithKeyword(Optional<String> keyword) {
        return (root, query, builder) -> {
            Join<ClaimPrescription, Claim> claimPrescription = root.join("claim");
            if (keyword.isPresent() && !keyword.get().isEmpty()) {
                Join<ClaimPrescription, Prescription> prescription = root.join("prescription");
                String lowercaseKeyword = keyword.get().toLowerCase();
                return builder.and(
                        builder.equal(claimPrescription.get("claimType"), 0),
                        builder.or(
                                builder.like(builder.lower(prescription.get("prescriptionId").get("prescriptionCode")), "%" + lowercaseKeyword + "%"),
                                builder.like(builder.lower(prescription.get("prescriptionId").get("prescriptionName")), "%" + lowercaseKeyword + "%"),
                                builder.like(builder.lower(prescription.get("form")), "%" + lowercaseKeyword + "%")
                        )
                );
            } else {
                return builder.equal(claimPrescription.get("claimType"), 0);
            }
        };
    }

    public static Specification<ClaimPrescription> claimsByPatientId(UUID patientId) {
        return (root, query, builder) -> {
            Join<ClaimPrescription, Claim> claimPrescription = root.join("claim");
            return builder.equal(claimPrescription.get("patientId"), patientId);
        };
    }

    public static Specification<ClaimPrescription> specificPrescriptionClaims(String prescriptionName,
            String prescriptionCode) {
        return (root, query, builder) -> {
            Predicate prescriptionNamePredicate = builder.equal(root.get("claimPrescriptionId").get("prescriptionName"),
                    prescriptionName);
            Predicate prescriptionCodePredicate = builder.equal(root.get("claimPrescriptionId").get("prescriptionCode"),
                    prescriptionCode);
            return builder.and(prescriptionNamePredicate,
                    prescriptionCodePredicate);
        };
    }

    public static Specification<ClaimPrescription> specificPrescriptionClaimsByPatientId(UUID patientId,
            String prescriptionName, String prescriptionCode) {
        return (root, query, builder) -> {
            Predicate prescriptionClaimsPredicate = prescriptionClaims().toPredicate(root, query, builder);
            Predicate claimsByPatientIdPredicate = claimsByPatientId(patientId).toPredicate(root, query, builder);
            Predicate specificPrescriptionClaimsPredicate = specificPrescriptionClaims(prescriptionName,
                    prescriptionCode).toPredicate(root, query, builder);
            return builder.and(prescriptionClaimsPredicate,
                    claimsByPatientIdPredicate,
                    specificPrescriptionClaimsPredicate);
        };
    }

}
