package com.covet.profile.clients.vba.VBAModels;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class VBAProvider {
    @JsonProperty("provider_ID")
    String providerId;
    @JsonProperty("first_Name")
    String firstName;
    @JsonProperty("middle_name")
    String middleName;
    @JsonProperty("last_Name")
    String lastName;
    @JsonProperty("org_Name")
    String orgName;
    @JsonProperty("accepting_Patients")
    boolean isAcceptingPatients;
    @JsonProperty("accepts_Capitation")
    boolean isAcceptsCapitation;
    @JsonProperty("accepts_Plan_Fees")
    boolean isAcceptsPlanFees;
    @JsonProperty("accepts_Withhold")
    boolean isAcceptsWithhold;
    @JsonProperty("provider_Type")
    String providerType;
    @JsonProperty("specialties")
    String specialties;
}
