package com.covet.profile.clients.vba.VBAModels.Plans;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

import javax.persistence.Column;

@Getter
@Setter
public class VBAPlaceCoInsuranceItem {
    @Column(name = "Plan_ID")
    String planId;

    @Column(name = "Benefit_Code")
    String benefitCode;

    @Column(name = "Place_Code")
    String placeCode;

    @Column(name = "Co_Insurance")
    BigDecimal coInsurance;

    @Column(name = "Co_Insurance_Net")
    BigDecimal coInsuranceNet;
}
