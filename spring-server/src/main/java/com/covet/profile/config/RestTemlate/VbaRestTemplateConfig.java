package com.covet.profile.config.RestTemlate;

import java.io.IOException;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;

import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.util.Base64Utils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.http.HttpRequest;
import org.springframework.http.HttpStatus;
import com.covet.profile.config.SecretMangerConfig;
import com.covet.profile.exception.RestTemplateExceptionHandler;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

@Configuration
public class VbaRestTemplateConfig {
    private String domain = "https://vbapi.vbasoftware.com/vbasoftware/";
    private String authPath = "user-authentication";

    private String vbasoftwareClientId;
    private String vbasoftwareDatabase;
    private String xApiKey;
    private String vbasoftwareClientCode;
    private String username;
    private String password;

    private String idToken;
    private int expiresIn;

    private Date loginAt;

    @Autowired
    public VbaRestTemplateConfig(SecretMangerConfig secretMangerUtils,
            @Value("${spring.third-party.vba.secret-name}") String vbaSecretName) throws Exception {
        Map<String, String> secrets = secretMangerUtils.getSecrets(vbaSecretName);
        this.vbasoftwareClientId = secrets.get("vbasoftwareClientId");
        this.vbasoftwareDatabase = secrets.get("vbasoftwareDatabase");
        this.xApiKey = secrets.get("xApiKey");
        this.vbasoftwareClientCode = secrets.get("vbasoftwareClientCode");
        this.username = secrets.get("username");
        this.password = secrets.get("password");
    }

    private void handleLogin() throws Exception {

        String endpoint = domain + authPath;
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setErrorHandler(new RestTemplateExceptionHandler());
        restTemplate.getInterceptors().add(interceptorForLogin());
        String responseString = restTemplate.postForObject(endpoint, null, String.class);
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(responseString);
        JsonNode authTokens = rootNode.at("/data/authenticationResult");
        this.idToken = authTokens.at("/idToken").asText();
        this.expiresIn = authTokens.at("/expiresIn").asInt();
        this.loginAt = new Date();

    }

    @Bean
    public RestTemplate vbaRestTemplate() {
        PoolingHttpClientConnectionManager poolingConnManager = new PoolingHttpClientConnectionManager();
        poolingConnManager.setMaxTotal(50);
        poolingConnManager.setDefaultMaxPerRoute(50);
        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(poolingConnManager)
                .build();
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);
        RestTemplate restTemplate = new RestTemplate(factory);
        restTemplate.setErrorHandler(new RestTemplateExceptionHandler());
        restTemplate.getInterceptors().add(interceptor());
        return restTemplate;
    }

    public HttpRequest buildRequest(HttpRequest request, String token) {
        HttpHeaders headers = request.getHeaders();
        headers.set("vbasoftware-client-id", vbasoftwareClientId);
        headers.set("vbasoftware-database", vbasoftwareDatabase);
        headers.set("x-api-key", xApiKey);
        headers.set("vbasoftware-client-code", vbasoftwareClientCode);
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", token);
        return request;
    }

    public ClientHttpRequestInterceptor interceptorForLogin() {
        return new ClientHttpRequestInterceptor() {
            @Override
            public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution)
                    throws IOException {
                String token = buildBasicAuthorizationHeader(username, password);
                HttpRequest customRequest = buildRequest(request, token);
                return execution.execute(customRequest, body);
            }
        };
    }

    public ClientHttpRequestInterceptor interceptor() {
        return new ClientHttpRequestInterceptor() {

            @Override
            public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution)
                    throws IOException {

                String replaceToken = "Bearer ";
                try {
                    if (idToken == null) {
                        handleLogin();
                    } else {
                        Date current = new Date();
                        Calendar c = Calendar.getInstance();
                        c.setTime(loginAt);
                        c.add(Calendar.HOUR, expiresIn / 3600);
                        Date expiredCalculation = c.getTime();
                        if (current.compareTo(expiredCalculation) > 0) {
                            handleLogin();
                        }
                    }
                } catch (Exception e) {
                    throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "VBA login fail!");
                }

                replaceToken += idToken;
                HttpRequest customRequest = buildRequest(request, replaceToken);
                return execution.execute(customRequest, body);
            }
        };

    }

    private String buildBasicAuthorizationHeader(String username, String password) {
        String authString = username + ":" + password;
        byte[] authBytes = authString.getBytes();
        String base64AuthString = Base64Utils.encodeToString(authBytes);
        return "Basic " + base64AuthString;
    }
}