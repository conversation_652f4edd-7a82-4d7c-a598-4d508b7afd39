package com.covet.profile.persistence.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sun.istack.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

import java.util.Date;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "discount")
public class Discount {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID discountId;

    @Column(name = "method_id")
    @NotNull
    private UUID methodId;

    @Column(name = "discount_type")
    private String discountType;

    @Column(name = "discount_code")
    private String discountCode;

    @Column(name = "created_date")
    private Date createAt;

    @Column(name = "expired_date")
    private Date expiredAt;

    // relationship
    @JsonIgnore
    @ManyToOne(cascade = CascadeType.ALL)
    @MapsId("methodId")
    @JoinColumn(name = "method_id")
    private HealthCareMethod healthCareMethod;
}
