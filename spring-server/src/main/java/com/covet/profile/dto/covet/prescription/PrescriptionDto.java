package com.covet.profile.dto.covet.prescription;

import com.covet.profile.persistence.compositeKey.PrescriptionID;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.util.Date;
import java.util.Optional;
import java.util.UUID;

@JsonSerialize(as = ImmutablePrescriptionDto.class)
@JsonDeserialize(as = ImmutablePrescriptionDto.class, builder = ImmutablePrescriptionDto.Builder.class)
@Value.Immutable
public interface PrescriptionDto {
    @Value.Parameter
    @JsonProperty
    Optional<PrescriptionID> getPrescriptionId();

    @Value.Parameter
    @JsonProperty
    Integer getQuantity();

    @Value.Parameter
    @JsonProperty
    String getForm();

    @Value.Parameter
    @JsonProperty
    String getStrength();

    @Value.Parameter
    @JsonProperty
    Optional<UUID> getPhysicianID();

    @Value.Parameter
    @JsonProperty
    Optional<Date> getInitRefillDate();

    @Value.Parameter
    @JsonProperty
    Optional<Date> getNextRefillDate();

    @Value.Parameter
    @JsonProperty
    int getDrugID();
}

