package com.covet.profile.dto.CapRxDto.claim;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class CapClaimPricingDto {

    @JsonProperty("accumulated_deductible_amount")
    Double accumulatedDeductibleAmount;

    @JsonProperty("amount_applied_to_periodic_deductible")
    Double amountAppliedToPeriodicDeductible;

    @JsonProperty("amount_exceeding_periodic_benefit_maximum")
    Double amountExceedingPeriodicBenefitMaximum;

    @JsonProperty("amount_of_copay")
    Double amountOfCopay;

    @JsonProperty("basis_of_calculation_copay")
    String basisOfCalculationCopay;

    @JsonProperty("basis_of_calculation_dispensing_fee")
    String basisOfCalculationDispensingFee;

    @JsonProperty("basis_of_reimbursement_determination")
    String basisOfReimbursementDetermination;

    @JsonProperty("dispensing_fee_paid")
    Double dispensingFeePaid;

    @JsonProperty("ingredient_cost_paid")
    Double ingredientCostPaid;

    @JsonProperty("patient_pay_amount")
    Double patientPayAmount;

    @JsonProperty("remaining_benefit_amount")
    Double remainingBenefitAmount;

    @JsonProperty("remaining_deductible_amount")
    Double remainingDeductibleAmount;

    @JsonProperty("total_amount_paid")
    Double totalAmountPaid;

}
