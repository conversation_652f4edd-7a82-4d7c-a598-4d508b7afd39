package com.covet.profile.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import javax.persistence.EntityManager;
import javax.persistence.EntityNotFoundException;
import javax.persistence.PersistenceContext;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import com.covet.profile.converter.PriorAuthorConverter;
import com.covet.profile.dto.PriorAuthorDto;
import com.covet.profile.persistence.model.Physician;
import com.covet.profile.persistence.model.PriorAuthor;
import com.covet.profile.persistence.repository.PhysicianRepository;
import com.covet.profile.persistence.repository.PriorAuthorRepository;
import com.covet.profile.responses.PriorAuthorPendingResponse;
import com.covet.profile.responses.PriorAuthorResponse;
import com.covet.profile.searchCriteria.PriorAuthorCriteria;
import com.covet.profile.persistence.specification.PriorAuthorSpec;

@Service
public class PriorAuthorService {
    @PersistenceContext
    private EntityManager entityManager;
    private final PriorAuthorRepository priorAuthorRepository;
    private final PhysicianRepository physicianRepository;
    private final Integer pageDefault = 0;
    private final Integer pageSizeDefault = 10;

    @Autowired
    PriorAuthorService(PriorAuthorRepository priorAuthorRepository,
            ProfileService profileService, PhysicianRepository physicianRepository) {
        this.priorAuthorRepository = priorAuthorRepository;
        this.physicianRepository = physicianRepository;
    }

    public List<PriorAuthorDto> getPriorAuthorsByPhysicianId(UUID physicianId) {
        return priorAuthorRepository.findByPhysicianId(physicianId).stream()
                .map(priorAuthor -> PriorAuthorConverter.priorAuthorToPriorAuthorDto(priorAuthor)).toList();
    }

    public PriorAuthor createOrUpdatePriorAuthor(PriorAuthor priorAuthor)
            throws EntityNotFoundException {
        return priorAuthorRepository.save(priorAuthor);
    }

    public PriorAuthorPendingResponse getPriorAuthorList(String priorAuthorStatus, Date startDate,
            Date endDate,
            String keyword) {

        // validate
        List<PriorAuthorResponse> priorAuthorList = priorAuthorRepository
                .getPriorAuthorList(priorAuthorStatus, startDate, endDate, keyword)
                .stream()
                .map(priorAuthor -> {
                    Physician physician = physicianRepository.getReferenceById(priorAuthor.getPhysicianId());
                    String physicianName = physician.getProfile().getFirstName();
                    String physicianLocation = physician.getAddress();
                    return new PriorAuthorResponse(priorAuthor.getId(), priorAuthor.getReqMed(), priorAuthor.getReqOp(),
                            priorAuthor.getCreatedDate(), physicianName, physicianLocation,
                            priorAuthor.getAuthorStatus());
                }).toList();
        int count = priorAuthorList.size();
        PriorAuthorPendingResponse PriorAuthorPendingResponse = new PriorAuthorPendingResponse();
        PriorAuthorPendingResponse.setTotalPendingStatus(count);
        PriorAuthorPendingResponse.setPriorAuthorList(priorAuthorList);
        return PriorAuthorPendingResponse;
    }

    public PriorAuthorPendingResponse getPriorAuthorListByConditions(String authorStatusValue,
            PriorAuthorCriteria criteria,
            Optional<Integer> page,
            Optional<Integer> pageSize) {

        List<String> physicianColumns = new ArrayList<>();
        physicianColumns.add("firstName");
        physicianColumns.add("address");

        List<String> priorAuthorColumns = new ArrayList<>();
        priorAuthorColumns.add("reqMed");
        priorAuthorColumns.add("reqOp");

        Specification<PriorAuthor> priorAuthorSpecs = PriorAuthorSpec.findByStatusValue(authorStatusValue)
                .and(PriorAuthorSpec.searchByKeywordOnPhysicianInfo(physicianColumns,
                        criteria.getKeyword())
                        .or(PriorAuthorSpec.searchByKeywordOnPriorAuthor(priorAuthorColumns, criteria.getKeyword())))
                .and(PriorAuthorSpec.withinRangeOfDate(criteria.getFromDate(),
                        criteria.getToDate()));
        Page<PriorAuthorResponse> priorAuthorResponse = priorAuthorRepository
                .findAll(priorAuthorSpecs, PageRequest.of(page.orElse(pageDefault), pageSize.orElse(pageSizeDefault)))
                .map(priorAuthor -> {
                    Physician physician = physicianRepository.getReferenceById(priorAuthor.getPhysicianId());
                    String physicianName = physician.getProfile().getFirstName();
                    String physicianLocation = physician.getAddress();
                    return new PriorAuthorResponse(priorAuthor.getId(), priorAuthor.getReqMed(), priorAuthor.getReqOp(),
                            priorAuthor.getCreatedDate(), physicianName, physicianLocation,
                            priorAuthor.getAuthorStatus());
                });
        long totalRecord = priorAuthorRepository.count(priorAuthorSpecs);
        PriorAuthorPendingResponse priorAuthorPendingResponse = new PriorAuthorPendingResponse();
        priorAuthorPendingResponse.setTotalPendingStatus(totalRecord);
        priorAuthorPendingResponse.setPriorAuthorList(priorAuthorResponse.toList());
        return priorAuthorPendingResponse;
    }

}
