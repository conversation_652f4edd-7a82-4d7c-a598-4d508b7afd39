package com.covet.profile.persistence.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sun.istack.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "visit_cost")
public class VisitCost {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @Column(name = "physician_id")
    @NotNull
    private UUID physicianId;

    @Column(name = "visit_cost")
    @NotNull
    private Double visitCost;

    @Column(name = "virtual_visit_cost")
    @NotNull
    private Double virtualVisitCost;

    // relationship
    @JsonIgnore
    @OneToOne(cascade = CascadeType.ALL)
    @MapsId("physicianId")
    @JoinColumn(name = "physician_id")
    private Physician physician;
}
