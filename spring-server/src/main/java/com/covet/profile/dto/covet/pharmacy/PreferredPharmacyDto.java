package com.covet.profile.dto.covet.pharmacy;

import com.covet.profile.dto.CapRxDto.pharmacy.CapPharmacyDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.net.URL;
import java.util.Optional;

@JsonSerialize(as = ImmutablePreferredPharmacyDto.class)
@JsonDeserialize(as = ImmutablePreferredPharmacyDto.class, builder = ImmutablePreferredPharmacyDto.Builder.class)
@Value.Immutable
public interface PreferredPharmacyDto {
    @Value.Parameter
    @JsonProperty
    CapPharmacyDto getPharmacyInfo();

    @Value.Parameter
    @JsonProperty
    Optional<Boolean> isPreferred();

    @Value.Parameter
    @JsonProperty
    double getDistance();

    @Value.Parameter
    @JsonProperty
    Optional<URL> getPharmacyImgUrl();

    @Value.Parameter
    @JsonProperty
    Double getCost();
}
