package com.covet.profile.converter.notification;

import com.covet.profile.converter.notification.response.MedicineNotificationResponse;
import com.covet.profile.dto.reminder.template.MedicineTemplateDto;
import com.covet.profile.persistence.model.Notification;
import com.covet.profile.systemEnum.ETakenPrescriptionStatus;
import com.covet.profile.utils.JsonUtils;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.time.LocalTime;
import java.util.Objects;

@Component
@Data
public class PrescriptionSchedulerConverter {

    public MedicineNotificationResponse convertToMedicineTemplateRes(Notification entity) {
        var result = MedicineNotificationResponse.builder();

        var notificationInfo = entity.getTemplate().getNotificationInfo();

        var medicineObj = JsonUtils.toObj(notificationInfo, MedicineTemplateDto.class);
        if (Objects.isNull(medicineObj)) {
            throw new RuntimeException("Fail to parse medicine template");
        }
        result.isActive(entity.getIsActive());
        result.notificationId(entity.getNotificationId());
        result.prescriptionId(medicineObj.getPrescriptionId());
        result.quantity(medicineObj.getQuantity());
        result.form(medicineObj.getForm());
        result.strength(medicineObj.getStrength());
        result.isPushed(entity.getIsPushed());
        result.dosage(medicineObj.getDosage());
        result.note(medicineObj.getNote());
        ETakenPrescriptionStatus status;
        if (entity.getIsRead()) {
            status = ETakenPrescriptionStatus.TAKEN;
        } else {
            if (LocalTime.now().isBefore(entity.getStartTime())) {
                status = ETakenPrescriptionStatus.COMING;
            } else {
                status = ETakenPrescriptionStatus.OVERDUE;
            }
        }
        result.status(status);
        result.startTime(entity.getStartTime());
        return result.build();
    }
}
