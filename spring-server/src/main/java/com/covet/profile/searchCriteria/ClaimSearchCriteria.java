package com.covet.profile.searchCriteria;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;
import java.util.Optional;
import java.util.Set;

@Data
public class ClaimSearchCriteria {
    @Parameter(description = "Search by Claim ID and Physician Name")
    public String keyword;

    @Parameter(description = "Values:  MEDICAL, PHARMACY, OTHER")
    public Set<String> claimType;

    public Boolean isPaid;

    public Integer price;

    @Temporal(TemporalType.TIMESTAMP)
    @DateTimeFormat(pattern= "MM/dd/yyyy")
    @Parameter(description = "MM/dd/yyyy")
    public Date fromDate;

    @Temporal(TemporalType.TIMESTAMP)
    @DateTimeFormat(pattern= "MM/dd/yyyy")
    @Parameter(description = "MM/dd/yyyy")
    public Date toDate;

    public Optional<String> getKeyword() {
        return Optional.ofNullable(keyword);
    }

    public Optional<Set<String>> getClaimType() {
        return Optional.ofNullable(claimType);
    }

    public Optional<Boolean> getIsPaid() {
        return Optional.ofNullable(isPaid);
    }

    public Optional<Integer> getPrice() {
        return Optional.ofNullable(price);
    }

    public Optional<Date> getFromDate() {
        return Optional.ofNullable(fromDate);
    }
    public Optional<Date> getToDate() {
        return Optional.ofNullable(toDate);
    }
}
