package com.covet.profile.service;

import com.covet.profile.converter.AppointmentConverter;
import com.covet.profile.converter.notification.MedicineConverter;
import com.covet.profile.persistence.model.*;
import com.covet.profile.persistence.repository.*;
import com.covet.profile.systemEnum.EDayOfWeek;
import com.covet.profile.systemEnum.ETemplateType;
import com.covet.profile.utils.JsonUtils;
import com.covet.profile.utils.ObjectToUuidUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.covet.profile.converter.NotificationConverter;
import com.covet.profile.converter.NotificationTemplateConverter;
import com.covet.profile.converter.SchedulerConverter;
import com.covet.profile.dto.PrescriptionSchedulerDto.PushNotificationDto;
import com.covet.profile.dto.PrescriptionSchedulerDto.SchedulerPayloadDto;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeParseException;
import java.util.*;


@Service
@RequiredArgsConstructor
@Slf4j
public class PushNotificationService {

    private final FirebaseMessagingService messagingService;
    private final SchedulerRepository schedulerRepository;
    private final NotificationRepository notificationRepository;
    private final NotificationTemplateRepository notificationTemplateRepository;
    private final ProfileRepository profileRepository;
    private final RegistrationFcmRepository registrationFcmRepository;
    private final Auth0Service auth0Service;
    private final NotificationTemplateConverter notificationTemplateConverter;
    private final SchedulerConverter schedulerConverter;
    private final NotificationConverter notificationConverter;
    private final MedicineConverter medicineConverter;
    private final int pageNumberDefault = 0;
    private final int pageSizeDefault = 100;

    /**
     * Creates a transaction to store the relationship of entities.
     *
     * @params PushNotificationDto: serialize the information related to scheduler
     * @return List<Notification>: created notifications of the user
     */
    @Transactional
    public List<Notification> saveSchedulerSchemes(PushNotificationDto pushNotificationDto) {

        Scheduler scheduler = schedulerRepository
                .save(new Scheduler(null,
                        pushNotificationDto.getStartDate(),
                        pushNotificationDto.getEndDate(),
                        JsonUtils.toJson(pushNotificationDto.getDayOfWeeks()),
                        null));

        UUID cognitoId = auth0Service.getCognitoId();
        UUID schedulerId = scheduler.getSchedulerId();

        Profile profile = profileRepository.getReferenceById(cognitoId);

        List<Notification> notifications = new ArrayList<>();
        for (var item : pushNotificationDto.getDosageTimes()) {
            try {

                LocalTime realTime = LocalTime.parse(item.getTime());

                var dosage = item.getDosage();
                var note = pushNotificationDto.getNote();
                var medicineBuilder = medicineConverter.activePrescriptionToMedicineTemplateBuilder(
                        pushNotificationDto.getActivePrescription(), note, dosage
                );

                NotificationTemplate template = notificationTemplateRepository
                        .save(new NotificationTemplate(null,
                                pushNotificationDto.getTemplateType(),
                                pushNotificationDto.getRoute(),
                                ObjectToUuidUtils.hashToUUID(medicineBuilder.getPrescriptionId()),
                                JsonUtils.toJson(medicineBuilder),
                                null));
                UUID templateId = template.getTemplateId();

                Notification noti = new Notification(
                        null,
                        cognitoId,
                        templateId,
                        schedulerId,
                        realTime,
                        false,
                        false,
                        true,
                        scheduler,
                        profile,
                        template);

                notifications.add(noti);
            } catch (DateTimeParseException e) {
                log.error("saveSchedulerSchemes wrong: {}", e.getMessage());
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Time(s) are wrong format");
            }

        }

        return notificationRepository.saveAll(notifications);
    }

    @Transactional
    public void createOrUpdateAppointmentNoti(Appointment appointment, boolean isUpdate) {

        if (isUpdate) {
            UUID appointmentId = appointment.getId();
            var notiTemplateOpt = notificationTemplateRepository.findByTargetId(appointmentId);
            if (notiTemplateOpt.isPresent()) {
                var appointmentTemplateDto = AppointmentConverter.appointmentToTemplateDto(appointment);
                notiTemplateOpt.get().setNotificationInfo(JsonUtils.toJson(appointmentTemplateDto));
                notificationTemplateRepository.saveAndFlush(notiTemplateOpt.get());
            }
        } else {
            var startTime = LocalDateTime.ofInstant(appointment.getStartTime().toInstant(), ZoneId.systemDefault());
            var endTime = LocalDateTime.ofInstant(appointment.getEndTime().toInstant(), ZoneId.systemDefault());

            var dateOfWeek = Arrays.stream(EDayOfWeek.values())
                    .filter(value -> endTime.getDayOfWeek().name().contains(value.name()))
                    .findFirst()
                    .orElse(EDayOfWeek.ALL);

            Scheduler scheduler = schedulerRepository
                    .save(new Scheduler(null,
                            startTime.toLocalDate(),
                            endTime.toLocalDate(),
                            JsonUtils.toJson(List.of(dateOfWeek.name())),
                            null));

            var appointmentTemplateDto = AppointmentConverter.appointmentToTemplateDto(appointment);

            NotificationTemplate template = notificationTemplateRepository
                    .save(new NotificationTemplate(null,
                            ETemplateType.APPOINTMENT,
                            ETemplateType.APPOINTMENT.name(),
                            appointment.getId(),
                            JsonUtils.toJson(appointmentTemplateDto),
                            null));
            UUID cognitoId = auth0Service.getCognitoId();
            UUID schedulerId = scheduler.getSchedulerId();
            Profile profile = profileRepository.getReferenceById(cognitoId);

            Notification noti = new Notification(
                    null,
                    cognitoId,
                    template.getTemplateId(),
                    schedulerId,
                    startTime.toLocalTime().minusHours(3),
                    false,
                    false,
                    true,
                    scheduler,
                    profile,
                    template);

            notificationRepository.save(noti);
            log.info("save appointment success!");
        }
    }

    /**
     * Creates a transaction to update relationship of entities.
     *
     * @params PushNotificationDto: serialize the information related to scheduler
     */
    @Transactional
    public void updateScheduler(SchedulerPayloadDto schedulerPayloadDto) {
        Scheduler scheduler = schedulerRepository
                .save(schedulerConverter.toScheduler(schedulerPayloadDto.getScheduler()));

        NotificationTemplate template = notificationTemplateRepository
                .save(notificationTemplateConverter.toNotificationTemplate(schedulerPayloadDto.getTemplate()));

        UUID cognitoId = auth0Service.getCognitoId();
        Profile profile = profileRepository.getReferenceById(cognitoId);

        List<Notification> notificationList = schedulerPayloadDto.getNotifications().stream()
                .map(dto -> notificationConverter.toNotification(dto, scheduler, profile, template)).toList();

        notificationRepository.saveAll(notificationList);
    }

    /**
     * deletes the specific scheduler. the cascade will delete the related entities
     * in the notification table.
     *
     * @params UUID schedulerId
     * @return HttpStatus Code 204: no-content at the controller.
     */
    public void deleteScheduler(UUID schedulerId) {
        Scheduler scheduler = schedulerRepository.getReferenceById(schedulerId);
        schedulerRepository.delete(scheduler);
    }

    public SchedulerPayloadDto getScheduler(UUID schedulerId) {
        Scheduler scheduler = schedulerRepository.findAllNotificationsBySchedulerId(schedulerId);
        return convertToSchedulerDetail(scheduler);
    }

    /**
     * converts the entities class to DTO. The method also convert the
     * notification's template dynamically.
     *
     * @params Scheduler scheduler
     * @return the object type of SchedulerPayloadDto: DTO contains scheduler
     *         detail, notification list and the template corresponding.
     */
    private SchedulerPayloadDto convertToSchedulerDetail(Scheduler scheduler) {
        SchedulerPayloadDto schedulerResponse = SchedulerPayloadDto.builder().build();
        schedulerResponse.setScheduler(schedulerConverter.toSchedulerDto(scheduler));
        List<Notification> notifications = scheduler.getNotificationList();
        if (!notifications.isEmpty()) {
            // all notifications reference to the same template
            var template = notificationTemplateConverter.toNotificationTemplateDto(notifications.get(0).getTemplate());
            schedulerResponse.setTemplate(template);
        }
        schedulerResponse.setNotifications(
                notifications
                        .stream()
                        .map(notificationConverter::toNotificationDto)
                        .toList()
        );

        return schedulerResponse;
    }

    /**
     * schedules the the notification system for all users.
     * join fetch 100 schedulers per page until retrieving entire records in the
     * scheduler table. Getting the notification satisfying the conditions for each
     * user
     * fetch the registration tokens for the users and send the payload to firebase
     * messaging service.
     *
     * @see Scheduler execute this function every 10 minutes
     */
    private boolean allowPush(LocalTime startTime) {
        List<LocalTime> ranges = new LinkedList<>();
        ranges.add(startTime);
        ranges.add(startTime.plusHours(1));
        ranges.add(startTime.plusHours(2));
        ranges.add(startTime.plusHours(3));
        LocalTime now = LocalTime.now();
        for (var time : ranges) {
            if (Math.abs(time.toSecondOfDay() - now.toSecondOfDay()) < 60) {
                return true;
            }
        }
        return false;
    }
    public void schedulePushNotifications() {
        Pageable page = PageRequest.of(pageNumberDefault, pageSizeDefault);
        var currentDate = LocalDate.now();
        var dayOfWeek = currentDate.getDayOfWeek().getValue();
        TypeReference<List<EDayOfWeek>> typeReference = new TypeReference<>() {
        };
        while (true) {
            Page<Scheduler> schedulerPage = schedulerRepository.findAllSchedulers(page);
            List<UUID> cogitoIdList = new ArrayList<>();
            List<Notification> notificationList = new ArrayList<>();

            schedulerPage.getContent().forEach(scheduler -> {
                var convertDayOfWeeks = JsonUtils.toObj(scheduler.getDayOfWeeks(), typeReference);
                if (Objects.nonNull(convertDayOfWeeks)) {

                    var isDayOfWeek = convertDayOfWeeks
                            .stream()
                            .map(EDayOfWeek::getValue)
                            .toList()
                            .contains(dayOfWeek) || convertDayOfWeeks.contains(EDayOfWeek.ALL);

                    if (!scheduler.getEndDate().isBefore(currentDate) &&
                            !scheduler.getStartDate().isAfter(currentDate) &&
                            isDayOfWeek
                    ) {
                        scheduler.getNotificationList().forEach(notification -> {
                            if (notification.getIsActive() &&
                                    !notification.getIsRead() &&
                                    allowPush(notification.getStartTime())) {
                                cogitoIdList.add(notification.getCognitoId());
                                notificationList.add(notification);
                            }
                        });
                    }
                }
            });
            List<String> registrationFCMs = registrationFcmRepository.findAllById(cogitoIdList).stream()
                    .map(RegistrationFCM::getRegistrationToken).toList();
            messagingService.sendNotification(registrationFCMs, notificationList);
            if (!schedulerPage.hasNext()) {
                break;
            }
            page.next();
        }
    }

    /**
     * resets the the notification status to schedule push notification system in
     * the next day.
     * join fetch 100 schedulers per page until retrieving entire records in the
     * scheduler table. Getting the notification which has isPushed equal to true
     * sets isPushed is false and store in the table again.
     */
    public void resetNotificationsStatus() {
        Pageable page = PageRequest.of(pageNumberDefault, pageSizeDefault);
        while (true) {
            Page<Scheduler> schedulerPage = schedulerRepository.findAllSchedulers(page);
            List<Notification> notificationList = new ArrayList<>();
            schedulerPage.getContent().forEach(scheduler -> {
                scheduler.getNotificationList().forEach(notification -> {
                    notification.setIsPushed(false);
                    notification.setIsRead(false);
                    notificationList.add(notification);
                });

            });
            notificationRepository.saveAll(notificationList);
            if (!schedulerPage.hasNext()) {
                break;
            }
            page.next();
        }
    }

//    @Scheduled(cron = "0 */1 * * * *") // Execute every 1 minutes
    public void schedulePushNotificationsTask() {
        schedulePushNotifications();
    }

//    @Scheduled(cron = "0 0 2 * * *") // Run at 2:00 AM every day
    public void scheduleResetNotificationsStatus() {
        resetNotificationsStatus();
    }
}