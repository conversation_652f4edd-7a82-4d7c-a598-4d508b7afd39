package com.covet.profile.clients.vba.VBADtos.Claim;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

@JsonSerialize(as = ImmutableVBAClaimDto.class)
@JsonDeserialize(as = ImmutableVBAClaimDto.class, builder = ImmutableVBAClaimDto.Builder.class)
@Value.Immutable
public interface VBAClaimDto {
    @Value.Parameter
    @JsonProperty
    long getClaimNumber();

    @Value.Parameter
    @JsonProperty
    String getSubscriberId();

    @Value.Parameter
    @JsonProperty
    String getMemberSeq();

    @Value.Parameter
    @JsonProperty
    String getProviderId();

    @Value.Parameter
    @JsonProperty
    String getClaimType();
}
