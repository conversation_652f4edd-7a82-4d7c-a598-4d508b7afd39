package com.covet.profile.dto.reminder.template;

import com.covet.profile.FactoryMethod.ParseNotiTemplate.BaseTemplateDto;
import com.covet.profile.persistence.compositeKey.PrescriptionID;

import lombok.*;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MedicineTemplateDto extends BaseTemplateDto implements Serializable {
    PrescriptionID prescriptionId;

    @Builder.Default
    int quantity = 0;

    @Builder.Default
    String imgUrl = "";

    @Builder.Default
    String form = "";

    @Builder.Default
    String strength = "";

    @Builder.Default
    String note = "";

    @Builder.Default
    String dosage = "";
}
