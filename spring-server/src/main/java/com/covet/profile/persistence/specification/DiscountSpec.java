package com.covet.profile.persistence.specification;

import java.util.UUID;

import org.springframework.data.jpa.domain.Specification;

import com.covet.profile.persistence.model.Discount;

public class DiscountSpec {
    public static Specification<Discount> getDiscountsByHealthCareMethodId(UUID methodId) {
        return (root, query, builder) -> {
            return builder.equal(root.get("methodId"), methodId);
        };
    }
}