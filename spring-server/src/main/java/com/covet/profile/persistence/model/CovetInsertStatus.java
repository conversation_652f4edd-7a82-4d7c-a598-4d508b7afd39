package com.covet.profile.persistence.model;

import lombok.Data;

import javax.persistence.*;
import java.util.UUID;

@Data
@Entity
@Table(name = "covet_insert_status")
public class CovetInsertStatus {
    @Id
    @Column(name = "id")
    @GeneratedValue(
            strategy = GenerationType.AUTO
    )
    UUID id;

    @Column(name = "status")
    String status;

    @Column(name = "current_chunk")
    Integer currentChunk;

    @Column(name = "key_name")
    String keyName;
}
