package com.covet.profile.dto.CapRxDto.plan;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import org.hibernate.mapping.List;

@Data
public class CapRxGroupDto implements Serializable {
    @JsonProperty("accumulation_period_type")
    private String accumulationPeriodType;

    @JsonProperty("address_line_1")
    private String addressLine1;

    @JsonProperty("address_line_2")
    private String addressLine2;

    @JsonProperty("city")
    private String city;

    @JsonProperty("coverage_effective_date")
    private String coverageEffectiveDate;

    @JsonProperty("coverage_strategy_id")
    private String coverageStrategyId;

    @JsonProperty("created_at")
    private String createdAt;

    @JsonProperty("customer_service_phone_number")
    private String customerServicePhoneNumber;

    @JsonProperty("effective_date")
    private Date effectiveDate;

    @JsonProperty("end_date")
    private Date endDate;

    @JsonProperty("external_group_id")
    private String externalGroupId;

    @JsonProperty("fax")
    private String fax;

    @JsonProperty("group_name")
    private String groupName;

    @JsonProperty("id")
    private String id;

    @JsonProperty("phone_number")
    private String phoneNumber;

    @JsonProperty("plan_account_id")
    private String planAccountId;

    @JsonProperty("rn")
    private String rn;

    @JsonProperty("send_group_in_outbound_accumulator_data")
    private boolean sendGroupInOutboundAccumulatorData;

    // @JsonProperty("spans")
    // private List spans;

    @JsonProperty("start_date")
    private Date startDate;

    @JsonProperty("state")
    private String state;

    @JsonProperty("termination_date")
    private Date terminationDate;

    @JsonProperty("version_created_at")
    private Date versionCreatedAt;

    @JsonProperty("zip_code")
    private String zipCode;

}
