package com.covet.profile.clients.cigna.response.practitioner;

import com.covet.profile.clients.cigna.response.common.Address;
import com.covet.profile.clients.cigna.response.common.BaseResource;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class PractitionerResource extends BaseResource {
    private boolean active;
    private List<Name> name;
    private String gender;
    private String birthDate;
    private List<PractitionerExtension> extension;
    private List<Address> address;
    private List<Qualification> qualification;
    private List<Communication> communication;
}
