package com.covet.profile.persistence.model;

import com.covet.profile.persistence.compositeKey.GroupPhysicianKey;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import com.sun.istack.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "group_physician")
@JsonIdentityInfo(
        generator = ObjectIdGenerators.PropertyGenerator.class,
        property = "id")
public class GroupPhysician {

    @EmbeddedId
    GroupPhysicianKey id;
    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @MapsId("groupId")
    @JoinColumn(name = "group_id")
    GroupProvider group;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @MapsId("physicianId")
    @JoinColumn(name = "physician_id")
    Physician physician;

    @Column(name = "created_date")
    @NotNull
    private Date createdDate;

    @Column(name = "updated_date")
    @NotNull
    private Date updatedDate;

    @Column(name = "in_group")
    @NotNull
    private Boolean isInGroup;
}
