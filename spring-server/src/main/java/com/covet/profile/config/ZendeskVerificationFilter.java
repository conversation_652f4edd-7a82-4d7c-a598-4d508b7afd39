package com.covet.profile.config;

import com.covet.profile.utils.SecretManagerUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Enumeration;
import java.util.Map;

@Slf4j
public class ZendeskVerificationFilter extends OncePerRequestFilter {

    @Value("${spring.third-party.zendesk.appointment-endpoint-url}")
    public String endpointUrl;

    private final String apiKey;

    public ZendeskVerificationFilter(SecretManagerUtils secretMangerUtil, @Value("${spring.third-party.zendesk.secret-name-webhook-appointment}") String webhookSecretName) throws Exception {
        this.apiKey = getApiKeyFromSecretsManager(secretMangerUtil, webhookSecretName);
    }
    private String getApiKeyFromSecretsManager(SecretManagerUtils secretMangerUtil, String webhookSecretName) throws Exception {
        Map<String, String> secrets = secretMangerUtil.getSecrets(webhookSecretName);
        return secrets.get("X-API-KEY");
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        logRequestHeaders(request);
        if (endpointUrl.equals(request.getRequestURI())) {
            String apiKeyHeader = request.getHeader("x-api-key");
            if (!this.apiKey.equals(apiKeyHeader)) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("Invalid API Key");
                log.error("Invalid API Key");
                return;
            }
        }

        filterChain.doFilter(request, response);
    }

    private void logRequestHeaders(HttpServletRequest request) {
        Enumeration<String> headerNames = request.getHeaderNames();
        if (headerNames != null) {
            if(!("Zendesk Webhook".equals(request.getHeader("User-Agent")))){
                // if zendesk webhook then log else ignore
                return;
            }
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);
                log.info("Zendesk Header: {} - Value: {}", headerName, headerValue);
            }
        } else {
            log.info("No headers found in the request.");
        }
    }
}
