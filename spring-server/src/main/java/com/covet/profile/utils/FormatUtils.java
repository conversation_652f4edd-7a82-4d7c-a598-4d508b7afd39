package com.covet.profile.utils;


import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@UtilityClass
@Slf4j
public class FormatUtils {
    public static String formatNdc(String input) {
        String ndcPattern = "(\\d{5})(\\d{4})(\\d{2})";
        String fileExtension = ".jpg";
        Pattern regex = Pattern.compile(ndcPattern);
        Matcher matcher = regex.matcher(input);
        if (matcher.matches()) {
            return matcher.group(1) + "-" + matcher.group(2) + "-" + matcher.group(3) + fileExtension;
        }
        return "";
    }

    public static boolean isMatchUUID (String input) {
        Pattern UUID_REGEX =
                Pattern.compile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");

        return UUID_REGEX.matcher(input).matches();
    }

    public static Date formatDateTime(SimpleDateFormat dateTimeFmt, String text) {
        try {
            return dateTimeFmt.parse(text);
        } catch (Exception e) {
            log.error("Cannot parse the str: {} to datetime", text);
        }
        return null;
    }
}
