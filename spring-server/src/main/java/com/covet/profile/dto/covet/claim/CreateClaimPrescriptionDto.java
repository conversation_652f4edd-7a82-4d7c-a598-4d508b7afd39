package com.covet.profile.dto.covet.claim;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.util.Date;

@JsonSerialize(as = ImmutableCreateClaimPrescriptionDto.class)
@JsonDeserialize(as = ImmutableCreateClaimPrescriptionDto.class, builder = ImmutableCreateClaimPrescriptionDto.Builder.class)
@Value.Immutable
public interface CreateClaimPrescriptionDto {

    @Value.Parameter
    @JsonProperty
    String getPrescriptionName();

    @Value.Parameter
    @JsonProperty
    String getPrescriptionCode();

    @Value.Parameter
    @JsonProperty
    String getClaimCode();

    @Value.Parameter
    @JsonProperty
    String getPharmacyId();

    @Value.Parameter
    @JsonProperty
    Date getInitRefillDate();

    @Value.Parameter
    @JsonProperty
    Date getNextRefillDate();


}
