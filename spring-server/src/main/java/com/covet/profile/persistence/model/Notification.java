package com.covet.profile.persistence.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sun.istack.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;

import java.time.LocalTime;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "notification")
@FieldNameConstants
public class Notification {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID notificationId;

    @Column(name = "cognito_id")
    @NotNull
    private UUID cognitoId;

    @Column(name = "template_id")
    @NotNull
    private UUID templateId;

    @Column(name = "scheduler_id")
    @NotNull
    private UUID schedulerId;

    @Column(name = "start_time")
    @NotNull
    private LocalTime startTime;

//    private String description;
//    private String title;

    @Column(name = "is_pushed")
    @NotNull
    private Boolean isPushed;

    @Column(name = "is_read")
    @NotNull
    private Boolean isRead;

    @Column(name = "is_active")
    @NotNull
    private Boolean isActive;

    // relationship
    @JsonIgnore
    @ManyToOne
    @MapsId("schedulerId")
    @JoinColumn(name = "scheduler_id", referencedColumnName = "id", insertable = false, updatable = false)
    private Scheduler scheduler;

    @JsonIgnore
    @ManyToOne
    @MapsId("cognitoId")
    @JoinColumn(name = "cognito_id", referencedColumnName = "cognito_id", insertable = false, updatable = false)
    private Profile profile;

    @JsonIgnore
    @ManyToOne
    @MapsId("templateId")
    @JoinColumn(name = "template_id", referencedColumnName = "id", insertable = false, updatable = false)
    private NotificationTemplate template;
}
