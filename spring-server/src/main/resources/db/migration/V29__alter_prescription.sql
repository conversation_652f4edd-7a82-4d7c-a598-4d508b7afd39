-- Step 1: Drop the existing foreign key and primary key constraint --
BEGIN;

--     select * from information_schema.table_constraints
--     WHERE table_name = 'claim_prescription';
--
-- SELECT *
-- FROM information_schema.table_constraints
-- WHERE table_name = 'prescription';

DO $$
    BEGIN
        IF EXISTS (
                SELECT constraint_name
                FROM information_schema.table_constraints
                WHERE table_name = 'sale_prescription'
                  AND constraint_name = 'fk_sale_prescription_name' AND constraint_type = 'FOREIGN KEY'
            ) THEN
            ALTER TABLE sale_prescription
                DROP CONSTRAINT fk_sale_prescription_name;
        END IF;
    END $$;

DO $$
    BEGIN
        IF EXISTS (
                SELECT constraint_name
                FROM information_schema.table_constraints
                WHERE table_name = 'claim_prescription'
                  AND constraint_name = 'fk_for_prescription' AND constraint_type = 'FOREIGN KEY'
            ) THEN
            ALTER TABLE claim_prescription
                DROP CONSTRAINT fk_for_prescription;
        END IF;
    END $$;

-- ALTER TABLE claim_prescription
-- DROP CONSTRAINT fk_for_prescription;

DO $$
    BEGIN
        IF EXISTS (
                SELECT constraint_name
                FROM information_schema.table_constraints
                WHERE table_name = 'prescription'
                  AND constraint_name = 'prescription_pkey' AND constraint_type = 'PRIMARY KEY'
            ) THEN
            ALTER TABLE prescription
                DROP CONSTRAINT prescription_pkey;
        END IF;
    END $$;

-- Step 2: Alter the table to add the "patient_id", "init_refill_date", "next_refill_date" column
-- Check if the column exists
DO $$
    BEGIN
        IF NOT EXISTS (
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'prescription'
                  AND (column_name = 'patient_id')
            ) THEN
            -- Add the column
            ALTER TABLE prescription
                ADD COLUMN patient_id UUID DEFAULT '84d82c9f-**************-4fa8326299c6' NOT NULL;
        END IF;
    END $$;

DO $$
    BEGIN
        IF NOT EXISTS (
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'prescription'
                  AND column_name in ('init_refill_date', 'next_refill_date')
            ) THEN
            -- Add the column
            alter table prescription add init_refill_date timestamp;
            alter table prescription add next_refill_date timestamp;
        END IF;
    END $$;

-- Step 3: Create a new primary key constraint

DO $$
    BEGIN
        IF NOT EXISTS (
                SELECT conname
                FROM pg_constraint
                WHERE conrelid = 'prescription'::regclass
                  AND contype = 'p'
            ) THEN
            ALTER TABLE prescription
                ADD PRIMARY KEY (prescription_name, prescription_code, patient_id);

            ALTER TABLE prescription
                ADD CONSTRAINT prescription_unique_constraint UNIQUE (prescription_name, prescription_code);
        END IF;
    END $$;



ALTER TABLE claim_prescription
    ADD CONSTRAINT fk_for_prescription FOREIGN KEY (prescription_name, prescription_code) REFERENCES prescription(prescription_name, prescription_code);

ALTER TABLE sale_prescription
    ADD CONSTRAINT fk_sale_prescription_name FOREIGN KEY (prescription_name, prescription_code) REFERENCES prescription(prescription_name, prescription_code);

--Step 4: Add corresponding data from claim prescription

UPDATE prescription p
SET patient_id = c.patient_id
FROM claim_prescription cp
         JOIN claim c ON c.claim_code = cp.claim_code
WHERE cp.prescription_name = p.prescription_name
  AND p.prescription_code = cp.prescription_code;

COMMIT;