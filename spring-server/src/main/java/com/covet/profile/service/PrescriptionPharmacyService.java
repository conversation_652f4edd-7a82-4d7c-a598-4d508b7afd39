
package com.covet.profile.service;

import com.covet.profile.persistence.compositeKey.PrescriptionID;
import com.covet.profile.persistence.compositeKey.PrescriptionPharmacyID;
import com.covet.profile.converter.PrescriptionPharmacyConverter;
import com.covet.profile.dto.covet.prescription.PrescriptionPharmacyDto;
import com.covet.profile.persistence.model.Prescription;
import com.covet.profile.persistence.model.PrescriptionPharmacy;
import com.covet.profile.persistence.repository.PrescriptionPharmacyRepository;
import com.covet.profile.utils.AuthUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.EntityNotFoundException;
import javax.persistence.PersistenceContext;
import javax.transaction.Transactional;
import java.util.Optional;
import java.util.UUID;

@Service
public class PrescriptionPharmacyService {
    @PersistenceContext
    private EntityManager entityManager;
    private PrescriptionPharmacyRepository prescriptionPharmacyRepository;

    @Autowired
    PrescriptionPharmacyService(PrescriptionPharmacyRepository prescriptionPharmacyRepository) {
        this.prescriptionPharmacyRepository = prescriptionPharmacyRepository;
    }

    public PrescriptionPharmacyDto createOrUpdatePrescription(PrescriptionPharmacyDto newPrescriptionPharmacyDto)
            throws EntityNotFoundException {
        var newPrescriptionPharmacy = newPrescriptionPharmacyDto.getPrescriptionPharmacy();
        if (newPrescriptionPharmacy.isPresent()) {
            UUID patientId = AuthUtils.getCognitoId();
            PrescriptionPharmacyID prescriptionPharmacyId = newPrescriptionPharmacy.get();
            PrescriptionID prescriptionId = new PrescriptionID(prescriptionPharmacyId.getPrescriptionName(), prescriptionPharmacyId.getPrescriptionCode(), patientId);
            Prescription prescription = entityManager.getReference(Prescription.class, prescriptionId);
            PrescriptionPharmacy prescriptionPharmacy = PrescriptionPharmacyConverter.dtoToPrescriptionPharmacy(newPrescriptionPharmacyDto, prescription);
            return PrescriptionPharmacyConverter.prescriptionToDto(prescriptionPharmacyRepository.save(prescriptionPharmacy));
        } else {
            throw new EntityNotFoundException();
        }
    }

    public Optional<PrescriptionPharmacyDto> findPrescriptionPharmacyById(PrescriptionPharmacyID prescriptionPharmacyId) {
        return prescriptionPharmacyRepository.findPrescriptionPharmacyById(prescriptionPharmacyId).map(prescriptionPharmacy -> PrescriptionPharmacyConverter.prescriptionToDto(prescriptionPharmacy));
    }

    public Optional<PrescriptionPharmacyDto> findPrescriptionPharmacyByPrescriptionId(PrescriptionID prescriptionID) {
        return prescriptionPharmacyRepository.findPrescriptionPharmacyByPrescriptionId(prescriptionID).map(prescriptionPharmacy -> PrescriptionPharmacyConverter.prescriptionToDto(prescriptionPharmacy));
    }

    @Transactional
    public void deletePrescriptionPharmacyById(PrescriptionPharmacyID prescriptionPharmacyId) {
        prescriptionPharmacyRepository.deletePrescriptionPharmacyById(prescriptionPharmacyId);
    }

}
