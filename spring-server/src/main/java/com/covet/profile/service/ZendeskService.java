package com.covet.profile.service;

import com.covet.profile.utils.SecretManagerUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

@Service
public class ZendeskService {
    public static final Logger log = LoggerFactory.getLogger(ZendeskService.class);
    private final SecretManagerUtils secretsManagerUtil;

    @Value("${spring.third-party.zendesk.secret-name-webhook-appointment}")
    private String webhookSecretName;

    private static final String SIGNING_SECRET_ALGORITHM = "HmacSHA256";

    public ZendeskService(SecretManagerUtils secretsManagerUtil) {
        this.secretsManagerUtil = secretsManagerUtil;
    }

    public boolean isValidSignature(String signature, String body, String timestamp) {
        try {
            Mac sha256_HMAC = Mac.getInstance(SIGNING_SECRET_ALGORITHM);
            var secrets = secretsManagerUtil.getSecrets(webhookSecretName);
            var signingSecret = secrets.get("X-SECRET-KEY");
            SecretKeySpec secret_key = new SecretKeySpec(signingSecret.getBytes(), SIGNING_SECRET_ALGORITHM);
            sha256_HMAC.init(secret_key);

            String hash = Base64.getEncoder().encodeToString(sha256_HMAC.doFinal((timestamp + body).getBytes()));

            log.info("signature: {}", signature);
            log.info("hash: {}", hash);
            return signature.equals(hash);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

}
