package com.covet.profile.controller;

import com.covet.profile.dto.*;
import com.covet.profile.dto.covet.appointment.AppointmentDto;
import com.covet.profile.dto.covet.claim.ClaimDto;
import com.covet.profile.dto.covet.claim.ImmutableClaimDto;
import com.covet.profile.dto.covet.health_check.HealthCareMethodDto;
import com.covet.profile.dto.covet.health_check.HealthCheckDto;
import com.covet.profile.dto.covet.provider.ImmutablePhysicianDto;
import com.covet.profile.dto.covet.provider.PhysicianDetailDto;
import com.covet.profile.dto.covet.provider.PhysicianDto;
import com.covet.profile.persistence.model.Appointment;
import com.covet.profile.persistence.model.PostVisit;
import com.covet.profile.persistence.model.Profile;
import com.covet.profile.responses.ResponseHandler;
import com.covet.profile.searchCriteria.ClaimSearchCriteria;
import com.covet.profile.service.ClaimService;
import com.covet.profile.service.PhysicianService;
import com.covet.profile.service.ReadFileService;
import com.covet.profile.utils.AuthUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.persistence.EntityNotFoundException;
import javax.validation.Valid;

import java.io.IOException;
import java.util.*;

// @PreAuthorize("hasRole('physician')")
@Validated
@RestController
@RequestMapping("/api")
public class PhysicianController {
    public static final Logger log = LoggerFactory.getLogger(PhysicianController.class);
    private final PhysicianService physicianService;
    private final ClaimService claimService;
    private final ReadFileService readFileService;

    @Autowired
    public PhysicianController(PhysicianService physicianService, ClaimService claimService, ReadFileService readFileService) {
        this.physicianService = physicianService;
        this.claimService = claimService;
        this.readFileService = readFileService;
    }

    @GetMapping("/physician")
    public PhysicianDetailDto getPhysician() {
        UUID cognitoId = AuthUtils.getCognitoId();
        return physicianService.findPhysicianById(cognitoId);
    }

    /// working on
    @GetMapping("/physician/appointments")
    public ResponseEntity<Set<AppointmentDto>> getAppointments() {
        UUID cognitoId = AuthUtils.getCognitoId();
        Set<AppointmentDto> foundAppointments = physicianService.getAppointmentsByPatient(cognitoId);
        return new ResponseEntity<>(foundAppointments, HttpStatus.OK);
    }

    @PostMapping(path = "/physician", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<PhysicianDto> createPhysician(
            @Valid @RequestBody final PhysicianDto newPhysicianDto) {
        UUID cognitoId = AuthUtils.getCognitoId();
        return physicianService.getPhysicianById(cognitoId).map(physician -> {
            log.error("PhysicianId {} already exist for create", cognitoId);
            return new ResponseEntity<PhysicianDto>(HttpStatus.CONFLICT);
        }).orElseGet(() -> {
            newPhysicianDto.getProfile().setCognitoId(cognitoId); // profile cognito id first
            var physicianDto = ImmutablePhysicianDto.copyOf(newPhysicianDto)
                    .withPhysicianId(cognitoId)
                    .withProfile(newPhysicianDto.getProfile());
            return new ResponseEntity<>(physicianService.createOrUpdatePhysician(physicianDto), HttpStatus.CREATED);
        });
    }

    @PutMapping(path = "/physician", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<PhysicianDto> updatePhysician(
            @Valid @RequestBody PhysicianDto newPhysicianDetails) {
        UUID cognitoId = AuthUtils.getCognitoId();
        return physicianService.getPhysicianById(cognitoId).map(physician -> {
            Profile newPhysicianDetailsProfile = newPhysicianDetails.getProfile();

            if (newPhysicianDetailsProfile.getCognitoId() != null
                    && !newPhysicianDetailsProfile.getCognitoId().equals(cognitoId)) {
                log.error("PhysicianId {} and profileId {} mismatch", cognitoId,
                        newPhysicianDetailsProfile.getCognitoId());
                return new ResponseEntity<PhysicianDto>(HttpStatus.BAD_REQUEST);
            }

            PhysicianDto updatedProfileDto = physicianService.createOrUpdatePhysician(
                    ImmutablePhysicianDto.copyOf(newPhysicianDetails)
                            .withPhysicianId(physician.getPhysicianId())
                            .withProfile(newPhysicianDetailsProfile)); // update cognito field in both profile and in
                                                                       // physician object

            return new ResponseEntity<>(updatedProfileDto, HttpStatus.OK);
        }).orElseGet(() -> {
            log.error("PhysicianId {} doesn't exist for update", cognitoId);
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        });
    }

    @DeleteMapping("/physician")
    public ResponseEntity<PhysicianDto> deletePhysician() {
        UUID cognitoId = AuthUtils.getCognitoId();
        return physicianService.getPhysicianById(cognitoId).map(physician -> {
            physicianService.deletePhysician(cognitoId);
            return new ResponseEntity<>(physician, HttpStatus.OK);
        }).orElseGet(() -> {
            log.error("PhysicianId {} doesn't exist for delete", cognitoId);
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        });
    }

    @PutMapping(path = "/physician/{id}/postVisit", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<PostVisit> updatePostVisit(@PathVariable("id") UUID id,
            @Valid @RequestBody final PostVisit updatePostVisit) {
        UUID cognitoId = AuthUtils.getCognitoId();
        return physicianService.getPhysicianById(cognitoId).map(physician -> {
            Appointment theAppointment = physicianService.getAppointmentByPhysician(id, cognitoId);
            try {
                theAppointment.setPostVisit(new ObjectMapper().writeValueAsString(updatePostVisit));
            } catch (JsonProcessingException e) {
                return new ResponseEntity<PostVisit>(HttpStatus.BAD_REQUEST);
            }
            physicianService.createOrUpdateAppointment(theAppointment);
            return new ResponseEntity<>(updatePostVisit, HttpStatus.OK);
        }).orElseGet(() -> {
            log.error("PhysicianId {} doesn't exist for update post visit", cognitoId);
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        });
    }

    // Use for testing only
    // @GetMapping("/physician/all-claims")
    // public ResponseEntity<Set<ClaimDto>> getAllClaims() {
    // var physicianId = AuthUtils.getCognitoId();
    // Set<ClaimDto> claims = claimService.getAllClaimsOld();
    // return new ResponseEntity<>(claims, HttpStatus.OK);
    // }

    @PostMapping(path = "/physician/claim", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ClaimDto> createClaim(
            @Valid @RequestBody final ClaimDto newClaimDto) {
        UUID cognitoId = AuthUtils.getCognitoId();

        UUID patientId = newClaimDto.getPatientId();
        return (ResponseEntity<ClaimDto>) physicianService.getPhysicianById(cognitoId)
                .map(physician -> {
                    try {
                        ClaimDto newClaim = physicianService.createOrUpdateClaim(
                                ImmutableClaimDto.copyOf(newClaimDto).withPhysicianId(cognitoId));
                        return new ResponseEntity<>(newClaim, HttpStatus.CREATED);
                    } catch (EntityNotFoundException e) {
                        log.error("PatientId {} doesn't exist for create claim", patientId);
                        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
                    }
                }).orElseGet(() -> {
                    log.error("PhysicianId {} doesn't exist for create claim", cognitoId);
                    return new ResponseEntity<>(HttpStatus.NOT_FOUND);
                });
    }

    @DeleteMapping("/physician/claim")
    public ResponseEntity<String> deleteClaim(@RequestParam String claimCode) {
        UUID physicianId = AuthUtils.getCognitoId();
        return physicianService.getPhysicianById(physicianId).map(physician -> {
            ClaimDto foundClaim = claimService.getClaim(claimCode, physicianId);
            claimService.deleteClaimById(claimCode);
            return new ResponseEntity<>(foundClaim.getClaimCode(), HttpStatus.OK);
        }).orElseGet(() -> {
            log.error("Claim with ID {} doesn't exist for delete", claimCode);
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        });
    }

    @Operation(summary = "Get All Claims - Search - Filter, If don't pass value for params page and pageSize then Get All, otherwise Search/Filter. Return: total number of claims + paging object + total Price of Claims ")
    @GetMapping("/physician/claims")
    public ResponseEntity<Object> getClaimsWithFilter(@Valid @ParameterObject ClaimSearchCriteria criteria,
            Optional<Integer> page, Optional<Integer> pageSize) {
        UUID physicianId = AuthUtils.getCognitoId();
        Page<ClaimDto> foundClaims;

        if (!(page.isPresent() && pageSize.isPresent())) {
            foundClaims = claimService.getAllClaims(physicianId);
        } else if ((page.isEmpty() && pageSize.isPresent()) || (page.isPresent() && pageSize.isEmpty())) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } else {
            foundClaims = claimService.getClaimsWithFilter(page.get(), pageSize.get(), physicianId, criteria);
        }

        int numOfClaims = 0;
        double totalPriceOfClaims = 0;
        var listClaims = claimService.getAllClaims(physicianId).getContent();

        if (Objects.nonNull(listClaims)) {
            numOfClaims = listClaims.size();
            totalPriceOfClaims = listClaims.stream().mapToDouble(x -> x.getTotalClaim()).sum();
        }

        return ResponseHandler.generateClaimResponse(foundClaims, numOfClaims, totalPriceOfClaims, HttpStatus.OK);
    }

    @PostMapping(path = "/physician/prior-author")
    public ResponseEntity<PriorAuthorDto> createPriorAuthor(
            @Valid @RequestBody final PriorAuthorDto newPriorAuthorDto) {
        PriorAuthorDto result = physicianService.createOrUpdatePriorAuthor(newPriorAuthorDto);
        return new ResponseEntity<>(result, HttpStatus.CREATED);
    }

    @PutMapping(path = "/physician/prior-author")
    public ResponseEntity<PriorAuthorDto> updatePriorAuthor(
            @Valid @RequestBody final PriorAuthorDto updatePriorAuthorDto) {
        PriorAuthorDto result = physicianService.createOrUpdatePriorAuthor(updatePriorAuthorDto);
        return new ResponseEntity<>(result, HttpStatus.CREATED);
    }

    @PostMapping(path = "/physician/health-care-method")
    public ResponseEntity<HealthCareMethodDto> createOrUpdateHealthCareMethod(
            @Valid @RequestBody final HealthCareMethodDto newHealthCareMethodDto) {
        HealthCareMethodDto result = physicianService.createOrUpdateHealthCareMethod(newHealthCareMethodDto);
        return new ResponseEntity<>(result, HttpStatus.CREATED);
    }

    @PostMapping(path = "/physician/visit-cost")
    public ResponseEntity<VisitCostDto> createOrUpdateVisitCost(
            @Valid @RequestBody final VisitCostDto newVisitCostDto) {
        VisitCostDto result = physicianService.createOrUpdateVisitCost(newVisitCostDto);
        return new ResponseEntity<>(result, HttpStatus.CREATED);
    }

    @PostMapping(path = "/physician/health-check")
    public ResponseEntity<HealthCheckDto> createOrUpdateHealthCheck(
            @Valid @RequestBody final HealthCheckDto newHealthCheckDto) {
        HealthCheckDto result = physicianService.createOrUpdateHealthCheck(newHealthCheckDto);
        return new ResponseEntity<>(result, HttpStatus.CREATED);
    }

    public void insertGroupProvider() throws IOException {
        readFileService.insertGroupProvider();
    }

    public void insertGroupPhysician() throws IOException {
        readFileService.insertGroupPhysician();
    }

    public void insertDrugData() {
        readFileService.insertDrugData();
    }

    public void insertProvider() throws IOException {
        readFileService.insertProvider();
    }

    public void getCoordinates() {
        physicianService.insertPhysicianAddress();
    }

    public void getCoordinatesTest() {
        physicianService.getCoordinateWithCallback();
    }
}