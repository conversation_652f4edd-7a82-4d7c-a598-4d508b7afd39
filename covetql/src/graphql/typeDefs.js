

const typeDefs = `

  type Query {
    _empty: String
    zendeskAuth(userId: String!): ZendeskAuth!
    fetchWidgetSessionURL: String!
  }

  type ZendeskAuth {
    token: String!
  }

  type Message {
    id: String
    content: String
    user_id: String
    conversation_id: String
    attached_image_url: String
    attached_audio_url: String
    document_id: String
    created_at: String
    updated_at: String
    is_autoresponse: Boolean
    deleted_by_user: Boolean
    scheduled_at: String
    image_name: String
    document_name: String
  }

  type Subscription {
    openConversation(conversationId: String): Message
  }
`;

export default typeDefs;

