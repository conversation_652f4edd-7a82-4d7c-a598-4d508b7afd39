package com.covet.profile.service;

import com.covet.profile.config.MpxConfig;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
@Service
public class MpxService {

    @Autowired
    private MpxConfig mpxConfig;

    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final Logger log = LoggerFactory.getLogger(MpxService.class);
    private String accessToken;
    private LocalDateTime accessTokenExpiry;
    private String refreshToken;
    private LocalDateTime refreshTokenExpiry;

    private static String acceptHeader = "Accept";
    

    public MpxService(ObjectMapper objectMapper) {
        this.httpClient = new OkHttpClient();
        this.objectMapper = objectMapper;
    }

    private synchronized void checkToken() throws IOException {
        log.info("checking toekn expirations: "+ accessTokenExpiry);
        log.info("checking refresh expirations: "+ refreshTokenExpiry);

        if (accessToken == null || LocalDateTime.now().isAfter(accessTokenExpiry)) {
            if (refreshToken == null || LocalDateTime.now().isAfter(refreshTokenExpiry)) {
                log.debug("access and refresh are expired. reauthnticating with MPX");
                authenticate();
            } else {
                log.debug("access is expired. using refresh token for MPX");
                refreshAccessToken();
            }
        }
    }


    private void authenticate() throws IOException {
        log.info("GETTING NEW TOKENS FOR MPX!!!!");
        String authUrl = "https://www.mpxpp.com/api/v3/covethealth/user-sessions";
        MediaType mediaType = MediaType.parse("application/json");
        String jsonBody = String.format("{\"username\": \"%s\", \"password\": \"%s\", \"one_time_code\": \"\", \"is_impersonation\": false, \"is_sso\": false}", mpxConfig.getUsername(), mpxConfig.getPassword());
        RequestBody body = RequestBody.create(jsonBody, mediaType);
        Request request = new Request.Builder()
                .url(authUrl)
                .post(body)
                .addHeader(acceptHeader, "*/*")
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to fetch token. Code: " + response.code());
            }

            JsonNode jsonResponse = objectMapper.readTree(response.body().string());
            accessToken = jsonResponse.get("token").asText();
            refreshToken = jsonResponse.get("refresh_token").asText();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssZ");
            accessTokenExpiry = OffsetDateTime.parse(jsonResponse.get("datetime_token_expires").asText(), formatter).toLocalDateTime();
            refreshTokenExpiry = OffsetDateTime.parse(jsonResponse.get("datetime_refresh_token_expires").asText(), formatter).toLocalDateTime();
        }
    }

    private void refreshAccessToken() throws IOException {
        log.debug("refreshing mpx access token!!!");
        String refreshUrl = "https://www.mpxpp.com/api/v3/covethealth/user-sessions/current";
        String jsonBody = String.format("{\"refresh_token\": \"%s\"}", refreshToken);
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(jsonBody, mediaType);

        Request request = new Request.Builder()
                .url(refreshUrl)
                .patch(body)
                .addHeader(acceptHeader, "*/*")
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to refresh token. Code: " + response.code());
            }

            JsonNode jsonResponse = objectMapper.readTree(response.body().string());

            // Update the tokens and their expiry times
            accessToken = jsonResponse.get("token").asText();
            refreshToken = jsonResponse.get("refresh_token").asText();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssZ");
            accessTokenExpiry = OffsetDateTime.parse(jsonResponse.get("datetime_token_expires").asText(), formatter).toLocalDateTime();
            refreshTokenExpiry = OffsetDateTime.parse(jsonResponse.get("datetime_refresh_token_expires").asText(), formatter).toLocalDateTime();
        }
    }


     public String getIDCardImageID(String subscriberId) throws IOException {
        checkToken();

        String url = String.format("https://www.mpxpp.com/api/v3/covethealth/documents?embed=metadata&paging[page_size]=200&filter[field]=_.metadata.SubscriberID&filter[term]=%s&search=_.metadata.Description|ID+Card+Image", subscriberId);

        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + accessToken)
                .addHeader(acceptHeader, "application/vnd.hale+json")
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to fetch document ID. Code: " + response.code());
            }

            JsonNode jsonResponse = objectMapper.readTree(response.body().string());
            JsonNode documentsNode = jsonResponse.path("_embedded").path("documents");

            if (!documentsNode.isEmpty() && documentsNode.isArray()) {
                JsonNode firstDocumentNode = documentsNode.get(0);
                return Optional.ofNullable(firstDocumentNode)
                        .map(node -> node.get("id"))
                        .map(JsonNode::asText)
                        .orElseThrow(() -> new RuntimeException("Document ID not found"));
            } else {
                throw new RuntimeException("No documents found");
            }
        }
    }

    public String getCOBID(String subscriberId) throws IOException {
        checkToken();

        String url = String.format("https://www.mpxpp.com/api/v3/covethealth/document-groups");

        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + accessToken)
                .addHeader("Accept", "application/vnd.hale+json")
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to fetch document ID. Code: " + response.code());
            }

            JsonNode jsonResponse = objectMapper.readTree(response.body().string());
            JsonNode documentsNode = jsonResponse.path("_embedded").path("documents");

            System.out.println("ACCESS TOKEN");
            System.out.println(accessToken);
            System.out.println("COB Response");
            System.out.println(jsonResponse);
            System.out.println(documentsNode);
            if (!documentsNode.isEmpty() && documentsNode.isArray()) {
                JsonNode firstDocumentNode = documentsNode.get(0);
                return Optional.ofNullable(firstDocumentNode)
                        .map(node -> node.get("id"))
                        .map(JsonNode::asText)
                        .orElseThrow(() -> new RuntimeException("Document ID not found"));
            } else {
                throw new RuntimeException("No documents found");
            }
        }
    }

    public byte[] getPdfForSubscriber(String documentId) throws IOException {
        checkToken();

        String url = String.format("https://www.mpxpp.com/api/v3/covethealth/documents/%s?embed=*", documentId);

        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + accessToken)
                .addHeader(acceptHeader,"application/pdf")
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to fetch PDF document. Code: " + response.code());
            }

            return response.body().bytes();
        }
    }

    
}
