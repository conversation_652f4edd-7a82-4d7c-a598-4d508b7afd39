package com.covet.profile.converter;

import java.util.List;
import java.util.UUID;

import com.covet.profile.dto.SchedulerDto;
import com.covet.profile.persistence.model.Scheduler;

import com.covet.profile.systemEnum.EDayOfWeek;
import com.covet.profile.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;
import org.springframework.stereotype.Component;

@Data
@Component
public class SchedulerConverter {
    public Scheduler toScheduler(SchedulerDto schedulerDto) {
        UUID schedulerId = schedulerDto.getSchedulerId();
        var startDate = schedulerDto.getStartDate();
        var endDate = schedulerDto.getEndDate();
        var dayOfWeeks = JsonUtils.toJson(schedulerDto.getDayOfWeeks());

        return new Scheduler(
                schedulerId,
                startDate,
                endDate,
                dayOfWeeks,
                null);
    }

    public SchedulerDto toSchedulerDto(Scheduler scheduler) {
        UUID schedulerId = scheduler.getSchedulerId();
        var startDate = scheduler.getStartDate();
        var endDate = scheduler.getEndDate();
        TypeReference<List<EDayOfWeek>> typeReference = new TypeReference<>() {
        };
        var convertDayOfWeeks = JsonUtils.toObj(scheduler.getDayOfWeeks(), typeReference);

        return new SchedulerDto(
                schedulerId,
                startDate,
                endDate,
                convertDayOfWeeks);
    }
}
