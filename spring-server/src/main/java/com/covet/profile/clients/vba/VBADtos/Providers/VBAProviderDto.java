package com.covet.profile.clients.vba.VBADtos.Providers;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

@JsonSerialize(as = ImmutableVBAProviderDto.class)
@JsonDeserialize(as = ImmutableVBAProviderDto.class, builder = ImmutableVBAProviderDto.Builder.class)
@Value.Immutable
public interface VBAProviderDto {
    @Value.Parameter
    @JsonProperty
    String getProviderId();
    @Value.Parameter
    @JsonProperty()
    String getProviderName();
}
