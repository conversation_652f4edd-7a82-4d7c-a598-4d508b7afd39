#  Template maven-build

#  This template allows you to test and build your Java project with <PERSON><PERSON>.
#  The workflow allows running tests, code checkstyle and security scans on the default branch.

# Prerequisites: pom.xml and appropriate project structure should exist in the repository.

image: maven:3.8.5-openjdk-17-slim

definitions:
  services:
    postgres:
      image: postgres
      environment:
        POSTGRES_HOST_AUTH_METHOD: trust
      variables:
        POSTGRES_DB: 'covet'
        POSTGRES_USER: 'admin'
        POSTGRES_PASSWORD: 'password'
  steps:
    - step: &build-and-test
        name: Build and test
        caches:
          - maven
        services:
          - postgres
        script:
          - mvn --batch-mode verify
        after-script:
          # Collect checkstyle results, if any, and convert to Bitbucket Code Insights.
          - pipe: atlassian/checkstyle-report:0.3.0
        artifacts:
          - 'target/**'
    - step: &security-scan
        name: Security Scan
        script:
          # Run a security scan for sensitive data.
          # See more security tools at https://bitbucket.org/product/features/pipelines/integrations?&category=security
          - pipe: atlassian/git-secrets-scan:0.5.1
  scripts:
    - script: &deploy
        - cd target
        - pipe: atlassian/aws-elasticbeanstalk-deploy:1.0.2
          variables:
            AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
            AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
            AWS_DEFAULT_REGION: 'us-east-1'
            APPLICATION_NAME: 'covet-backend'
            ENVIRONMENT_NAME: $AWS_BEAN_STALK_ENV
            ZIP_FILE: 'profile-0.0.1-SNAPSHOT.jar'
            VERSION_LABEL: 'deploy-$BITBUCKET_BUILD_NUMBER-multiple'
            S3_BUCKET: 'elasticbeanstalk-us-east-1-123903216279'
            WAIT: 'true'
pipelines:
  default:
    - parallel:
        - step: *build-and-test
        - step: *security-scan
  branches:
    master:
      - parallel:
          - step: *build-and-test
          - step: *security-scan
      - step:
          name: Deploy Dev
          deployment: Dev
          script: *deploy
      - step:
          name: Deploy Stg
          deployment: Staging
          trigger: manual
          script: *deploy
      - step:
          name: Deploy Prod
          deployment: Production
          trigger: manual
          script: *deploy