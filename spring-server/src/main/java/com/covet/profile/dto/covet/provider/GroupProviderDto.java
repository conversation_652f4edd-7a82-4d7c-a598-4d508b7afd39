package com.covet.profile.dto.covet.provider;

import com.covet.profile.persistence.model.GroupRating;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;
import java.util.Optional;
import java.util.UUID;

@JsonSerialize(as = ImmutableGroupProviderDto.class)
@JsonDeserialize(as = ImmutableGroupProviderDto.class, builder = ImmutableGroupProviderDto.Builder.class)
@Value.Immutable
public interface GroupProviderDto {
    @Value.Parameter
    @JsonProperty
    Optional<UUID> getGroupId();

    @Value.Parameter
    @JsonProperty
    String getGroupName();

    @Value.Parameter
    @JsonProperty
    String getGroupDescription();

    @Value.Parameter
    @JsonProperty
    String getGroupAddress();

    @Value.Parameter
    @JsonProperty
    String getGroupEmail();

    @Value.Parameter
    @JsonProperty
    String getGroupPhoneNumber();

    @Value.Parameter
    @JsonProperty
    boolean getIsVirtualOnly();

    @Value.Parameter
    @JsonProperty
    String getGroupSpecialty();

    @Value.Parameter
    @JsonProperty
    Optional<String> getNpi();

    @Value.Parameter
    @JsonProperty
    Optional<String> getCity();

    @Value.Parameter
    @JsonProperty
    Optional<String> getState();

    @Value.Parameter
    @JsonProperty
    Optional<Double> getValue();

    @Value.Parameter
    @JsonProperty
    Optional<Double> getQuality();

    @Value.Parameter
    @JsonProperty
    Optional<Double> getEfficiency();

    @Value.Parameter
    @JsonProperty
    Optional<String> getSource();

    @Value.Parameter
    @JsonProperty
    Double getDistance();

    @Value.Parameter
    @JsonProperty
    Optional<GroupRating> getGroupRating();

    @Value.Parameter
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    Optional<Object> npiInfo();

    @Value.Parameter
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    Optional<Boolean> isFavorite();
}
