
package com.covet.profile.dto.CapRxDto.drug.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.List;

import org.immutables.value.Value;

@JsonSerialize(as = ImmutableAlternativeDrugsDto.class)
@JsonDeserialize(as = ImmutableAlternativeDrugsDto.class, builder = ImmutableAlternativeDrugsDto.Builder.class)
@Value.Immutable
public interface AlternativeDrugsDto {

    @Value.Parameter
    @JsonProperty
    List<DrugItemDto> getAlternatives();

    @Value.Parameter
    @JsonProperty
    DrugDto getDrug();

}
