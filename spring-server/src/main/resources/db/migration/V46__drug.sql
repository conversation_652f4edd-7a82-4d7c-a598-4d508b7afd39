DO
$$
BEGIN
    CREATE TABLE IF NOT EXISTS drug (
          id uuid PRIMARY KEY,
          ndc varchar(16),
          label_name varchar(128),
          brand_name varchar(128),
          generic_name varchar(128),
          tier varchar(16),
          tier_original varchar(128),
          prior_authorization varchar(128),
          quantity_limit varchar(128),
          quantity_limit_over_time varchar(128),
          step_therapy varchar(128),
          step_therapy_number varchar(16),
          minimum_age_limit varchar(16),
          maximum_age_limit varchar(16),
          gender varchar(128),
          specialty varchar(128),
          preferred_specialty varchar(128),
          low_cost_generic varchar(128),
          high_cost_generic varchar(128),
          high_cost_brand varchar(128),
          affordable_care_act varchar(128),
          preventive varchar(128),
          package_size varchar(16),
          package_size_unit_of_measure varchar(128),
          package_quantity varchar(16),
          unit_dose_code varchar(128),
          package_description_code varchar(64),
          maintenance varchar(128),
          compound_kit varchar(128),
          rx_cap_plus varchar(128),
          opioid varchar(128),
          strength_per_unit varchar(128),
          mme_conversion_factor varchar(128),
          medical_drug varchar(128),
          rx4less varchar(128),
          drug_application_type varchar(128),
          cms_labeler_code varchar(128),
          manufacturer_name varchar(128),
          add_date date,
          effective_date date,
          formulary_name varchar(128),
          updated_at timestamp,
          created_at timestamp
    );
    -- create index later
END
$$;