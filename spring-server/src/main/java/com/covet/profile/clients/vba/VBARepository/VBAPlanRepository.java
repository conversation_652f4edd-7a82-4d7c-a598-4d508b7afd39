package com.covet.profile.clients.vba.VBARepository;

import com.covet.profile.clients.vba.VBAModels.EnrollmentSubscriber;
import com.covet.profile.clients.vba.VBAModels.Plans.VBABenefit;
import com.covet.profile.clients.vba.VBAModels.Plans.VBAPlaceCoInsuranceItem;
import com.covet.profile.clients.vba.VBAModels.Plans.VBAPlaceCoPayItem;
import com.covet.profile.clients.vba.VBAModels.Plans.VBAPlanDisplay;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import com.google.common.util.concurrent.RateLimiter;
import javax.sql.DataSource;

import java.math.BigDecimal;
import java.net.URI;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Repository
public class VBAPlanRepository {
    private final JdbcTemplate jdbcTemplate;

    private final RestTemplate vbaRestTemplate;
    private final String domain;
    private ObjectMapper objectMapper;
    private final RateLimiter externalServiceRateLimiter;
    private static final Logger log = LoggerFactory.getLogger(VBAPlanRepository.class);

    public VBAPlanRepository(@Qualifier("vbaDataSource") DataSource dataSource, @Qualifier("vbaRestTemplate") RestTemplate vbaRestTemplate, @Value("${spring.third-party.vba.domain}") String domain, ObjectMapper objectMapper, RateLimiter externalServiceRateLimiter) {
        this.jdbcTemplate = new JdbcTemplate(dataSource);
        this.vbaRestTemplate = vbaRestTemplate;
        this.domain = domain;
        this.objectMapper = objectMapper;
        this.externalServiceRateLimiter = externalServiceRateLimiter;
    }

    public List<VBABenefit> getBenefitListByPlanId(String planId) {
        String query = "SELECT Plan_ID, Benefit_Code FROM PlanBenefits WHERE Plan_ID = ?";
        List<VBABenefit> benefits = jdbcTemplate.query(query,
            new BeanPropertyRowMapper<>(VBABenefit.class), planId);
        return benefits;
    }

    public List<VBAPlanDisplay> getPlanDisplayList(String planId) {
        String query = "SELECT Plan_ID, Display_Type, Display FROM PlanDisplay WHERE Plan_ID = ?";
        List<VBAPlanDisplay> displays = jdbcTemplate.query(query,
            new BeanPropertyRowMapper<>(VBAPlanDisplay.class), planId);
        return displays;
    }

    public VBAPlaceCoInsuranceItem getPlaceCoInsurance(String planId, String benefitCode, String placeCode) {
        String query = "SELECT Plan_ID, Benefit_Code, Place_Code, 100 - COALESCE(Co_Insurance, 0) AS coInsurance, 100 - COALESCE(Co_Insurance_Net, 0) AS coInsuranceNet FROM PlanBenefitsPlaceCoIns WHERE Plan_ID = ? AND Benefit_Code = ? AND Place_Code = ?";
        List<VBAPlaceCoInsuranceItem> items = jdbcTemplate.query(query,
            new BeanPropertyRowMapper<>(VBAPlaceCoInsuranceItem.class), planId, benefitCode, placeCode);
    
        return items.isEmpty() ? null : items.get(0);
    }

    public List<VBAPlaceCoPayItem> getPlaceCopayList(String planId, String benefitCode) {
        // Use the field name from your class 'planID' for logging consistency if desired,
        // but the input parameter is 'planId'. Stick with parameter name for clarity here.
        log.info("Entering getPlaceCopayList for planId: '{}', benefitCode: '{}'", planId, benefitCode);

        // SQL column names (Plan_ID, Copay_Amt, etc.) are mapped by BeanPropertyRowMapper
        // to the fields (planID, copayAmount, etc.) via setters (generated by Lombok)
        String query = "SELECT Plan_ID, Benefit_Code, Place_Code, Copay_Amt AS copayAmount, Copay_Amt_Net AS copayAmountNet  FROM PlanBenefitsPlaceCoPay WHERE Plan_ID = ? AND Benefit_Code = ?";
        List<VBAPlaceCoPayItem> coPays = null;

        try {
            coPays = jdbcTemplate.query(query,
                    new BeanPropertyRowMapper<>(VBAPlaceCoPayItem.class),
                    planId, benefitCode);

            log.info("Query for planId '{}', benefitCode '{}' returned {} raw copay items.", planId, benefitCode, coPays.size());

            // Check for null planId in fetched results - USE THE CORRECT GETTER (getPlanID)
            for (int i = 0; i < coPays.size(); i++) {
                VBAPlaceCoPayItem item = coPays.get(i);
                // *** Check the correct field via its Lombok-generated getter ***
                if (item.getPlanId() == null) {
                    // Log relevant details using correct getters
                    log.warn("Fetched VBAPlaceCoPayItem at index {} has NULL planID! benefitCode: '{}', placeCode: '{}', copayAmount: {}",
                            i, item.getBenefitCode(), item.getPlaceCode(), item.getCopayAmount()); // Use getCopayAmount()

                    // Logging the item directly requires @ToString on VBAPlaceCoPayItem for useful output
                    log.warn("Full item with null planID [Index {}]: {}", i, item);
                }
                // Optional TRACE log - also benefits from @ToString
                // log.trace("Fetched item [{}]: {}", i, item);
            }


            if (coPays.isEmpty()) {
                log.info("No specific copay items found for planId '{}', benefitCode '{}'. Creating and adding default item.", planId, benefitCode);

                // Call Lombok's @AllArgsConstructor. Argument order MUST match field declaration order:
                // planID, benefitCode, placeCode, copayAmount, copayAmountNet
                VBAPlaceCoPayItem defaultCopay = new VBAPlaceCoPayItem(
                        planId,          // planID
                        benefitCode,     // benefitCode
                        "00",            // placeCode
                        BigDecimal.ZERO, // copayAmount
                        BigDecimal.ZERO  // copayAmountNet
                );

                // Check default item using correct getter
                if (defaultCopay.getPlanId() == null) {
                    // This would be highly unusual if planId passed in was not null
                    log.error("CRITICAL: Default VBAPlaceCoPayItem created with NULL planID for input planId '{}', benefitCode '{}'. Check constructor logic/inputs!", planId, benefitCode);
                }

                // Initialize as ArrayList to ensure it's modifiable
                coPays = new ArrayList<>();
                coPays.add(defaultCopay);
            }

        } catch (DataAccessException e) { // Catch Spring's generic data access exception
            log.error("Database error querying PlanBenefitsPlaceCoPay for planId '{}', benefitCode: '{}'", planId, benefitCode, e);
            return Collections.emptyList(); // Return empty list on error
        } catch (Exception e) { // Catch any other unexpected exceptions
             log.error("Unexpected error in getPlaceCopayList for planId '{}', benefitCode: '{}'", planId, benefitCode, e);
            return Collections.emptyList();
        }

        log.info("Exiting getPlaceCopayList for planId: '{}', benefitCode: '{}'. Returning {} items.", planId, benefitCode, coPays.size());
        return coPays;
    }
}
