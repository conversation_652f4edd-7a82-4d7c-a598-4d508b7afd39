package com.covet.profile.service;

import org.springframework.stereotype.Service;
import com.covet.profile.clients.vba.VBADtos.Plans.ImmutablePlanBenefitDto;
import com.covet.profile.clients.vba.VBADtos.Plans.PlanBenefitDto;
import com.covet.profile.persistence.model.Benefit;
import com.covet.profile.clients.vba.VBADtos.Plans.VBAPlaceCoPayItemDto;
import com.covet.profile.clients.vba.VBAService.VBAPlanBenefitService;

import javax.annotation.PostConstruct;

import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class PlanBenefitService {

    private final VBAPlanBenefitService vbaPlanBenefitService;

    public PlanBenefitService(VBAPlanBenefitService vbaPlanBenefitService) {
        this.vbaPlanBenefitService = vbaPlanBenefitService;
    }

    private final Map<String, Benefit> benefitMapping = new HashMap<>();

    // Regex to find titles ending in " (Tier X)" and capture the base title and tier number
    private static final Pattern TIERED_TITLE_PATTERN = Pattern.compile("^(.*) \\(Tier ([12])\\)$");

    @PostConstruct
    public void loadBenefitMapping() {
        // Load the mapping from source
        // benefitMapping.put("MEDPCPOV", new Benefit("MEDPCPOV", "Primary Care", ""));
        // benefitMapping.put("MEDSPECOV", new Benefit("MEDSPECOV", "Specialist Visit", ""));
        // benefitMapping.put("MEDWA", new Benefit("MEDWA", "Preventive Care", ""));
        // benefitMapping.put("MEDUCC", new Benefit("MEDUCC", "Urgent Care", ""));
        // benefitMapping.put("MEDER", new Benefit("MEDER", "Emergency Care", ""));
        // benefitMapping.put("MEDMOP", new Benefit("MEDMOP", "Imaging", "Notes 2"));
        // benefitMapping.put("MEDDXL", new Benefit("MEDDXL", "Labs", "Notes 1"));
        // benefitMapping.put("1140", new Benefit("1140", "Prescriptions", "Notes 2"));
        // benefitMapping.put("MEDPT", new Benefit("MEDPT", "Rehab - PT", "Notes 1"));
        // benefitMapping.put("MEDOCC", new Benefit("MEDOCC", "Rehab - OT", "Notes 2"));
        // benefitMapping.put("1117", new Benefit("1117", "Mental Health Outpatient", "Notes 1"));
        // benefitMapping.put("1171", new Benefit("1171", "Mental Health Inpatient", "Notes 2"));
        // benefitMapping.put("MEDHOSI", new Benefit("MEDHOSI", "Hospital Care - Inpatient Facility Fee", "Notes 1"));
        // benefitMapping.put("MEDIPP", new Benefit("MEDIPP", "Hospital Care - Inpatient Physician Fee", "Notes 2"));
        // benefitMapping.put("MODOSP", new Benefit("MODOSP", "Hospital Care - Outpatient Facility Fee", "Notes 1"));
        // benefitMapping.put("MEDOSPP", new Benefit("MEDOSPP", "Hospital Care - Outpatient Physician Fee", "Notes 2"));
        // benefitMapping.put("MEDPCPOV", new Benefit("MEDPCPOV", "Primary Care", ""));
        // benefitMapping.put("MEDPHYOV", new Benefit("MEDPHYOV", "Specialist Visit", ""));
        benefitMapping.put("MEDWA", new Benefit("MEDWA", "Preventive Care", ""));
        benefitMapping.put("MEDUCC", new Benefit("MEDUCC", "Urgent Care (Tier 1)", ""));
        benefitMapping.put("MEDUCC2", new Benefit("MEDUCC2", "Urgent Care (Tier 2)", ""));
        // benefitMapping.put("MEDER", new Benefit("MEDER", "Emergency Care", ""));
        // benefitMapping.put("MEDMCP", new Benefit("MEDMCP", "Imaging", "part of Imaging & labs section"));
        // benefitMapping.put("MEDDXL", new Benefit("MEDDXL", "Labs", "part of Imaging & labs section"));
        benefitMapping.put("MEDDRUG", new Benefit("MEDDRUG", "Prescriptions", ""));
        benefitMapping.put("MEDPT", new Benefit("MEDPT", "Rehab - PT", ""));
        benefitMapping.put("MEDOCC", new Benefit("MEDOCC", "Rehab - OT", ""));
        benefitMapping.put("MEDHOSI", new Benefit("MEDHOSI", "Hospital Care - Inpatient Facility Fee", ""));
        benefitMapping.put("MEDIPP", new Benefit("MEDIPP", "Hospital Care - Inpatient Physician Fee", ""));
        benefitMapping.put("MEDOSP", new Benefit("MEDOSP", "Hospital Care - Outpatient Facility Fee", ""));
        benefitMapping.put("MEDOSPP", new Benefit("MEDOSPP", "Hospital Care - Outpatient Physician Fee", ""));
        benefitMapping.put("1100PCP", new Benefit("1100PCP", "Primary Care (Tier 1)", ""));
        benefitMapping.put("1100PCP2", new Benefit("1100PCP2", "Primary Care (Tier 2)", ""));
        benefitMapping.put("1100", new Benefit("1100", "Specialist Visit (Tier 1)", ""));
        benefitMapping.put("11002", new Benefit("11002", "Specialist Visit (Tier 2)", ""));
        benefitMapping.put("ER", new Benefit("ER", "Emergency Room (Tier 1)", ""));
        benefitMapping.put("ER2", new Benefit("ER2", "Emergency Room (Tier 2)", ""));
    }

    public String getBenefitTitle(String benefitCode) {
        Benefit benefit = benefitMapping.get(benefitCode);
        return (benefit != null) ? benefit.getTitle() : "";
    }

    public String getBenefitNote(String benefitCode) {
        Benefit benefit = benefitMapping.get(benefitCode);
        return (benefit != null) ? benefit.getNote() : "";
    }

    public List<PlanBenefitDto> getPlanBenefitList(String planId) {
        List<VBAPlaceCoPayItemDto> benefitDataList = vbaPlanBenefitService.getPlanBenefitList(planId);

        if (benefitDataList == null || benefitDataList.isEmpty()) {
            return Collections.emptyList(); // Return empty list if no data
        }

        // 1. Initial Transformation: Create DTOs with original titles from static mapping
        List<PlanBenefitDto> initialDtos = new ArrayList<>();
        Map<String, VBAPlaceCoPayItemDto> uniqueBenefitData = benefitDataList.stream()
                .collect(Collectors.toMap(
                        VBAPlaceCoPayItemDto::getBenefitCode,
                        Function.identity(),
                        (existing, replacement) -> existing // Keep first in case of duplicates
                ));

        for (Map.Entry<String, VBAPlaceCoPayItemDto> entry : uniqueBenefitData.entrySet()) {
             String benefitCode = entry.getKey();
             VBAPlaceCoPayItemDto benefit = entry.getValue();
             String currentPlanId = planId; // Or benefit.getPlanID() if needed

             String originalTitle = getBenefitTitle(benefitCode); // Title from static map
             String benefitNote = getBenefitNote(benefitCode);     // Note from static map

             if (!originalTitle.isEmpty()) { // Only add if we have a title mapping
                 PlanBenefitDto initialDto = ImmutablePlanBenefitDto.of(
                         currentPlanId,
                         benefitCode,
                         benefit,
                         originalTitle, // Use original title for now
                         benefitNote);
                 initialDtos.add(initialDto);
             }
        }

        // 2. Analyze Titles *within this specific plan's results* for Tiers
        Map<String, Set<String>> baseTitleToTiers = new HashMap<>();
        for (PlanBenefitDto dto : initialDtos) {
            Matcher matcher = TIERED_TITLE_PATTERN.matcher(dto.getBenefitCodeTitle());
            if (matcher.matches()) {
                String baseTitle = matcher.group(1);
                String tier = matcher.group(2);
                baseTitleToTiers.computeIfAbsent(baseTitle, k -> new HashSet<>()).add(tier);
            }
        }

        // 3. Create Final DTO List with Titles Cleaned *based on plan context*
        List<PlanBenefitDto> finalDtos = new ArrayList<>();
        for (PlanBenefitDto dto : initialDtos) {
            String originalTitle = dto.getBenefitCodeTitle();
            String finalTitle = originalTitle;

            Matcher matcher = TIERED_TITLE_PATTERN.matcher(originalTitle);
            if (matcher.matches()) {
                String baseTitle = matcher.group(1);
                // Clean title ONLY IF this base title had exactly one tier IN THIS PLAN'S RESULTS
                if (baseTitleToTiers.containsKey(baseTitle) && baseTitleToTiers.get(baseTitle).size() == 1) {
                    finalTitle = baseTitle;
                }
            }

            // Create the final DTO (re-create if title changed, or use original if not)
            if (!finalTitle.equals(originalTitle)) {
                 // Use copyOf().withBenefitTitle() for immutable objects
                finalDtos.add(ImmutablePlanBenefitDto.copyOf(dto).withBenefitCodeTitle(finalTitle));
            } else {
                finalDtos.add(dto); // Add the original DTO if title didn't change
            }
        }

        // 4. Sort the final list by the (potentially cleaned) benefit title
        // This ensures items with the same effective title are adjacent.
        finalDtos.sort(Comparator.comparing(PlanBenefitDto::getBenefitCodeTitle));

        // 5. Return the sorted list
        return finalDtos;
    }
}