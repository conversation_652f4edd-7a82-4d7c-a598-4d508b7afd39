package com.covet.profile.persistence.repository;

import com.covet.profile.persistence.model.GroupProviderAddress;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.UUID;

public interface GroupProviderAddressRepository extends JpaRepository<GroupProviderAddress, UUID> {

    @Query("""
            select gpa from GroupProviderAddress gpa
            where gpa.address != ''
            """)
    List<GroupProviderAddress> getGroupProviderNonNullAddress(Pageable pageable);
}
