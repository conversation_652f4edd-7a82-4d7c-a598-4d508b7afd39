package com.covet.profile.persistence.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;
import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "group_rating")
@FieldNameConstants
public class GroupRating {
    @Id
    @Column(name = "group_id")
    private UUID groupId;

    @Column(name = "average_rating")
    private double averageRating;

    @Column(name = "total_reviews")
    private long totalReviews;

    @JsonIgnore
    @OneToOne
    @MapsId
    @JoinColumn(name = "group_id")
    private GroupProvider groupProvider;
}
