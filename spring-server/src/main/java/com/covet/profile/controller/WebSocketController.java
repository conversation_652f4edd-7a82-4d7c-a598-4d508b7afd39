package com.covet.profile.controller;

import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;
import com.covet.profile.service.NotificationService;

@Controller
public class WebSocketController {

    private final NotificationService notificationService;

    @Autowired
    WebSocketController(NotificationService notificationService) {
        this.notificationService = notificationService;
    }

    @MessageMapping("/notification/{notificationId}")
    @SendTo("/topic/notification")
    public void pushNotification(@DestinationVariable UUID notificationId) {
        notificationService.updateNotification(notificationId);
    }

}