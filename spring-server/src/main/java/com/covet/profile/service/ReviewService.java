package com.covet.profile.service;

import com.covet.profile.converter.ReviewConverter;
import com.covet.profile.dto.covet.provider.review.CustomReviewDto;
import com.covet.profile.dto.covet.provider.review.ReviewDetailDto;
import com.covet.profile.dto.covet.provider.review.ReviewDto;
import com.covet.profile.persistence.model.GroupProvider;
import com.covet.profile.persistence.model.Physician;
import com.covet.profile.persistence.model.Profile;
import com.covet.profile.persistence.model.Review;
import com.covet.profile.persistence.repository.*;
import com.covet.profile.responses.ResponseHandler;
import com.covet.profile.searchCriteria.GroupReviewSearchCriteria;
import com.covet.profile.searchCriteria.PhysicianReviewSearchCriteria;
import com.covet.profile.persistence.specification.GroupReviewSpec;
import com.covet.profile.persistence.specification.PhysicianReviewSpec;
import com.covet.profile.utils.ImageUtils;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityNotFoundException;
import javax.validation.Valid;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
@Service
public class ReviewService  {

    private final ReviewRepository reviewRepository;
    private final ProfileRepository profileRepository;
    private final PhysicianRepository physicianRepository;
    private final PictureRepository pictureRepository;

    private final GroupProviderRepository groupProviderRepository;

    private final S3Service s3Service;

    @Autowired
    ReviewService(ReviewRepository reviewRepository, ProfileRepository profileRepository,
            PhysicianRepository physicianRepository, GroupProviderRepository groupProviderRepository,
            PictureRepository pictureRepository,
            S3Service s3Service) {
        this.reviewRepository = reviewRepository;
        this.profileRepository = profileRepository;
        this.physicianRepository = physicianRepository;
        this.groupProviderRepository = groupProviderRepository;
        this.pictureRepository = pictureRepository;
        this.s3Service = s3Service;
    }

    /**
     * @param uuid
     * @return avatarUrl from DB image if not expired; otherwise, return S3 presignedUrl
     */
    private URL getAvatarUrl(@NonNull UUID uuid) {
        URL avatarUrl = ImageUtils.getImageUrl(s3Service.getExpiredDays(), pictureRepository.findById(uuid));
        if (avatarUrl == null) {
            avatarUrl =  s3Service.getPresignedURL(uuid.toString());
        }
        return avatarUrl;
    }

    private List<CustomReviewDto> convertReviewData(Page<Review> reviews) {
        List<CompletableFuture<URL>> futures = reviews.map(review ->
             CompletableFuture.supplyAsync(() -> getAvatarUrl(review.getPatientId()))
        ).toList();
        List<CustomReviewDto> convertedReviews = new ArrayList<>();
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        try {
            allFutures.get();
            List<Review> reviewList = reviews.getContent();
            for (int i = 0; i < reviewList.size(); i++) {
                URL url = futures.get(i).get();
                convertedReviews.add(ResponseHandler.generateReviewItem(reviewList.get(i), url));
            }
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
            Thread.currentThread().interrupt();
        }
        return convertedReviews;
    }

    public Page<CustomReviewDto> getAllReviewByPhysicianId(UUID physicianId) {
        // get UUID
        Page<Review> reviews = reviewRepository.findAllByPhysicianId(physicianId, Pageable.unpaged());
        List<CustomReviewDto> customReviewDtoList = convertReviewData(reviews);
        return new PageImpl<>(customReviewDtoList, reviews.getPageable(),
                reviews.getTotalElements());
    }

    public Page<CustomReviewDto> getAllReviewByGroupId(UUID groupID) {
        Page<Review> reviews = reviewRepository.findAllByGroupId(groupID, Pageable.unpaged());
        List<CustomReviewDto> customReviewDtoList = convertReviewData(reviews);
        return new PageImpl<>(customReviewDtoList, reviews.getPageable(),
                reviews.getTotalElements());
    }

    public Page<CustomReviewDto> getReviewsWithFilter(PhysicianReviewSearchCriteria criteria, int page,
            int pageSize) {
        Specification<Review> conditions;
        var keyword = criteria.getKeyword();
        if(keyword.isPresent()) {
            conditions = PhysicianReviewSpec.hasFilterCriteriaIn(criteria)
                    .and((PhysicianReviewSpec.hasReviewContentLike(keyword.get()))
                            .or(PhysicianReviewSpec.hasPatientFirstNameLike(keyword.get()))
                            .or(PhysicianReviewSpec.hasPatientMiddleNameLike(keyword.get()))
                            .or(PhysicianReviewSpec.hasPatientLastNameLike(keyword.get())));
        }
        else{
            conditions = PhysicianReviewSpec.hasFilterCriteriaIn(criteria);
        }

        return getCustomReviewDtos(page, pageSize, conditions);
    }

    public Page<CustomReviewDto> getGroupReviewsWithFilter(GroupReviewSearchCriteria criteria, int page,
            int pageSize, UUID groupId) {
        Specification<Review> conditions;
        var keyword = criteria.getKeyword();

        if(keyword.isPresent()) {
            conditions = GroupReviewSpec.hasFilterCriteriaIn(criteria, groupId)
                    .and((GroupReviewSpec.hasReviewContentLike(keyword.get()))
                            .or(GroupReviewSpec.hasPatientFirstNameLike(keyword.get()))
                            .or(GroupReviewSpec.hasPatientMiddleNameLike(keyword.get()))
                            .or(GroupReviewSpec.hasPatientLastNameLike(keyword.get())));
        }
        else{
            conditions = GroupReviewSpec.hasFilterCriteriaIn(criteria, groupId);
        }

        return getCustomReviewDtos(page, pageSize, conditions);
    }

    private Page<CustomReviewDto> getCustomReviewDtos(int page, int pageSize, Specification<Review> conditions) {
        Page<Review> reviews = reviewRepository.findAll(conditions, PageRequest.of(page, pageSize));
        List<CustomReviewDto> customReviewDtoList = convertReviewData(reviews);
        return new PageImpl<>(customReviewDtoList, reviews.getPageable(),
                reviews.getTotalElements());
    }

    public Optional<Review> getReviewById(UUID reviewId) {
        return reviewRepository.findReviewById(reviewId);
    }

    public Review createOrUpdateReview(UUID patientId, @Valid ReviewDto newReviewDto) {
        Profile patient = profileRepository.findById(patientId).orElseThrow(EntityNotFoundException::new);
        var physicianId = newReviewDto.getPhysicianId();
        if (physicianId.isPresent()) {
            Physician physician = physicianRepository.findById(physicianId.get()).orElseThrow(EntityNotFoundException::new);
            return reviewRepository.save(ReviewConverter.dtoToReview(newReviewDto, patient, physician));
        }
        else {
            var groupId = newReviewDto.getGroupId();
            if (groupId.isPresent()) {
                GroupProvider groupProvider = groupProviderRepository.findByGroupId(groupId.get()).orElseThrow(EntityNotFoundException::new);
                return reviewRepository.save(ReviewConverter.dtoToReview(newReviewDto, patient, groupProvider));
            } else {
                throw new EntityNotFoundException();
            }
        }
    }

    public void deleteReview(UUID reviewId) {
        reviewRepository.deleteById(reviewId);
    }

    public Optional<ReviewDetailDto> getReviewDetail(UUID reviewId) {
        Optional<Review> review = reviewRepository.findReviewById(reviewId);

        return review.map(r -> {
            var patientProfile = profileRepository.findById(r.getPatientId());
            return ReviewConverter.reviewToReviewDetailDto(r, patientProfile.get());
        });
    }

    @Transactional
    public void updateReviewHelpfulStatus(UUID reviewId, boolean helpful) {
        reviewRepository.updateHelpfulStatus(reviewId, helpful);
    }

}
