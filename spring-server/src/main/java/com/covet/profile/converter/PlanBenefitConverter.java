package com.covet.profile.converter;
import com.covet.profile.clients.vba.VBADtos.Plans.ImmutableVBABenefitDto;
import com.covet.profile.clients.vba.VBADtos.Plans.ImmutableVBAPlaceCoPayItemDto;
import com.covet.profile.utils.RoundingUtils;
import com.covet.profile.clients.vba.VBADtos.Plans.VBABenefitDto;
import com.covet.profile.clients.vba.VBADtos.Plans.VBAPlaceCoPayItemDto;
import com.covet.profile.clients.vba.VBAModels.Plans.VBABenefit;
import com.covet.profile.clients.vba.VBAModels.Plans.VBAPlaceCoPayItem;

import java.math.BigDecimal;
import java.util.Optional;

public class PlanBenefitConverter {
    private PlanBenefitConverter() {
    }

    public static VBABenefitDto benefitToDto(VBABenefit benefit) {
        return ImmutableVBABenefitDto.of(
                benefit.getPlanId(),
                benefit.getBenefitCode()
        );
    }

    public static VBAPlaceCoPayItemDto toPlanBenefitDto(VBAPlaceCoPayItem placeCoPay, BigDecimal coInsurance, BigDecimal coInsuranceNet) {
        return ImmutableVBAPlaceCoPayItemDto.of(
                placeCoPay.getPlanId(),
                placeCoPay.getBenefitCode(),
                placeCoPay.getPlaceCode(),
                Optional.ofNullable(RoundingUtils.customRoundValue(placeCoPay.getCopayAmount())).orElse(BigDecimal.valueOf(0)),
                Optional.ofNullable(RoundingUtils.customRoundValue(placeCoPay.getCopayAmountNet())).orElse(BigDecimal.valueOf(0)),
                Optional.ofNullable(RoundingUtils.customRoundValue(coInsurance)).orElse(BigDecimal.valueOf(0)),
                Optional.ofNullable(RoundingUtils.customRoundValue(coInsuranceNet)).orElse(BigDecimal.valueOf(0))
        );
    }
}
