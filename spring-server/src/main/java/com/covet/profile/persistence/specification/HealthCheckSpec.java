package com.covet.profile.persistence.specification;

import org.springframework.data.jpa.domain.Specification;

import com.covet.profile.persistence.model.HealthCheck;

public class HealthCheckSpec {
    public static Specification<HealthCheck> getMethodIdsByClaimCode(String claimCode) {
        return (root, query, builder) -> {
            return builder.equal(root.get("healthCheckId").get("claimCode"), claimCode);
        };
    }
}
