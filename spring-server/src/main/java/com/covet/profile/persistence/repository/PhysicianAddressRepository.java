package com.covet.profile.persistence.repository;

import com.covet.profile.persistence.model.PhysicianAddress;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.UUID;

@Repository
public interface PhysicianAddressRepository extends JpaRepository<PhysicianAddress, UUID> {

    @Query("""
            select pa from PhysicianAddress pa
            where pa.address is not null and pa .address != ''
            """)
    Page<PhysicianAddress> getNonNullAddressByPage(Pageable page);
}
