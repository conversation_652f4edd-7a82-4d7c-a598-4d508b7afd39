package com.covet.profile.persistence.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "group_provider_address")
public class GroupProviderAddress {
    @Id
    @Column(name = "id")
    private UUID id;

    @Column(name = "address")
    private String address;
}
