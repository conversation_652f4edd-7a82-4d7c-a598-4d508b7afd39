package com.covet.profile.converter;

import com.covet.profile.dto.covet.provider.ImmutablePhysicianDto;
import com.covet.profile.dto.covet.provider.PhysicianDto;
import com.covet.profile.persistence.model.Physician;
import com.fasterxml.jackson.databind.JsonNode;

import java.net.URL;
import java.util.Optional;

public class PhysicianConverter {

    private PhysicianConverter() {
    }

    public static PhysicianDto physicianTo<PERSON><PERSON>(Physician physician, URL avatarUrl, JsonNode additionalData) {
        return ImmutablePhysicianDto.of(
                Optional.ofNullable(physician.getPhysicianId()),
                physician.getIsAcceptNewPatient(),
                physician.getIsVirtual(),
                physician.getSpecialties(),
                physician.getAddress(),
                Optional.ofNullable(physician.getAddressLine()),
                Optional.ofNullable(physician.getCity()),
                Optional.ofNullable(physician.getState()),
                Optional.ofNullable(physician.getZipCode()),
                Optional.ofNullable(physician.getNpi()),
                Optional.ofNullable(physician.getFacilityName()),
                //Optional.ofNullable(physician.getFacilityNpi()),
                Optional.ofNullable(physician.getValue()),
                Optional.ofNullable(physician.getQuality()),
                Optional.ofNullable(physician.getEfficiency()),
                Optional.ofNullable(physician.getSource()),
                physician.getProfile(),
                // Optional.ofNullable(physician.getGroups()),
                Optional.ofNullable(physician.getPhysicianRating()),
                physician.getNetworks(),
                Optional.ofNullable(avatarUrl),
                Optional.ofNullable(null));
    }

    public static Physician dtoToPhysician(PhysicianDto physicianDto) {
        return new Physician(
                physicianDto.getPhysicianId().orElse(null),
                physicianDto.getIsAcceptNewPatient(),
                physicianDto.getIsVirtual(),
                physicianDto.getAddress(),
                physicianDto.getAddressLine().orElse(null),
                physicianDto.getCity().orElse(null),
                physicianDto.getState().orElse(null),
                physicianDto.getZipCode().orElse(null),
                physicianDto.getNpi().orElse(null),
                physicianDto.getValue().orElse(0.0),
                physicianDto.getQuality().orElse(0.0),
                physicianDto.getEfficiency().orElse(0.0),
                physicianDto.getSource().orElse(null),
                physicianDto.getFacilityName().orElse(""),
                physicianDto.getProfile(),
                null,
                physicianDto.getPhysicianRating().orElse(null),
                physicianDto.getSpecialties(),
                physicianDto.getNetworks()
        );
    }
}
