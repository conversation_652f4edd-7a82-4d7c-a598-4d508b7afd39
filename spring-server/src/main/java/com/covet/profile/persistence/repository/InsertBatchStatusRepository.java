package com.covet.profile.persistence.repository;

import com.covet.profile.persistence.model.InsertBatchStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;
import java.util.UUID;

public interface InsertBatchStatusRepository extends JpaRepository<InsertBatchStatus, UUID> {

    @Query(value = """
            select max(ibs.batchNumber) from InsertBatchStatus ibs where ibs.isInserted = true and ibs.source = :source
            """)
    Optional<Integer> getMaxSuccessInsertBatch(@Param("source") String source);

    @Query(value = """
            select min(ibs.batchNumber) from InsertBatchStatus ibs where ibs.isInserted = false and ibs.source = :source
            """)
    Optional<Integer> getMinFailInsertBatch(@Param("source") String source);

    @Modifying
    @Query(value = """
            update InsertBatchStatus ibs set ibs.isInserted = true where ibs.source = :source and ibs.batchNumber = :page
            """)
    void updateInsertStatusSuccess(@Param("source") String source, @Param("page") int page);

    @Query(value = """
            select count(ibs.id) from InsertBatchStatus ibs where ibs.source = :source and ibs.batchNumber = :page
            """)
    int existBatchNumber(@Param("source") String source, @Param("page") int page);
}
