DO $$
BEGIN
  IF EXISTS(SELECT *
    FROM information_schema.columns
    WHERE table_name='claim' and column_name='claim_id')
  THEN
      ALTER TABLE "public"."claim" RENAME COLUMN "claim_id" TO "claim_code";
  END IF;
END $$; 
alter table claim drop constraint claim_pkey;
alter table claim drop column id;
alter table claim add constraint claim_pkey primary key (claim_code);

alter table health_check drop constraint health_check_pkey;
DO $$
BEGIN
  IF EXISTS(SELECT *
    FROM information_schema.columns
    WHERE table_name='health_check' and column_name='claim_id')
  THEN
      ALTER TABLE "public"."health_check" RENAME COLUMN "claim_id" TO "claim_code";
  END IF;
END $$; 
alter table health_check alter column claim_code type varchar (50);
alter table health_check add constraint health_check_pkey primary key (claim_code, method_id);
alter table health_check add foreign key (claim_code) references claim(claim_code);
alter table health_check add foreign key (method_id) references health_care_method(method_id);