import { stitchSchemas } from '@graphql-tools/stitch';
import { makeExecutableSchema } from '@graphql-tools/schema';
import typeDefs from './typeDefs.js';
import resolvers from './resolvers.js';
import { buildHTTPExecutor } from '@graphql-tools/executor-http'
import { schemaFromExecutor } from '@graphql-tools/wrap';


const createRemoteSchema = async (url, headers) => {
  const remoteExecutor = buildHTTPExecutor({ endpoint: url, headers });

  return {
    schema: await schemaFromExecutor(remoteExecutor),
    executor: remoteExecutor
  };
};


const buildSchema = async () => {
  const healthieSchema = await createRemoteSchema(process.env.HEALTHIE_URL, {
    'authorization': process.env.HEALTHIE_AUTHORIZATION,
    'authorizationsource': process.env.HEALTHIE_AUTHORIZATION_SOURCE
  });

  const sanctuarySchema = await createRemoteSchema('https://v4-0-dot-livitay.appspot.com/graphql', {
    'apikey': process.env.SANCTUARY_APIKEY
  });

  const localSchema = makeExecutableSchema({
    typeDefs,
    resolvers,
  });

  const schema = stitchSchemas({
    subschemas: [healthieSchema, localSchema, sanctuarySchema], 
  });

  return schema;
}

export default buildSchema;
