package com.covet.profile.persistence.model;

import com.sun.istack.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.UUID;



@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "physician_rating")
public class AverageRating {
    @Id
    @Column(name = "physician_id")
    private UUID physicianId;

    @NotNull
    @Column(name = "average_rating")
    private int averageRating;
    }