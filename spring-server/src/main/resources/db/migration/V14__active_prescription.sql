create table prescription (
                        prescription_name varchar (256) not null,
                        prescription_code varchar (256) not null,
                        primary key (prescription_name, prescription_code),
                        quantity decimal,
                        form varchar (256) not null,
                        strength decimal,
                        next_refill_date timestamp
                      );
create table pharmacy (
                        pharmacy_id uuid not null,
                        primary key (pharmacy_id),
                        latitude decimal,
                        longitude decimal,
                        pharmacy_name varchar (256) not null,
                        address varchar (256) not null
                      );
create table sale_prescription (
                        prescription_name varchar (256) not null,
                        prescription_code varchar (256) not null,
                        pharmacy_id uuid not null,
                        cost decimal,
                        primary key (prescription_name, prescription_code, pharmacy_id),
                        constraint fk_sale_prescription_name foreign key (prescription_name, prescription_code) references prescription,
                        constraint fk_sale_prescription_pharmacy foreign key (pharmacy_id) references pharmacy
                      );


                      
                      