create table review  (
    review_id uuid not null,
    patient_id uuid not null,
    physician_id uuid not null,
    created_date timestamp,
    content varchar (65535),
    rating integer check (rating between 0 and 5),
    visit_type integer not null,

    primary key (review_id),
    constraint fk_patient_id foreign key (patient_id) references profile,
    constraint fk_physician_id foreign key (physician_id) references physician
);





