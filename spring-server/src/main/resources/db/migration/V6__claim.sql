create table claim (
                             id uuid not null,
                             patient_id uuid not null,
                             physician_id uuid not null,
                             claim_id varchar not null,
                             created_date timestamp not null,
                             total_claim decimal default 0,
                             is_paid boolean default false,
                             claim_type int default 0,

                             primary key (id, patient_id, physician_id, created_date),
                             constraint fk_physician_claim foreign key (physician_id) references physician,
                             constraint fk_patient_claim foreign key (patient_id) references profile
);