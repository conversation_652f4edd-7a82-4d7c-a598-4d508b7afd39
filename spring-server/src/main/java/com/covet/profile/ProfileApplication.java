package com.covet.profile;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.servers.Server;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

import java.util.TimeZone;

@SpringBootApplication
@OpenAPIDefinition(info = @Info(title = "Covet Profile API", version = "1.0",
				   description = "Covet Profile Application"),
				   security = @SecurityRequirement(name = "oauth2"),
				   servers = {
				    @Server(url = "${swagger.server-url}", description = "server")
				   })
@EnableFeignClients
public class ProfileApplication {
	public static final Logger log = LoggerFactory.getLogger(ProfileApplication.class);

	public static void main(String[] args) {
		SpringApplication.run(ProfileApplication.class, args);
	}

}