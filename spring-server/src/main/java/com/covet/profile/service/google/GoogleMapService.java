package com.covet.profile.service.google;

import com.covet.profile.persistence.model.GroupProviderAddress;
import com.covet.profile.persistence.model.PhysicianAddress;
import com.google.maps.GeoApiContext;
import com.google.maps.GeocodingApi;
import com.google.maps.GeocodingApiRequest;
import com.google.maps.PendingResult;
import com.google.maps.errors.ApiException;
import com.google.maps.model.GeocodingResult;
import com.google.maps.model.LatLng;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;

@Configuration
@RequiredArgsConstructor
@Slf4j
public class GoogleMapService {
    private final GeoApiContext context;


    public LatLng getCoordinatesByAddress(String address) {
        GeocodingResult[] res;
        try {
            res = GeocodingApi.geocode(context, address).await();
            if (res.length > 0) {
                return Arrays.stream(res).toList().get(0).geometry.location;
            }
            context.shutdown();
        } catch (ApiException | InterruptedException | IOException e) {
            log.info("Some thing wrong when get coordinate {}", e.getMessage());
            Thread.currentThread().interrupt();
        }
        return new LatLng(0.0, 0.0);
    }

    public List<LatLng> getCoordinatesByAddressList(List<String> addressList) {
        var futures = addressList.stream().map(item -> CompletableFuture.supplyAsync(() -> getCoordinatesByAddress(item))).toList();
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        List<LatLng> result = futures.stream().map(item -> {
            try {
                return item.get();
            } catch (InterruptedException | ExecutionException e) {
                Thread.currentThread().interrupt(); // Re-interrupt the thread
                log.info("Some thing wrong when get coordinate {}", e.getMessage());
            }
            return new LatLng(0.0, 0.0);
        }).toList();
        context.shutdown();
        return result;
    }

    public Map<UUID, LatLng> getCoordinatesTest(List<PhysicianAddress> physicianAddressList) {
        Map<UUID, LatLng> coordinatesMap = new ConcurrentHashMap<>();
        physicianAddressList.forEach(
                item -> {
                    GeocodingApiRequest req = GeocodingApi.newRequest(context).address(item.getAddress());
                    req.setCallback(new PendingResult.Callback<>() {
                        @Override
                        public void onResult(GeocodingResult[] result) {
                            if (result.length != 0) {
                                LatLng place = result[0].geometry.location;
                                coordinatesMap.computeIfAbsent(item.getId(), (__) -> place);
                            }
                        }

                        @Override
                        public void onFailure(Throwable e) {
                            log.info("something wrong with message {}", e.getMessage());
                        }
                    });
                }
        );
        try {
            Thread.sleep(30000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return coordinatesMap;
    }

    public Map<UUID, LatLng> getCoordinatesForGroupProvider(List<GroupProviderAddress> groupProviderAddressList) {
        Map<UUID, LatLng> coordinatesMap = new ConcurrentHashMap<>();
        groupProviderAddressList.forEach(
                item -> {
                    GeocodingApiRequest req = GeocodingApi.newRequest(context).address(item.getAddress());
                    req.setCallback(new PendingResult.Callback<>() {
                        @Override
                        public void onResult(GeocodingResult[] result) {
                            if (result.length != 0) {
                                LatLng place = result[0].geometry.location;
                                coordinatesMap.computeIfAbsent(item.getId(), (__) -> place);
                            }
                        }

                        @Override
                        public void onFailure(Throwable e) {
                            log.info("something wrong with message {}", e.getMessage());
                        }
                    });
                }
        );
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return coordinatesMap;
    }
}
