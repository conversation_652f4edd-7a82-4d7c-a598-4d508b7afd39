package com.covet.profile.persistence.model;

import java.util.List;

import com.covet.profile.dto.covet.pharmacy.PharmacyDetailDto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PharmacyListWithMessage {
    private List<PharmacyDetailDto> pharmacyList;
    private String message;

    // Constructor
    public PharmacyListWithMessage(List<PharmacyDetailDto> pharmacyList, String message) {
        this.pharmacyList = pharmacyList;
        this.message = message;
    }

}