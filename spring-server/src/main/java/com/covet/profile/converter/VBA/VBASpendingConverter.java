package com.covet.profile.converter.VBA;

import com.covet.profile.clients.vba.VBADtos.IDcard.Spending.ImmutableVBASpendingAttributesDto;
import com.covet.profile.utils.RoundingUtils;
import com.covet.profile.clients.vba.VBADtos.IDcard.Spending.VBASpendingAttributesDto;
import java.math.BigDecimal;

public class VBASpendingConverter {
    public static VBASpendingAttributesDto vbaSpendingAttributesToDto(BigDecimal inNetworkAmount, BigDecimal outNetworkAmount) {
        return ImmutableVBASpendingAttributesDto.of(
                RoundingUtils.customRoundValue(inNetworkAmount),
                RoundingUtils.customRoundValue(outNetworkAmount)
        );
    }
}
