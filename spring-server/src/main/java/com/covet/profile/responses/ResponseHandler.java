package com.covet.profile.responses;

import com.covet.profile.converter.ProfileConverter;
import com.covet.profile.dto.*;
import com.covet.profile.dto.covet.pharmacy.PharmacyDetailDto;
import com.covet.profile.dto.covet.prescription.PrescriptionDetailDto;
import com.covet.profile.dto.covet.profile.ProfileDto;
import com.covet.profile.dto.covet.provider.ImmutablePhysicianDetailDto;
import com.covet.profile.dto.covet.provider.PhysicianDetailDto;
import com.covet.profile.dto.covet.provider.review.CustomReviewDto;
import com.covet.profile.dto.covet.provider.review.ImmutableCustomReviewDto;
import com.covet.profile.persistence.model.*;
import com.covet.profile.utils.RoundingUtils;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.net.URL;
import java.util.*;

public class ResponseHandler {
    private static final Logger log = LoggerFactory.getLogger(ResponseHandler.class);

    public static ResponseEntity<Object> generateClaimResponse(Object responseObj, int numOfClaims,
            double totalPriceOfClaims, HttpStatus status) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("data", responseObj);
        map.put("numOfClaims", numOfClaims);
        map.put("totalPriceOfClaims", RoundingUtils.roundDoubleValue(totalPriceOfClaims));
        map.put("httpStatusCode", status.value());
        return new ResponseEntity<>(map, status);
    }

    public static ResponseEntity<Object> generateVbaClaimResponse(Object responseObj, int numOfClaims,
                                                                  BigDecimal totalPriceOfClaims, HttpStatus status) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("data", responseObj);
        map.put("numOfClaims", numOfClaims);
        map.put("totalPriceOfClaims", RoundingUtils.customRoundValue(totalPriceOfClaims));
        map.put("httpStatusCode", status.value());
        return new ResponseEntity<>(map, status);
    }

    public static Map<String, Object> generateHealthCareMethod(UUID methodId, String methodName, Double cost,
            List<DiscountDto> discounts) {
        Map<String, Object> healthCareMethodInfo = new HashMap<String, Object>();
        healthCareMethodInfo.put("methodId", methodId);
        healthCareMethodInfo.put("methodName", methodName);
        healthCareMethodInfo.put("cost", RoundingUtils.roundDoubleValue(cost));
        healthCareMethodInfo.put("discounts", discounts);

        return healthCareMethodInfo;
    }

    public static ResponseEntity<Object> generateIndividualClaimDetailResponse(
            List<Map<String, Object>> healthCareMethods, VisitCost visitCost, Double totalCost,
            Double totalCostMethods,
            Date createdDate, HttpStatus status) {
        Map<String, Object> individualClaimDetailResponse = new HashMap<String, Object>();
        individualClaimDetailResponse.put("healthCareMethods", healthCareMethods);
        individualClaimDetailResponse.put("virtualVisitCost", visitCost.getVirtualVisitCost());
        individualClaimDetailResponse.put("visitCost", visitCost.getVisitCost());
        individualClaimDetailResponse.put("totalCost", RoundingUtils.roundDoubleValue(totalCost));
        individualClaimDetailResponse.put("totalCostMethods", totalCostMethods);
        individualClaimDetailResponse.put("createdDate", createdDate);

        return new ResponseEntity<>(individualClaimDetailResponse, status);
    }

    public static Map<String, Object> generatePrescriptionResponse(
            Prescription prescription, URL imgURL, String claimCode, long numOfRefills, String prescriber,
            String pharmacy,
            Double cost, Date initRefillDate,
            Date nextRefillDate) {
        Map<String, Object> response = new HashMap<String, Object>();
        response.put("prescriptionName", prescription.getPrescriptionId().getPrescriptionName());
        response.put("prescriptionCode", prescription.getPrescriptionId().getPrescriptionCode());
        response.put("prescriptionImgURL", imgURL);
        response.put("quantity", prescription.getQuantity());
        response.put("form", prescription.getForm());
        response.put("strength", prescription.getStrength());
        response.put("refills", numOfRefills);
        response.put("initRefillDate", initRefillDate);
        response.put("nextRefillDate", nextRefillDate);
        response.put("prescriber", prescriber);
        response.put("pharmacy", pharmacy);
        response.put("cost", RoundingUtils.roundDoubleValue(cost));
        response.put("claimCode", claimCode);

        return response;
    }

    public static ResponseEntity<Object> generatePrescriptionPharmacySetResponse(
            PrescriptionDetailDto prescriptionDetail, Page<PharmacyDetailDto> pharmacySet, HttpStatus status) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("prescriptionDetail", prescriptionDetail);
        map.put("pharmacyList", pharmacySet);
        return new ResponseEntity<>(map, status);
    }

//    public static ResponseEntity<Object> generatePrescriptionPharmacyDetailResponse(
//            PrescriptionDetailDto prescriptionDetail, PharmacyDetailDto pharmacy, HttpStatus status) {
//        Map<String, Object> map = new HashMap<String, Object>();
//        map.put("prescriptionDetail", prescriptionDetail);
//        map.put("pharmacy", pharmacy);
//
//        return new ResponseEntity<>(map, status);
//    }

    public static ResponseEntity<Object> generatePrescriptionPharmacyDetailResponse(
            PrescriptionDetailDto prescriptionDetail, PharmacyDetailDto pharmacy, HttpStatus status) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("prescriptionDetail", prescriptionDetail);
        map.put("pharmacy", pharmacy);

        return new ResponseEntity<>(map, status);
    }

    public static Map<String, Object> generateInvalidPrescriptionItem(Prescription prescription, URL imgURL,
            Boolean isVerified,
            Boolean isPArequired,
            Boolean isNoPrescription) {
        Map<String, Object> item = new HashMap<String, Object>();
        item.put("prescriptionInfo", prescription);
        item.put("prescriptionImgURL", imgURL);
        item.put("isVerified", isVerified);
        item.put("isPArequired", isPArequired);
        item.put("isNoPrescription", isNoPrescription);

        return item;
    }

    public static PhysicianDetailDto generatePhysicianItem(Physician physician,
            URL avatarUrl,
            Optional<Object> npiInfo,
            Double distance,
            Map<UUID, Set<Network>> networkMap,
            Map<UUID, List<Specialty>> specialtyMap,
            Boolean isFavorite,
            boolean isInNetwork,
            boolean showInNetworkTag
    ) {
        log.info("Generating PhysicianDetailDto for physician {} - isInNetwork: {}, showInNetworkTag: {}", 
            physician.getPhysicianId(), isInNetwork, showInNetworkTag);
            
        Profile profile = physician.getProfile();
        ProfileDto profileDto = ProfileConverter.profileToProfileDto(profile, avatarUrl);
        return ImmutablePhysicianDetailDto.of(
                Optional.ofNullable(physician.getPhysicianId()),
                Optional.empty(),
                physician.getIsAcceptNewPatient(),
                physician.getIsVirtual(),
                Optional.ofNullable(specialtyMap.get(physician.getPhysicianId())).orElse(new LinkedList<>()),
                physician.getAddress(),
                Optional.ofNullable(physician.getAddressLine()),
                Optional.ofNullable(physician.getCity()),
                Optional.ofNullable(physician.getState()),
                Optional.ofNullable(physician.getZipCode()),
                Optional.ofNullable(physician.getNpi()),
                Optional.of(RoundingUtils.roundDoubleValue(physician.getValue())),
                Optional.of(RoundingUtils.roundDoubleValue(physician.getQuality())),
                Optional.of(RoundingUtils.roundDoubleValue(physician.getEfficiency())),
                Optional.ofNullable(physician.getSource()),
                Optional.ofNullable(physician.getFacilityName()),
                Optional.ofNullable(distance),
                isInNetwork,
                showInNetworkTag,
                profileDto,
                Optional.ofNullable(physician.getPhysicianRating()),
                Optional.ofNullable(networkMap.get(physician.getPhysicianId())).orElse(new HashSet<>()),
                npiInfo,
                Optional.ofNullable(isFavorite));
    }

    public static CustomReviewDto generateReviewItem(Review review, URL avatarUrl) {
        ProfileDto profileDto = ProfileConverter.profileToProfileDto(review.getProfile(), avatarUrl);
        return ImmutableCustomReviewDto.of(
                Optional.ofNullable(review.getId()),
                Optional.ofNullable(review.getPhysicianId()),
                Optional.ofNullable(review.getGroupId()),
                review.getPatientId(),
                Optional.ofNullable(review.getContent()),
                review.getRating(),
                review.getCreatedDate(),
                review.getVisitType(),
                review.getIsHelpful(),
                review.getHelpfulTotal(),
                Optional.ofNullable(profileDto),
                Optional.ofNullable(review.getGroup()));
    }
}
