package com.covet.profile.exception;

import org.springframework.http.HttpStatus;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RestTemplateException extends RuntimeException {

    private String api;
    private HttpStatus statusCode;
    private String error;

    public RestTemplateException(String api, HttpStatus statusCode, String error) {
        super(error);
        this.api = api;
        this.statusCode = statusCode;
        this.error = error;
    }
}