package com.covet.profile.controller;

import com.covet.profile.Constant;
import com.covet.profile.FactoryMethod.CreateFavoriteFactoryMethod;
import com.covet.profile.FactoryMethod.GetFavorite.GetFavoriteRequest;
import com.covet.profile.FactoryMethod.GetFavoriteFactoryMethod;
import com.covet.profile.clients.vba.VBADtos.Member.SubscriberMemberDto;
import com.covet.profile.clients.vba.VBADtos.Plans.PlanBenefitDto;
import com.covet.profile.dto.covet.appointment.AppointmentDto;
import com.covet.profile.dto.covet.appointment.ImmutableAppointmentDto;
import com.covet.profile.dto.covet.claim.ClaimMemberRequestDto;
import com.covet.profile.dto.covet.pharmacy.PharmacyDetailDto;
import com.covet.profile.dto.covet.pharmacy.PharmacyIdDto;
import com.covet.profile.dto.covet.pharmacy.PreferredPharmacyDto;
import com.covet.profile.dto.covet.prescription.ActivePrescriptionsDto;
import com.covet.profile.dto.covet.provider.PhysicianDetailDto;
import com.covet.profile.dto.covet.provider.PhysicianDto;
import com.covet.profile.dto.enums.EReminderStatus;
import com.covet.profile.persistence.compositeKey.PrescriptionID;
import com.covet.profile.converter.AppointmentConverter;
import com.covet.profile.converter.PhysicianConverter;
import com.covet.profile.converter.notification.response.GetListMedicineNotificationResponse;
import com.covet.profile.converter.notification.response.ReminderRes;
import com.covet.profile.dto.*;
import com.covet.profile.dto.PrescriptionSchedulerDto.UpdateNotificationRequest;
import com.covet.profile.dto.ProviderDto.LocationDto;
import com.covet.profile.dto.common.PageRequestParams;
import com.covet.profile.dto.reminder.ReminderDto;
import com.covet.profile.persistence.model.*;
import com.covet.profile.exception.ResourceNotFoundException;
import com.covet.profile.responses.ResponseHandler;
import com.covet.profile.searchCriteria.ClaimSearchCriteria;
import com.covet.profile.searchCriteria.PhysicianSearchCriteria;
import com.covet.profile.service.*;
import com.covet.profile.systemEnum.SysEnum;
import com.covet.profile.utils.AuthUtils;
import com.covet.profile.clients.vba.VBADtos.Claim.VBAClaimDetailDto;
import com.covet.profile.clients.vba.VBADtos.Claim.VBAClaimDto;
import com.covet.profile.clients.vba.VBADtos.Financial.FinancialDto;
import com.covet.profile.clients.vba.VBADtos.IDcard.VBAIDcardDto;
import com.covet.profile.clients.vba.VBADtos.Member.VBAMemberDto;
import com.covet.profile.clients.vba.VBADtos.Subscriber.EnrollmentSubscriberDto;
import com.covet.profile.clients.vba.VBAInterfaces.IVBAUserService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.util.StreamUtils;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.net.URL;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ExecutionException;

// @PreAuthorize("hasAuthority('read:patientdata')")
@Validated
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class PatientController {
    public static final Logger log = LoggerFactory.getLogger(PatientController.class);
    private final PhysicianService physicianService;
    private final ProfileService profileService;

    private final ClaimService claimService;
    private final IndividualService individualService;

    private final PrescriptionService prescriptionService;
    private final PharmacyService pharmacyService;

    private final S3Service s3Service;
    // private final ICognitoService cognitoService;
    private final Auth0Service auth0Service;
    private final IVBAUserService vbaUserService;
    private final MpxService mpxService;

    private final PlanBenefitService planBenefitService;
    private final CreateFavoriteFactoryMethod<CreateFavoriteRequest> createFavoriteFactoryMethod;
    private final GetFavoriteFactoryMethod<GetFavoriteRequest, ?> getFavoriteFactoryMethod;
    private final NotificationService notificationService;
    private final PushNotificationService pushNotificationService;
    @Autowired
    private ResourceLoader resourceLoader;

    @GetMapping("/patient/appointments")
    public ResponseEntity<Set<AppointmentDto>> getAppointments() {
        UUID cognitoId = AuthUtils.getCognitoId();
        Set<AppointmentDto> foundAppointments = physicianService.getAppointmentsByPatient(cognitoId);
        return new ResponseEntity<>(foundAppointments, HttpStatus.OK);
    }

    @GetMapping("/patient/{id}/appointment")
    public ResponseEntity<AppointmentDto> getAppointmentById(@PathVariable("id") UUID appointmentId) { // should be dto
        UUID cognitoId = AuthUtils.getCognitoId(); // passing in cognitoId to make sure whoever using this api should
        // have a cognitoId
        Appointment appointment = physicianService.getAppointment(appointmentId, cognitoId);
        Optional<PhysicianDto> physician = physicianService.getPhysicianById(appointment.getPhysicianId()); // PhysicianId

        return physician.map(physicianDto -> {
                    appointment.setPhysician(PhysicianConverter.dtoToPhysician(physicianDto));
                    return new ResponseEntity<>(AppointmentConverter.appointmentToDto(appointment), HttpStatus.OK);
                })
                .orElseGet(() -> {
                    log.error("PhysicianId {} doesn't exist.", cognitoId);
                    return new ResponseEntity<>(HttpStatus.NOT_FOUND);
                });
    }

    // new one that use criteria
    @GetMapping("/patient/physicians")
    public ResponseEntity<Page<PhysicianDetailDto>> getPhysiciansWithOrWithoutFilter(
            @Valid @ParameterObject PhysicianSearchCriteria criteria, Optional<Integer> page,
            Optional<Integer> pageSize, LocationDto location) throws Exception {
        if ((page.isEmpty() && pageSize.isPresent()) || (page.isPresent() && pageSize.isEmpty())) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        Page<PhysicianDetailDto> physicians = physicianService.getPhysicians(page, pageSize, criteria, location);
        return new ResponseEntity<>(physicians, HttpStatus.OK);
    }

    @GetMapping("/patient/{id}/physician")
    public ResponseEntity<PhysicianDto> getPhysician(@PathVariable("id") UUID cognitoId) {
        Optional<PhysicianDto> physician = physicianService.getPhysicianById(cognitoId); // PhysicianId
        return physician
                .map(phy -> new ResponseEntity<>(phy, HttpStatus.OK))
                .orElseGet(() -> {
                    log.error("PhysicianId {} doesn't exist.", cognitoId);
                    return new ResponseEntity<>(HttpStatus.NOT_FOUND);
                });
    }

    // assume you login as a patient to create appointment
    @PostMapping(path = "/patient/appointment", consumes = MediaType.APPLICATION_JSON_VALUE)
    public void createAppointment(
            @Valid @RequestBody final AppointmentDto newAppointmentDto) {
        UUID patientId = AuthUtils.getCognitoId();
        UUID physicianId = newAppointmentDto.getPhysicianId();
        physicianService.getPhysicianById(physicianId).ifPresentOrElse(physician -> {
                    var appointment = physicianService.createOrUpdateAppointmentV2(
                            ImmutableAppointmentDto.copyOf(newAppointmentDto).withPatientId(patientId));
                    pushNotificationService.createOrUpdateAppointmentNoti(appointment, false);
                }, () -> log.error("PhysicianId {} doesn't exist for creating appointment", physicianId));
    }

    @PutMapping(path = "/patient/appointment", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<AppointmentDto> updateAppointment(
            @Valid @RequestBody final AppointmentDto updateAppointmentDto) {
        UUID patientId = AuthUtils.getCognitoId(); // patientId
        UUID physicianId = updateAppointmentDto.getPhysicianId();
        return physicianService.getPhysicianById(physicianId)
                .map(physician -> {
                    AppointmentDto updateDto = physicianService.createOrUpdateAppointment(
                            ImmutableAppointmentDto.copyOf(updateAppointmentDto).withPatientId(patientId));
                    return new ResponseEntity<>(updateDto, HttpStatus.OK);
                })
                .orElseGet(() -> {
                    log.error("PhysicianId {} doesn't exist for create appointment", physicianId);
                    return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
                });
    }

    @DeleteMapping("/patient/appointment")
    public ResponseEntity<UUID> deleteAppointment(@RequestParam UUID id) {
        UUID patientId = AuthUtils.getCognitoId(); // patientId
        return profileService.getProfileById(patientId).map(profile -> {
            Appointment foundAppointment = physicianService.getAppointment(id, patientId);
            physicianService.deleteAppointment(id);
            return new ResponseEntity<>(foundAppointment.getId(), HttpStatus.OK);
        }).orElseGet(() -> {
            log.error(MessageFormat.format(Constant.PATIENT_NOT_FOUND, patientId));
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        });
    }

    @GetMapping("/patient/{id}/postVisit")
    public ResponseEntity<VisitSummary> getPostVisit(@PathVariable("id") UUID id) {
        UUID patientId = AuthUtils.getCognitoId(); // patientId
        return profileService.getProfileById(patientId).map(profile -> {
            Appointment foundAppointment = physicianService.getAppointment(id, patientId);
            String postVisitString = foundAppointment.getPostVisit();
            Optional<PhysicianDto> physician = physicianService.getPhysicianById(foundAppointment.getPhysicianId()); // PhysicianId
            if (StringUtils.isBlank(postVisitString)) {
                return new ResponseEntity<VisitSummary>(HttpStatus.NO_CONTENT);
            }
            try {
                PostVisit postVisitObj = new ObjectMapper().readValue(postVisitString, PostVisit.class);
                return new ResponseEntity<>(new VisitSummary(postVisitObj, physician.get()), HttpStatus.OK);
            } catch (JsonProcessingException e) {
                return new ResponseEntity<VisitSummary>(HttpStatus.BAD_REQUEST);
            }
        }).orElseGet(() -> {
            log.error(MessageFormat.format(Constant.PATIENT_NOT_FOUND, patientId));
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        });
    }

    @GetMapping("/appointment/{id}/preVisit")
    public ResponseEntity<PreVisit> getPreVisit(@PathVariable("id") UUID id) {
        UUID patientId = AuthUtils.getCognitoId(); // patientId
        return profileService.getProfileById(patientId).map(profile -> {
            Appointment foundAppointment = physicianService.getAppointment(id, patientId);
            String preVisitString = foundAppointment.getPreVisit();
            if (StringUtils.isBlank(preVisitString)) {
                return new ResponseEntity<PreVisit>(HttpStatus.NO_CONTENT);
            }
            try {
                return new ResponseEntity<>(new ObjectMapper().readValue(preVisitString, PreVisit.class),
                        HttpStatus.OK);
            } catch (JsonProcessingException e) {
                return new ResponseEntity<PreVisit>(HttpStatus.BAD_REQUEST);
            }
        }).orElseGet(() -> {
            log.error(MessageFormat.format(Constant.PATIENT_NOT_FOUND, patientId));
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        });
    }

    @PutMapping(path = "/patient/appointment/{id}/preVisit", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<PreVisit> answerPreVisit(@PathVariable("id") UUID id, @RequestBody PreVisit preVisit) {
        UUID patientId = AuthUtils.getCognitoId(); // patientId
        return profileService.getProfileById(patientId).map(profile -> {
            Appointment theAppointment = physicianService.getAppointment(id, patientId);
            try {
                theAppointment.setPreVisit(new ObjectMapper().writeValueAsString(preVisit));
            } catch (JsonProcessingException e) {
                return new ResponseEntity<PreVisit>(HttpStatus.BAD_REQUEST);
            }
            physicianService.createOrUpdateAppointment(theAppointment);
            return new ResponseEntity<>(preVisit, HttpStatus.OK);
        }).orElseGet(() -> {
            log.error(MessageFormat.format(Constant.PATIENT_NOT_FOUND, patientId));
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        });
    }

    // CH-121: new enpoint to prevent crash
    @Operation(summary = "CH-121: Fetch claims from VBA for displaying in claims overview page")
    @GetMapping("/patient/claims")
    public ResponseEntity<Object> getClaimsWithFilterNew(@Valid @ParameterObject ClaimSearchCriteria criteria,
            Optional<String> memberSequence,
            Optional<Integer> page, Optional<Integer> pageSize) throws Exception {
        UUID patientId = AuthUtils.getCognitoId();

        String auth0ID = AuthUtils.getAuth0Id();
        // String email = auth0Service.getUserEmail(auth0ID);

        // SubscriberMemberDto subscriberID = vbaUserService.getSubscriberMemberInfo(email);
        SubscriberMemberDto subscriberID = auth0Service.getSubscriberMemberID(auth0ID);

        int numOfClaims = 0;
        BigDecimal totalPriceOfClaims = new BigDecimal("0");

        List<VBAClaimDetailDto> vbaClaimDetailDtoList = new ArrayList<>();

        var foundClaimNumbers = claimService.getClaimsBySubscriber("003736664", memberSequence);

        if (!foundClaimNumbers.isEmpty()) {
            var providerIdList = foundClaimNumbers.stream().map(VBAClaimDto::getProviderId).toList();
            var providerList = vbaUserService.fetchVbaProviderList(providerIdList);

            for (VBAClaimDto item : foundClaimNumbers) {
                var claimDetailList = claimService.getClaimDetailList(item, providerList, patientId);
                if (!claimDetailList.isEmpty()) {
                    vbaClaimDetailDtoList.addAll(claimDetailList);
                }
            }
        } else {
            //TODO: handle no results better. shouldnt be an error either front end handles 404 or we send 204 or something to show query worked but empty
            return ResponseHandler.generateVbaClaimResponse(vbaClaimDetailDtoList, numOfClaims, totalPriceOfClaims, HttpStatus.OK);
            // return ResponseHandler.generateVbaClaimResponse(vbaClaimDetailDtoList, numOfClaims, totalPriceOfClaims,
            //         HttpStatus.NOT_FOUND);
        }

        if (claimService.hasCriteria(criteria)) {
            vbaClaimDetailDtoList = claimService.filterClaimDetailList(vbaClaimDetailDtoList, criteria);
        }

        numOfClaims = foundClaimNumbers.size();
        totalPriceOfClaims = vbaClaimDetailDtoList.stream()
                .map(VBAClaimDetailDto::getClaimTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);


        Pageable pageable = PageRequest.of(page.orElse(SysEnum.EPaging.PageNumber.getValue()),
                pageSize.orElse(SysEnum.EPaging.PageSize.getValue()));

        Page<VBAClaimDetailDto> result;

        if (!vbaClaimDetailDtoList.isEmpty()) {
            int start = (int) pageable.getOffset();
            int end = Math.min((start + pageable.getPageSize()), vbaClaimDetailDtoList.size());

            // Add check for case: Same account but different values on server? - TODO: Review to find root cause later
            // Check if start is within bounds
            if (start >= 0 && start < vbaClaimDetailDtoList.size()) {
                List<VBAClaimDetailDto> pageItems = vbaClaimDetailDtoList.subList(start, end);

                result = new PageImpl<>(pageItems, pageable, vbaClaimDetailDtoList.size());
            } else {
                // Handle the case where start is out of bounds
                result = Page.empty(); // or another appropriate empty Page
            }
        } else {
            result = Page.empty();
        }

        return ResponseHandler.generateVbaClaimResponse(result, numOfClaims, totalPriceOfClaims, HttpStatus.OK);
    }

    @Operation(summary = "Fetch claims from VBA based on member sequences")
    @PostMapping("/patient/claims")
    public ResponseEntity<Object> getClaimsFromMemberSequences(@Valid @ParameterObject ClaimSearchCriteria criteria,
            @RequestBody @Valid ClaimMemberRequestDto request) throws Exception {
        UUID patientId = AuthUtils.getCognitoId();

        String auth0ID = AuthUtils.getAuth0Id();
        // String email = auth0Service.getUserEmail(auth0ID);

        // SubscriberMemberDto subscriberID = vbaUserService.getSubscriberMemberInfo(email);
        SubscriberMemberDto subscriberID = auth0Service.getSubscriberMemberID(auth0ID);

        int numOfClaims = 0;
        BigDecimal totalPriceOfClaims = new BigDecimal("0");

        List<VBAClaimDetailDto> vbaClaimDetailDtoList = new ArrayList<>();

        List<VBAClaimDto> foundClaimNumbers = claimService.getClaimsBySubscriberAndMemberSequences("003736664", request.getMemberSequences());

        if (!foundClaimNumbers.isEmpty()) {
            var providerIdList = foundClaimNumbers.stream().map(VBAClaimDto::getProviderId).toList();
            var providerList = vbaUserService.fetchVbaProviderList(providerIdList);

            for (VBAClaimDto item : foundClaimNumbers) {
                var claimDetailList = claimService.getClaimDetailList(item, providerList, patientId);
                if (!claimDetailList.isEmpty()) {
                    vbaClaimDetailDtoList.addAll(claimDetailList);
                }
            }
        } else {
            //TODO: handle no results better. shouldnt be an error either front end handles 404 or we send 204 or something to show query worked but empty
            return ResponseHandler.generateVbaClaimResponse(vbaClaimDetailDtoList, numOfClaims, totalPriceOfClaims, HttpStatus.OK);
            // return ResponseHandler.generateVbaClaimResponse(vbaClaimDetailDtoList, numOfClaims, totalPriceOfClaims,
            //         HttpStatus.NOT_FOUND);
        }

        if (claimService.hasCriteria(criteria)) {
            vbaClaimDetailDtoList = claimService.filterClaimDetailList(vbaClaimDetailDtoList, criteria);
        }

        numOfClaims = foundClaimNumbers.size();
        totalPriceOfClaims = vbaClaimDetailDtoList.stream()
                .map(VBAClaimDetailDto::getClaimTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);


        Pageable pageable = PageRequest.of(request.getPage().orElse(SysEnum.EPaging.PageNumber.getValue()),
        request.getPageSize().orElse(SysEnum.EPaging.PageSize.getValue()));

        Page<VBAClaimDetailDto> result;

        if (!vbaClaimDetailDtoList.isEmpty()) {
            int start = (int) pageable.getOffset();
            int end = Math.min((start + pageable.getPageSize()), vbaClaimDetailDtoList.size());

            // Add check for case: Same account but different values on server? - TODO: Review to find root cause later
            // Check if start is within bounds
            if (start >= 0 && start < vbaClaimDetailDtoList.size()) {
                List<VBAClaimDetailDto> pageItems = vbaClaimDetailDtoList.subList(start, end);

                result = new PageImpl<>(pageItems, pageable, vbaClaimDetailDtoList.size());
            } else {
                // Handle the case where start is out of bounds
                result = Page.empty(); // or another appropriate empty Page
            }
        } else {
            result = Page.empty();
        }

        return ResponseHandler.generateVbaClaimResponse(result, numOfClaims, totalPriceOfClaims, HttpStatus.OK);
    }

    @GetMapping("/patient/claim/subscriber/members")
    public ResponseEntity<List<VBAMemberDto>> getMemberListBySubscriber() throws Exception {
        String auth0ID = AuthUtils.getAuth0Id();
        // String email = auth0Service.getUserEmail(auth0ID);
        // SubscriberMemberDto subscriberID = vbaUserService.getSubscriberMemberInfo(email);
        SubscriberMemberDto subscriberID = auth0Service.getSubscriberMemberID(auth0ID);

        // List<VBAMemberDto> memberList = vbaUserService.geMemberListBySubscriberId(subscriberID.getSubscriberId());
        //Tried to edit an immutable list before ig there was one member
        List<VBAMemberDto> memberList = new ArrayList<>(vbaUserService.geMemberListBySubscriberId("000964844"));
        //If only that subscriber exists in the list then no need to return data
        if (memberList.size() == 1) {
            memberList.clear();
        }
        return new ResponseEntity<>(memberList, HttpStatus.OK);
    }

    @GetMapping("/patient/individual-claim-detail/{claimCode}")
    public ResponseEntity<Object> getIndividualClaimDetail(@PathVariable("claimCode") String claimCode) {
        return individualService.getIndividualClaimDetail(claimCode);
    }

    // CH-117: Update function using caprx data
    @GetMapping("/patient/active-prescriptions")
    public ResponseEntity<Page<ActivePrescriptionsDto>> getActivePrescriptions(
            @Valid @Parameter Optional<String> keyword, Optional<Integer> page, Optional<Integer> pageSize) {
        Pageable pageable = PageRequest.of(page.orElse(SysEnum.EPaging.PageNumber.getValue()),
                pageSize.orElse(SysEnum.EPaging.PageSize.getValue()));
        Page<ActivePrescriptionsDto> prescriptionList = prescriptionService.getActivePrescriptions(pageable, keyword);
        return new ResponseEntity<>(prescriptionList, HttpStatus.OK);
    }

    @GetMapping("/patient/prescriptions/{claimCode}/{prescriptionName}/{prescriptionCode}")
    public ResponseEntity<Object> getPrescriptionClaimDetail(@PathVariable String claimCode,
                                                             @PathVariable String prescriptionName, @PathVariable String prescriptionCode) {
        UUID patientId = AuthUtils.getCognitoId();
        Map<String, Object> result = prescriptionService.getPrescriptionClaimDetail(claimCode,
                prescriptionName,
                prescriptionCode, patientId);
        return new ResponseEntity<>(result, HttpStatus.OK);

    }

    @GetMapping("/patient/pharmacy/active-prescription")
    public ResponseEntity<Page<PreferredPharmacyDto>> getPharmaciesByCondition(
            @Valid @ParameterObject final PrescriptionID prescriptionID,
            @ParameterObject Optional<SysEnum.EPharmacy> ePharmacy, Optional<Integer> page, Optional<Integer> pageSize,
            Optional<Integer> distance) {
        Pageable pageable = PageRequest.of(page.orElse(SysEnum.EPaging.PageNumber.getValue()),
                pageSize.orElse(SysEnum.EPaging.PageSize.getValue()));
        Page<PreferredPharmacyDto> result = pharmacyService.getPharmaciesWithAdditionInfo(prescriptionID, ePharmacy,
                pageable, distance);

        if (result.isEmpty()) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @GetMapping("/patient/preferred-pharmacies")
    public ResponseEntity<Page<PreferredPharmacyDto>> getPreferredPharmacies(
            @Valid @Parameter Optional<SysEnum.ESortType> eSortType, Optional<Integer> page, Optional<Integer> pageSize,
            Optional<Integer> distance, Optional<String> closestToAddress, Optional<String> latitude, Optional<String> longitude) {


        Pageable pageable = PageRequest.of(page.orElse(SysEnum.EPaging.PageNumber.getValue()),
                pageSize.orElse(SysEnum.EPaging.PageSize.getValue()));
        UUID patientId = AuthUtils.getCognitoId();
        Page<PreferredPharmacyDto> result = pharmacyService.getPreferredPharmacies(eSortType, pageable, distance,
                patientId, closestToAddress, latitude, longitude);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PostMapping("/patient/preferred-pharmacy")
    public ResponseEntity<PreferredPharmacy> createPreferredPharmacy(
            @Valid @RequestBody PharmacyIdDto pharmacyIdDto) throws JsonProcessingException {
        PreferredPharmacy result = pharmacyService.createPreferredPharmacy(pharmacyIdDto.getPharmacyId());
        return new ResponseEntity<>(result, HttpStatus.OK);

    }

    @DeleteMapping("/patient/preferred-pharmacy")
    public ResponseEntity<Object> deletePreferredPharmacy() {
        pharmacyService.deletePreferredPharmacy();
        Map<String, String> result = new HashMap<>();
        result.put("massage", "successful!");
        return new ResponseEntity<>(result, HttpStatus.OK);

    }

    @PostMapping(path = "/patient/images", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<String> uploadImages(@RequestParam("file") MultipartFile file) {
        return new ResponseEntity<>(file.getContentType(), HttpStatus.OK);
    }

    @GetMapping(path = "/patient/images")
    public ResponseEntity<URL> getImages(String filePath) {
        return new ResponseEntity<>(s3Service.getPresignedURL(filePath), HttpStatus.OK);
    }

    @GetMapping(path = "/patient/preferred-pharmacy-info")
    public ResponseEntity<PharmacyDetailDto> getPreferredPharmacyInfo() {
        UUID patientID = AuthUtils.getCognitoId();
        var result = pharmacyService.getPreferredPharmacyDetail(patientID);
        if (result.isPresent()) {
            return new ResponseEntity<>(result.get(), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    @GetMapping("/patient/financial")
    public FinancialDto getListMemberAccum() throws ExecutionException, InterruptedException {
        return vbaUserService.getFinancialOverview();
    }


    /*TODO: Refactor code to check exception for copays - benefits, handle code to reuse handle exception in rest template*/

    /** CH-19
     * Retrieve a paginated list of plan benefits based on the specified plan ID, page number, and page size.
     *
     * @param page      Optional parameter for the requested page number (default is 0).
     * @param pageSize  Optional parameter for the number of items per page (default is a system-defined value).
     * @return A ResponseEntity containing a Page of PlanBenefitDto objects or a NOT_FOUND response if no benefits are found.
     * @throws Exception
     */
    @GetMapping("/patient/plan/benefits")
    public ResponseEntity<Page<PlanBenefitDto>> getPlanBenefits(Optional<Integer> page, Optional<Integer> pageSize) throws Exception {
        String auth0ID = AuthUtils.getAuth0Id();
        // String email = auth0Service.getUserEmail(auth0ID);

        // SubscriberMemberDto subscriberID = vbaUserService.getSubscriberMemberInfo(email);
        SubscriberMemberDto subscriberID = auth0Service.getSubscriberMemberID(auth0ID);

        var enrollmentSubscriber = vbaUserService.getEnrollmentSubscriber(subscriberID.getSubscriberId());
        List<PlanBenefitDto> planBenefitList = planBenefitService.getPlanBenefitList(enrollmentSubscriber.getPlanId());

        if (planBenefitList != null) {
            // Calculate pagination parameters
            int pageNumber = page.orElse(SysEnum.EPaging.PageNumber.getValue());
            int pageSizeValue = pageSize.orElse(SysEnum.EPaging.PageSizeFroBenefits.getValue());

            // Ensure pageNumber and pageSizeValue are positive
            if (pageNumber >= 0 && pageSizeValue > 0) {
                int totalItems = planBenefitList.size();
                int totalPages = (int) Math.ceil((double) totalItems / pageSizeValue);

                // Check if the requested page is within valid range
                if (pageNumber < totalPages) {
                    int fromIndex = pageNumber * pageSizeValue;
                    int toIndex = Math.min(fromIndex + pageSizeValue, totalItems);

                    List<PlanBenefitDto> pageItems = planBenefitList.subList(fromIndex, toIndex);

                    // Create a Page object with a 0-based page number
                    Page<PlanBenefitDto> pageResult = new PageImpl<>(pageItems, PageRequest.of(pageNumber, pageSizeValue), totalItems);

                    return new ResponseEntity<>(pageResult, HttpStatus.OK);
                } else {
                    // Requested page is out of range
                    return new ResponseEntity<>(HttpStatus.NOT_FOUND);
                }
            } else {
                // Invalid pageNumber or pageSizeValue
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }
        } else {
            // Handle the case where the planBenefitList is null (e.g., not found)
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    //Old Manual IDcard endpoint
    @GetMapping("/patient/id-card")
    @ResponseStatus(HttpStatus.OK)
    public VBAIDcardDto getIDCard() {
        return vbaUserService.getIDCardInfo();
    }
    //New request for pdf ID cards from MPX
    @GetMapping("/patient/mpx/id-card/{subscriberId}")
    public ResponseEntity<byte[]> getMpxIDCard(@PathVariable String subscriberId) {
        try {
            System.out.println("SUBSCRIBERID FOR MXP CARD::::: "+subscriberId);
            //TODO: remove after andoird review
            if (subscriberId.equals("*********")){
                System.out.println("Returning test PDF for development account!!!!!");

                Resource resource = resourceLoader.getResource("classpath:test-data/test-id-card.pdf");
                byte[] pdfContent = StreamUtils.copyToByteArray(resource.getInputStream());

                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_PDF);
                headers.setContentDisposition(ContentDisposition.builder("attachment").filename("test-id-card.pdf").build());

                return new ResponseEntity<>(pdfContent, headers, HttpStatus.OK);
            }
            String documentId = mpxService.getIDCardImageID(subscriberId);
            byte[] pdfContent = mpxService.getPdfForSubscriber(documentId);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDisposition(ContentDisposition.builder("attachment").filename("id-card.pdf").build());

            return new ResponseEntity<>(pdfContent, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("Error fetching ID card for Subscriber ID {}: {}", subscriberId, e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    //New request for pdf ID cards from MPX
    @GetMapping("/patient/mpx/eob/{subscriberId}")
    public ResponseEntity<byte[]> getMpxEOB(@PathVariable String subscriberId) {
        try {
            System.out.println("SUBSCRIBERID FOR MXP CARD::::: "+subscriberId);
            String documentId = mpxService.getCOBID(subscriberId);
            byte[] pdfContent = mpxService.getPdfForSubscriber(documentId);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDisposition(ContentDisposition.builder("attachment").filename("id-card.pdf").build());

            return new ResponseEntity<>(pdfContent, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("Error fetching ID card for Subscriber ID {}: {}", subscriberId, e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/patient/favorites")
    @ResponseStatus(HttpStatus.CREATED)
    public void createFavorite(@RequestBody @Valid CreateFavoriteRequest request) {
        createFavoriteFactoryMethod.createMethod(request).createOrUpdate(request);
    }

    @GetMapping("/patient/favorites")
    @ResponseStatus(HttpStatus.OK)
    public Object getFavorite(@ParameterObject @Valid GetFavoriteRequest request) {
        if (request.getPageNumber() == null || request.getPageSize() == null) {
            request.setPageSize(10);
            request.setPageNumber(0);
        }
        return getFavoriteFactoryMethod.getMethod(request).getFavorite(request);
    }

    @GetMapping("/patient/prescription/schedulers")
    @ResponseStatus(HttpStatus.OK)
    public GetListMedicineNotificationResponse getPrescriptionScheduler(
            @ParameterObject @Valid PageRequestParams pageRequest) {
        return profileService.getDailyPrescriptionScheduler(pageRequest);
    }

    @PutMapping("/patient/prescription/schedulers")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void switchModeNotificationScheduler(@ParameterObject @Valid UpdateNotificationRequest body) {
        profileService.switchModeNotificationScheduler(body);
    }

    @GetMapping("/patient/reminders")
    public ReminderRes getPatientNotifications(@ParameterObject @Valid PageRequestParams params,
            @Parameter EReminderStatus filter) {
        return notificationService.getPatientNotifications(params, filter);
    }

    @GetMapping("/patient/reminders/{notificationId}")
    public ReminderDto getPatientNotificationDetail(@PathVariable("notificationId") UUID notificationId) {
        return notificationService.getPatientNotificationDetail(notificationId);
    }

    @PutMapping("/patient/reminders")
    public void updatePatientNotifications(@ParameterObject @Valid UpdateNotificationRequest body) {
        profileService.switchModeNotificationScheduler(body);
    }

    @DeleteMapping("/patient/reminders/{notificationId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteNotification(@PathVariable UUID notificationId) {
        notificationService.deletePatientNotification(notificationId);
    }
}