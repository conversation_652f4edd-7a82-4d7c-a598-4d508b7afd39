package com.covet.profile.dto.covet.provider.review;

import com.covet.profile.persistence.model.GroupProvider;
import com.covet.profile.persistence.model.Profile;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.Date;
import java.util.Optional;
import java.util.UUID;

@JsonSerialize(as = ImmutableReviewDto.class)
@JsonDeserialize(as = ImmutableReviewDto.class, builder = ImmutableReviewDto.Builder.class)
@Value.Immutable
public interface ReviewDto {
    @Value.Parameter
    @JsonProperty
    Optional<UUID> getId();

    @Value.Parameter
    @JsonProperty
    Optional<UUID> getPhysicianId();

    @Value.Parameter
    @JsonProperty
    Optional<UUID> getGroupId();

    @Value.Parameter
    @JsonProperty
    UUID getPatientId();

    @Value.Parameter
    @JsonProperty
    Optional<String> getContent();

    @Value.Parameter
    @JsonProperty
    @Min(value = 0, message = "can't rate lower than 0")
    @Max(value = 5, message = "can't rate higher than 5")
    Integer getRating();

    @Value.Parameter
    @JsonProperty
    Date getCreatedDate();

    @Value.Parameter
    @JsonProperty
    int getVisitType();

    @Value.Parameter
    @JsonProperty
    boolean getIsHelpful();

    @Value.Parameter
    @JsonProperty
    int getHelpfulTotal();

    @Value.Parameter
    @JsonProperty
    Optional<Profile> getPatientProfile();

    @Value.Parameter
    @JsonProperty
    Optional<GroupProvider> getGroupProvider();

}
