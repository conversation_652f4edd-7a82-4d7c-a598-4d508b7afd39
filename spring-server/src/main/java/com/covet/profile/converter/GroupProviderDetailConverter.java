package com.covet.profile.converter;

import com.covet.profile.dto.covet.provider.GroupProviderDetailDto;
import com.covet.profile.dto.covet.provider.GroupProviderDto;
import com.covet.profile.dto.covet.provider.ImmutableGroupProviderDetailDto;
import com.covet.profile.persistence.model.Review;
import lombok.NoArgsConstructor;

import java.util.Optional;

@NoArgsConstructor
public class GroupProviderDetailConverter {
    public static GroupProviderDetailDto modelsToDto(GroupProviderDto group, Review review, long totalReviews) {
        return ImmutableGroupProviderDetailDto.of(
                group,
                Optional.ofNullable(review),
                totalReviews
        );
    }
}
