package com.covet.profile.clients.vba.VBAInterfaces;

import com.covet.profile.clients.vba.VBADtos.Financial.FinancialDto;
import com.covet.profile.clients.vba.VBADtos.IDcard.VBAIDcardDto;
import com.covet.profile.clients.vba.VBADtos.Member.VBAMemberDto;
import com.covet.profile.clients.vba.VBADtos.Providers.VBAProviderDto;
import com.covet.profile.clients.vba.VBADtos.Subscriber.EnrollmentSubscriberDto;
import com.covet.profile.clients.vba.VBADtos.Member.SubscriberMemberDto;

import java.util.List;
import java.util.concurrent.ExecutionException;

public interface IVBAUserService {
    SubscriberMemberDto getSubscriberMemberInfo(String email);

    FinancialDto getFinancialOverview() throws ExecutionException, InterruptedException;

    EnrollmentSubscriberDto getEnrollmentSubscriber(String subscriberId);

    List<VBAMemberDto> geMemberListBySubscriberId(String subscriberID);

    VBAIDcardDto getIDCardInfo();

    List<VBAProviderDto> fetchVbaProviderList(List<String> providerIdList);

    boolean checkForPrescriptionSearch(String groupId);
}
