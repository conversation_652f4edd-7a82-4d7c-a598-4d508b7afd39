package com.covet.profile.clients.vba.VBARepository;

import com.covet.profile.clients.vba.VBAModels.Claim.VBAClaim;
import com.covet.profile.clients.vba.VBARowMapper.VBAClaimRowMapper;
import com.covet.profile.persistence.model.Claim;
import com.covet.profile.persistence.model.Physician;
import com.covet.profile.persistence.model.Profile;
import com.covet.profile.persistence.repository.PhysicianRepository;
import com.covet.profile.persistence.repository.ProfileRepository;

import org.springframework.beans.factory.annotation.*;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Repository
public class VBAClaimRepository {
    private final JdbcTemplate jdbcTemplate;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private ProfileRepository profileRepository;
    private PhysicianRepository physicianRepository;
    private SimpleDateFormat formatter = new SimpleDateFormat("yyyy-M-dd hh:mm:ss");

    public VBAClaimRepository(@Qualifier("vbaDataSource") DataSource dataSource, ProfileRepository profileRepository,
            PhysicianRepository physicianRepository) {
        jdbcTemplate = new JdbcTemplate(dataSource);
        namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSource);
        this.profileRepository = profileRepository;
        this.physicianRepository = physicianRepository;
    }

    private Claim mappingClaimCovet(ResultSet rs) throws SQLException {
        String claimCode = Integer.toString(rs.getInt("Claim_Number"));
        UUID patientId = UUID.nameUUIDFromBytes(rs.getString("Subscriber_ID").getBytes());
        UUID physicianId = UUID.nameUUIDFromBytes(rs.getString("Provider_ID").getBytes());
        Boolean isPaid = rs.getString("Claim_Funded_Status").equals("HOLD");
        Date createdDate = new Date();
        try {
            createdDate = formatter.parse(rs.getString("Service_Date"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        HashMap<String, Integer> claimTypes = new HashMap<>();
        claimTypes.put("DENTAL", 1); // Dental
        claimTypes.put("FLEX", 2); // Flex
        claimTypes.put("LIFE", 2); // Life
        claimTypes.put("PROF", 2); // Professional
        claimTypes.put("RX", 0); // Pharmacy
        claimTypes.put("STD", 2); // Disability
        claimTypes.put("UB92", 3); // Institutional
        Double totalClaim = rs.getDouble("Billed_Price");
        int claimType = claimTypes.get(rs.getString("Claim_Type"));
        // get entity
        Profile patient = profileRepository.getReferenceById(patientId);
        Physician physician = physicianRepository.getReferenceById(physicianId);
        return new Claim(claimCode,
                patientId,
                physicianId,
                isPaid,
                createdDate,
                totalClaim,
                claimType,
                patient,
                physician);
    }

    public List<Claim> findRecentClaims() {
        List<String> columns = new ArrayList<>();
        columns.add("cd.Claim_Number");
        columns.add("Subscriber_ID");
        columns.add("Provider_ID");
        columns.add("Claim_Funded_Status");
        columns.add("cd.Service_Date");
        columns.add("Billed_Price");
        columns.add("Claim_Type");
        String selectedColumns = String.join(", ", columns);
        String sql = "SELECT " + selectedColumns + " FROM Claim c\n" +
                "INNER JOIN ClaimDetail cd ON c.Claim_Number = cd.Claim_Number\n" +
                "WHERE Received_Date >= DATEADD(day, -90, CONVERT(DATE, GETDATE()))";
        return jdbcTemplate.query(sql,
                (rs, rowNum) -> mappingClaimCovet(rs));
    }

    public List<VBAClaim> getClaimsBySubscriber(String subscriberID, Optional<String> memberSeq) {
        List<String> columns = new ArrayList<>();
        List<Object> params = new ArrayList<>();
        params.add(subscriberID);

        columns.add("Claim_Number");
        columns.add("Subscriber_ID");
        columns.add("Member_Seq");
        columns.add("Provider_ID");
        columns.add("Claim_Type");

        String selectedColumns = String.join(", ", columns);

        String sql = "SELECT " + selectedColumns + " FROM Claim " +
                     "WHERE Subscriber_ID = ?";
        if (memberSeq.isPresent()) {
            sql += " AND Member_Seq = ?";
            params.add(memberSeq.get());
        }

        return jdbcTemplate.query(sql, new VBAClaimRowMapper(), params.toArray());
    }

    public List<VBAClaim> getClaimsBySubscriberAndMemberSequences(String subscriberID, List<String> memberSeq) {
        List<String> columns = new ArrayList<>();
        MapSqlParameterSource parameters = new MapSqlParameterSource();
        parameters.addValue("subscriberID", subscriberID);
        parameters.addValue("memberSeqs", memberSeq);

        columns.add("Claim_Number");
        columns.add("Subscriber_ID");
        columns.add("Member_Seq");
        columns.add("Provider_ID");
        columns.add("Claim_Type");

        String selectedColumns = String.join(", ", columns);

        String sql = "SELECT " + selectedColumns + " FROM Claim " +
                     "WHERE Subscriber_ID = :subscriberID and Member_Seq in (:memberSeqs)";

        return namedParameterJdbcTemplate.query(sql, parameters, new VBAClaimRowMapper());
    }
}
