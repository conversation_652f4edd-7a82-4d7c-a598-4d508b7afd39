package com.covet.profile.persistence.repository;

import com.covet.profile.persistence.model.Pharmacy;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface PharmacyRepository
        extends JpaRepository<Pharmacy, UUID>, JpaSpecificationExecutor<Pharmacy> {

    Optional<Pharmacy> findPharmacyByPharmacyId(UUID pharmacyId);

    @Query(value = "select p.* from pharmacy p " +
            "join sale_prescription sp on sp.pharmacy_id = p.pharmacy_id " +
            "where sp.prescription_name = :prescriptionName " +
            "and sp.prescription_code = :prescriptionCode ", nativeQuery = true)
    List<Pharmacy> findPharmaciesByPrescriptionNameAndCode(@Param("prescriptionName") String prescriptionName, @Param("prescriptionCode") String prescriptionCode);

    Page<Pharmacy> findAll(Pageable pageable);

}
