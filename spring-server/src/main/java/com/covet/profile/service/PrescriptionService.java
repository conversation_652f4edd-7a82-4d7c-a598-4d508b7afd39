package com.covet.profile.service;

import com.covet.profile.dto.covet.prescription.ActivePrescriptionsDto;
import com.covet.profile.dto.covet.prescription.ImmutablePrescriptionDetailDto;
import com.covet.profile.dto.covet.prescription.PrescriptionDetailDto;
import com.covet.profile.dto.covet.prescription.PrescriptionDto;
import com.covet.profile.persistence.compositeKey.ClaimPrescriptionID;
import com.covet.profile.persistence.compositeKey.PrescriptionID;
import com.covet.profile.persistence.compositeKey.PrescriptionPharmacyID;
import com.covet.profile.converter.PharmacyConverter;
import com.covet.profile.converter.PrescriptionConverter;
import com.covet.profile.dto.CapRxDto.pharmacy.CapPharmacyDto;
import com.covet.profile.exception.ResourceNotFoundException;
import com.covet.profile.persistence.model.*;
import com.covet.profile.persistence.repository.*;
import com.covet.profile.responses.ResponseHandler;
import com.covet.profile.persistence.specification.PrescriptionSpec;
import com.covet.profile.persistence.specification.PriorAuthorSpec;
import com.covet.profile.systemEnum.EDefaultForm;
import com.covet.profile.utils.AuthUtils;
import com.covet.profile.utils.ImageUtils;
import com.covet.profile.utils.ObjectToUuidUtils;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.persistence.EntityNotFoundException;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
@RequiredArgsConstructor
public class PrescriptionService {

    private final PrescriptionRepository prescriptionRepository;
    private final PriorAuthorRepository priorAuthorRepository;
    private final ClaimPrescriptionRepository claimPrescriptionRepository;
    private final PrescriptionPharmacyRepository prescriptionPharmacyRepository;
    private final PictureRepository pictureRepository;
    private final PhysicianRepository physicianRepository;
    private final S3Service s3Service;
    private final CapitalRxService capitalRxService;

    public static Map<String, URL> defaultImageMap = new HashMap<>();

    public Prescription createOrUpdatePrescription(PrescriptionDto prescriptionDto)
            throws EntityNotFoundException {
        Prescription prescription = PrescriptionConverter.dtoToPrescription(prescriptionDto);
        return prescriptionRepository.save(prescription);
    }

    public Optional<PrescriptionDto> getPrescriptionById(PrescriptionID prescriptionId) {
            return prescriptionRepository.findByPrescriptionId(prescriptionId)
                    .map(PrescriptionConverter::prescriptionToDto);
    }

    @PostConstruct
    private void getDefaultImage() {
        defaultImageMap = getDefaultUrlMap();
    }

    /**
     * @param physicianId
     * @return avatarUrl from DB image if not expired; otherwise, return S3 presignedUrl
     */
    private URL getAvatarUrl(@NonNull UUID physicianId) {
        URL avatarUrl = ImageUtils.getImageUrl(s3Service.getExpiredDays(), pictureRepository.findById(physicianId));
        if (avatarUrl == null) {
            avatarUrl = s3Service.getPresignedURL(physicianId.toString());
        }
        return avatarUrl;
    }

    @Scheduled(cron = "0 0 0 */5 * *")
    public void refreshDefaultMedicineImage() {
        getDefaultUrlMap();
    }

    private Map<String, URL> getDefaultUrlMap() {
        var executor = Executors.newFixedThreadPool(5);
        var fields = Arrays.stream(EDefaultForm.values()).map(EDefaultForm::getValue).toList();
        var defaultUrlFutures = fields.stream().map(
                field -> CompletableFuture.supplyAsync(() -> s3Service.getDefaultPresignedURL(field + ".png"), executor)
        ).toList();
        CompletableFuture<Void> allOf = CompletableFuture.allOf(defaultUrlFutures.toArray(new CompletableFuture[0]));
        allOf.join();

        Map<String, URL> defaultUrlMap = new HashMap<>();
        for (int i = 0; i < fields.size(); i++) {
            try {
                defaultUrlMap.put(fields.get(i), defaultUrlFutures.get(i).get());
            } catch (InterruptedException | ExecutionException e) {
                log.info("get default url fail with error {}", e.getMessage());
                Thread.currentThread().interrupt();
            }
        }
        return defaultUrlMap;
    }

    public URL getImgByNdc(String npc) {
        String ndcPattern = "(\\d{5})(\\d{4})(\\d{2})";
        String fileExtension = ".jpg";
        Pattern regex = Pattern.compile(ndcPattern);
        Matcher matcher = regex.matcher(npc);
        if (matcher.matches()) {
            String formattedString = matcher.group(1) + "-" + matcher.group(2) + "-" + matcher.group(3) + fileExtension;
            return s3Service.getPresignedURL(formattedString);
        }
        return null;
    }
    public Page<ActivePrescriptionsDto> getActivePrescriptions(Pageable pageable, Optional<String> keyword) {
        UUID patientID = AuthUtils.getCognitoId();

        Page<Prescription> prescriptions = prescriptionRepository
                .findAll(PrescriptionSpec.prescriptionByConditions(keyword, patientID), pageable);

        List<ActivePrescriptionsDto> result = new ArrayList<>();

        if (!prescriptions.isEmpty()) {

            List<PrescriptionID> prescriptionIds = prescriptions.stream()
                    .map(prescription -> new PrescriptionID(
                            prescription.getPrescriptionId().getPrescriptionName(),
                            prescription.getPrescriptionId().getPrescriptionCode()))
                    .toList();

            List<CompletableFuture<Map<UUID, URL>>> getDrugImageUrlFutures = prescriptionIds.stream().map(
                    item -> CompletableFuture.supplyAsync(() -> {
                        Map<UUID, URL> imgUrl = new HashMap<>();
                        imgUrl.put(ObjectToUuidUtils.hashToUUID(item), getImgByNdc(item.getPrescriptionCode()));
                        return imgUrl;

                    })).toList();
            CompletableFuture<Void> allOf = CompletableFuture.allOf(getDrugImageUrlFutures.toArray(new CompletableFuture[0]));
            allOf.join();

            List<Prescription> activePrescriptions = prescriptions.stream().toList();
            List<String> prescriptionNames = prescriptionIds.stream()
                    .map(PrescriptionID::getPrescriptionName)
                    .toList();

            Optional<List<UUID>> prescriptionPhysicians = Optional.of(activePrescriptions.stream()
                    .map(Prescription::getPhysicianID)
                    .toList());


            Optional<List<PrescriptionPharmacy>> prescriptionPharmacyList = Optional.of(
                    prescriptionPharmacyRepository.findPrescriptionPharmacies(prescriptionNames, patientID));

            Optional<List<Physician>> physicians = Optional.of(physicianRepository.findByPhysicianIds(prescriptionPhysicians.get()));

            activePrescriptions.forEach(prescription -> {
                String prescriptionName = prescription.getPrescriptionId().getPrescriptionName();
                String prescriptionCode = prescription.getPrescriptionId().getPrescriptionCode();

                PrescriptionID prescriptionID = new PrescriptionID(prescriptionName, prescriptionCode);

                long numOfRefills = getNumOfRefills(prescriptionID);

                Optional<PrescriptionPharmacy> prescriptionPharmacy = prescriptionPharmacyList.get().stream()
                        .filter(x -> x.getId().getPrescriptionName().equals(prescriptionName) &&
                                x.getId().getPrescriptionCode().equals(prescriptionCode) && x.getId().getPatientID().equals(patientID))
                        .findFirst();

                Physician physician = physicians.flatMap(physicianList -> physicianList.stream()
                                .filter(x -> x.getPhysicianId() == prescription.getPhysicianID())
                                .findFirst())
                        .orElse(null);

                var form = prescription.getForm().toLowerCase();
                var targetForm = EDefaultForm.OTHERS.name();
                for (var item : EDefaultForm.values()) {
                    if (form.contains(item.name().toLowerCase())) {
                        targetForm = item.name();
                        break;
                    }
                }
                var imgUrl = Optional.ofNullable(getDefaultUrlMap().get(targetForm))
                        .orElse(getDefaultUrlMap().get(EDefaultForm.OTHERS.name()));
                if (prescriptionPharmacy.isPresent()) {
                    CapPharmacyDto pharmacy = capitalRxService.getPharmacyById(prescriptionPharmacy.get().getId().getPharmacyId());
                    String physicianName = physician != null ? String.join(" ", physician.getProfile().getFirstName(),
                            physician.getProfile().getLastName()) : "";

                    ActivePrescriptionsDto item = PrescriptionConverter.toActivePrescriptionsDto(
                            prescription,
                            imgUrl,
                            numOfRefills, physicianName, PharmacyConverter.pharmacyToDto(pharmacy),
                            prescriptionPharmacy.get().getCost());
                    result.add(item);
                } else {
                    ActivePrescriptionsDto item = PrescriptionConverter.toActivePrescriptionsDto(
                            prescription,
                            imgUrl,
                            numOfRefills, "", null, 0.0);
                    result.add(item);
                }
            });
        }

        return new PageImpl<>(result, pageable, prescriptions.getTotalElements());
    }

    public long getNumOfRefills(PrescriptionID prescriptionId) {
        return priorAuthorRepository
                .count(PriorAuthorSpec.findByRequestMedicineWithPending(
                        prescriptionId.getPrescriptionName()));
    }

    public Map<String, Object> getPrescriptionClaimDetail(String claimCode,
            String prescriptionName, String prescriptionCode, UUID patientId) {
        ClaimPrescriptionID claimPrescriptionId = new ClaimPrescriptionID(prescriptionName, prescriptionCode,
                claimCode, patientId);
        Optional<ClaimPrescription> claimPrescription = claimPrescriptionRepository
                .findById(claimPrescriptionId);
        if (!claimPrescription.isPresent()) {
            throw new ResourceNotFoundException("Prescription claim not found " + claimPrescriptionId);
        }
        PrescriptionID prescriptionId = new PrescriptionID(prescriptionName,
                prescriptionCode);
        Prescription prescription = claimPrescription.get().getPrescription();
        UUID convertedUUID = ObjectToUuidUtils.hashToUUID(prescription.getPrescriptionId());
        Claim claim = claimPrescription.get().getClaim();
        Pharmacy pharmacy = claimPrescription.get().getPharmacy();
        PrescriptionPharmacyID prescriptionPharmacyId = new PrescriptionPharmacyID(prescriptionId.getPrescriptionName(),
                prescriptionId.getPrescriptionCode(), pharmacy.getPharmacyId());
        Optional<PrescriptionPharmacy> salePrescription = prescriptionPharmacyRepository.findById(prescriptionPharmacyId);
        if (!salePrescription.isPresent()) {
            throw new ResourceNotFoundException(
                    "The pharmacy dose not sale the prescription" + prescriptionPharmacyId);
        }
        long numOfRefills = getNumOfRefills(prescriptionId);
        Physician physician = claim.getPhysician();
        String physicianName = String.join(" ", physician.getProfile().getFirstName(),
                physician.getProfile().getLastName());
        Date initRefillDate = claimPrescription.get().getInitRefillDate();
        Date nextRefillDate = claimPrescription.get().getNextRefillDate();
        return ResponseHandler.generatePrescriptionResponse(
                prescription,
                getAvatarUrl(convertedUUID),
                claim.getClaimCode(),
                numOfRefills,
                physicianName,
                pharmacy.getPharmacyName(),
                salePrescription.get().getCost(),
                initRefillDate,
                nextRefillDate);
    }

    //CH-180: no need refactor
    public PrescriptionDetailDto getPrescriptionDetail(String prescriptionName, String prescriptionCode, UUID patientId)
            throws ResourceNotFoundException {

        Prescription prescription = prescriptionRepository.findPrescriptionByPatientID(prescriptionName,
                        prescriptionCode, patientId)
                .orElseThrow(() -> new ResourceNotFoundException("No prescriptions found for prescription name "
                        + prescriptionName + " and code " + prescriptionCode));


        //Will be updated with VBA stories in project plan
//		List<ClaimPrescription> claimPrescriptions = claimPrescriptionRepository.findAll(ClaimPrescriptionSpec
//				.specificPrescriptionClaimsByPatientId(patientId, prescriptionName, prescriptionCode));
//		Boolean isVerified = claimPrescriptions.stream().anyMatch(item -> priorAuthorRepository
//				.exists(PriorAuthorSpec.claimStatus(item.getClaim().getClaimCode(), "approved")));
//		Boolean isPARequired = claimPrescriptions.stream().anyMatch(item -> priorAuthorRepository
//				.exists(PriorAuthorSpec.claimStatus(item.getClaim().getClaimCode(), "pending")));

        Boolean isVerified = true; //Will be updated with VBA stories in project plan
        Boolean isPARequired = true; //Will be updated with VBA stories in project plan

        Boolean isNoPrescription = !isVerified && !isPARequired;

        URL prescriptionImgUrl = getImgByNdc(prescriptionCode);
        if (prescriptionImgUrl == null) {
            prescriptionImgUrl = Optional.ofNullable(defaultImageMap.get(prescription.getForm()))
                    .orElse(defaultImageMap.get(EDefaultForm.OTHERS.toString()));
        }

        return ImmutablePrescriptionDetailDto.of(
                prescription,
                isVerified,
                isPARequired,
                isNoPrescription,
                Optional.ofNullable(prescriptionImgUrl));
    }

    public URL postImageForPrescription(@NonNull MultipartFile file, @NonNull PrescriptionID prescriptionId)
            throws EntityNotFoundException {
        UUID convertId = ObjectToUuidUtils.hashToUUID(prescriptionId);
        String imageUUID = convertId.toString();
        s3Service.deleteFile(imageUUID);
        s3Service.putFile(file, imageUUID);
        URL imageUrl = s3Service.getPresignedURL(imageUUID);
        pictureRepository.save(ImageUtils.getSavedImage(pictureRepository.findById(convertId), convertId, imageUrl));
        return imageUrl;
    }

    @Transactional
    public void deleteActivePrescription(PrescriptionID prescriptionID) {
        prescriptionRepository.deleteById(prescriptionID);
    }
}
