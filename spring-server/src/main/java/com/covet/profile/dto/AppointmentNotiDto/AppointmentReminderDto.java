package com.covet.profile.dto.AppointmentNotiDto;

import com.covet.profile.dto.covet.appointment.AppointmentDto;
import com.covet.profile.dto.reminder.BaseCreateReminderDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class AppointmentReminderDto extends BaseCreateReminderDto {
    AppointmentDto appointment;
}
