package com.covet.profile.converter;

import com.covet.profile.clients.vba.VBADtos.Member.ImmutableVBAMemberDto;
import com.covet.profile.clients.vba.VBADtos.Member.VBAMemberDto;
import com.covet.profile.clients.vba.VBADtos.Subscriber.EnrollmentSubscriberDto;
import com.covet.profile.clients.vba.VBADtos.Subscriber.ImmutableEnrollmentSubscriberDto;
import com.covet.profile.clients.vba.VBAModels.EnrollmentSubscriber;
import com.covet.profile.clients.vba.VBAModels.VBAMember;

public class SubscriberConverter {
    private SubscriberConverter() {}

    public static EnrollmentSubscriberDto toEnrollmentSubscriberDto(EnrollmentSubscriber subscriber) {
        return ImmutableEnrollmentSubscriberDto.of(
                subscriber.getSubscriberId(),
                subscriber.getPlanId(),
                subscriber.getGroupId(),
                subscriber.getPlanStart(),
                subscriber.getPlanEnd()
        );
    }

    public static VBAMemberDto toMemberDto(VBAMember member) {
        return ImmutableVBAMemberDto.of(
                member.getMemberSeq(),
                member.getFirstName(),
                member.getMiddleName(),
                member.getLastName(),
                member.getRelationship(),
                member.getBirthDate()
        );
    }
}
