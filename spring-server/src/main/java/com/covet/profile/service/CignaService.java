package com.covet.profile.service;

import com.covet.profile.clients.cigna.CignaClient;
import com.covet.profile.clients.cigna.request.GetLocationRequest;
import com.covet.profile.clients.cigna.response.location.LocationResponse;
import com.covet.profile.clients.cigna.response.organization.OrganizationResponse;
import com.covet.profile.clients.npi.response.NpiProvider;
import com.covet.profile.converter.cigna.PractitionerConverter;
import com.covet.profile.dto.NpiDto.TaxonomyInfo;
import com.covet.profile.dto.covet.provider.PhysicianDetailDto;
import com.covet.profile.dto.enums.EInsertStatus;
import com.covet.profile.persistence.model.CovetInsertStatus;
import com.covet.profile.persistence.model.NpiAddressEntity;
import com.covet.profile.persistence.repository.CovetInsertStatusRepository;
import com.covet.profile.persistence.repository.NpiAddressEntityRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class CignaService {
    private final CignaClient cignaClient;
    private final NpiService npiService;
    private final PractitionerConverter practitionerConverter;
    private final S3Service s3Service;
    private final NpiAddressEntityRepository npiAddressEntityRepository;
    private final CovetInsertStatusRepository covetInsertStatusRepository;

    public List<PhysicianDetailDto> getCignaProvider(String providerName) {
        var response = cignaClient.getPractitioner(providerName);

        List<String> npiList = new LinkedList<>();
        response.getEntry().forEach(entry -> {
            var resource = entry.getResource();
            var identifier = resource.getIdentifier();
            var cignaNpiInfo = identifier.stream()
                    .filter(item -> Objects.nonNull(item.getType())
                            && !CollectionUtils.isEmpty(item.getType().getCoding())
                            && item.getType().getCoding().stream().anyMatch(coding -> coding.getCode().equals("NPI"))
                    )
                    .findFirst();
            cignaNpiInfo.ifPresent(value -> npiList.add(value.getValue()));
        });

        Map<String, NpiProvider> npiProviders = npiService.getNpiInfoList(npiList).stream()
                .collect(Collectors.toMap(NpiProvider::getNumber, Function.identity()));

        return response.getEntry().stream().map(entry -> practitionerConverter.convertToPhysicianDetailDto(entry.getResource(), npiProviders))
                .toList();
    }

    public LocationResponse getLocationTest() {
        var req = new GetLocationRequest();
        req.setAddressState("OH");
        return cignaClient.getLocation(null, "OH", null, null, null, null);
    }

    public OrganizationResponse getOrganization() {
        return cignaClient.getOrganization(null, null, null, null);
    }

    public void readFile() {
        var file_nums = new int[] {117, 135, 139, 145, 147, 155, 156, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 350, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426};
        for (var idx: file_nums) {
            var key = String.format("cigna_providers/cigna_provider_%s.csv", idx);
            var insertStatus = new CovetInsertStatus();
            insertStatus.setId(UUID.randomUUID());
            insertStatus.setKeyName(key);
            var currentChunk = 0;
            try (InputStream inputStream = s3Service.getFile(key);
                 BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                var lines = reader.lines().toList();
                // skip line 0 is header
                var npiList = new LinkedList<String>();
                for (var i = 1; i < lines.size(); i++) {
                    var values = lines.get(i).split(",");
                    var npi = values[1];
                    // fetch npi data
                    if (npiList.size() < 10) {
                        npiList.add(npi);
                    } else {
                        handleNpiData(npiList, key);
                        currentChunk += 1;
                    }
                }
                if (!npiList.isEmpty()) {
                    handleNpiData(npiList, key);
                    currentChunk += 1;
                }
                currentChunk = 0;
                insertStatus.setStatus(EInsertStatus.SUCCESS.name());
            } catch (Exception e) {
                insertStatus.setStatus(EInsertStatus.FAILED.name());
                insertStatus.setCurrentChunk(currentChunk);
            }
            covetInsertStatusRepository.save(insertStatus);

            try {
                Thread.sleep(1000);
            } catch (Exception e) {
                // handle error
            }
        }
    }

    public void handleNpiData(List<String> npiList, String key) {
        if (CollectionUtils.isEmpty(npiList)) {
            return;
        }
        List<NpiAddressEntity> npiAddressEntities = new LinkedList<>();
        // fetch data from npi service
        try {
            npiService.getNpiInfoList(npiList)
                    .stream()
                    .filter(Objects::nonNull)
                    .forEach(npiProvider -> {
                        var address = npiService.buildNpiAddressTemplate(npiProvider);
                        var taxonomyInfo = Optional.ofNullable(npiService.findTaxonomyInfo(npiProvider.getTaxonomies()))
                                .orElse(new TaxonomyInfo());
                        var npiAddressEntity = new NpiAddressEntity();
                        npiAddressEntity.setNpi(npiProvider.getNumber());
                        npiAddressEntity.setIdentifier(UUID.randomUUID());
                        npiAddressEntity.setAddress(address);
                        npiAddressEntity.setSpecialtyCode(taxonomyInfo.getCode());
                        npiAddressEntity.setTaxonomyGroup(taxonomyInfo.getTaxonomyGroup());
                        npiAddressEntity.setSpecialtyDesc(taxonomyInfo.getDesc());
                        npiAddressEntity.setSpecialtyLicense(taxonomyInfo.getLicense());
                        npiAddressEntity.setState(taxonomyInfo.getState());

                        npiAddressEntities.add(npiAddressEntity);
                    });
            // store to covet DB
            storeNpiAddressData(npiAddressEntities);
        } catch (Exception e) {
            log.info("fetch key {} error {}", key, e.getCause().getMessage());
        } finally {
            npiList.clear();
        }
    }

    public void storeNpiAddressData(List<NpiAddressEntity> npiAddressEntities) {
        if (CollectionUtils.isEmpty(npiAddressEntities)) {
            return;
        }
        npiAddressEntityRepository.saveAll(npiAddressEntities);
    }
}
