package com.covet.profile.converter;

import com.covet.profile.dto.CapRxDto.pharmacy.CapPharmacyDto;
import com.covet.profile.dto.covet.pharmacy.*;
import com.covet.profile.persistence.model.Pharmacy;
import com.covet.profile.utils.RoundingUtils;

import java.net.URL;
import java.util.Optional;

public class PharmacyConverter {
    private PharmacyConverter() {
    }

    public static PharmacyDto pharmacyToDto(CapPharmacyDto pharmacy) {
        return ImmutablePharmacyDto.of(
                Optional.ofNullable(pharmacy.getNpi()), pharmacy.getLatitude(), pharmacy.getLongitude(),
                pharmacy.getName(),
                pharmacy.getAddressLine1(), pharmacy.getPhone());
    }

    public static Pharmacy dtoToPharmacy(PharmacyDto pharmacyDto) {
        return new Pharmacy(
                pharmacyDto.getPharmacyId().orElse(null), pharmacyDto.getLatitude(), pharmacyDto.getLongitude(),
                pharmacyDto.getPharmacyName(),
                pharmacyDto.getAddress(), pharmacyDto.getPhoneNumber());
    }

    public static PreferredPharmacyDto toPreferredPharmacyDto(CapPharmacyDto pharmacy, URL imgURL, Boolean isPreferred, Double distance, Double cost) {
        return ImmutablePreferredPharmacyDto.of(
                pharmacy,
                Optional.ofNullable(isPreferred),
                distance,
                Optional.ofNullable(imgURL),
                RoundingUtils.roundDoubleValue(cost)
        );
    }

    public static PharmacyDetailDto pharmacyDetailToDto(CapPharmacyDto pharmacy, Double cost, URL imageURL, Double distance) {
        return ImmutablePharmacyDetailDto.of(
                pharmacy,
                RoundingUtils.roundDoubleValue(cost),
                Optional.ofNullable(imageURL),
                distance
        );
    }
}
