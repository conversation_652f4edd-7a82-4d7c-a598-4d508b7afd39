package com.covet.profile.clients.cigna.response.location;

import com.covet.profile.clients.cigna.response.common.Coding;
import com.covet.profile.clients.cigna.response.common.BaseExtension;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class LocationExtension extends BaseExtension {
    private Map<String, List<Coding>> valueCodeableConcept = new HashMap<>();
}
