BEGIN;

--Remove OR UPDATE
CREATE
    OR REPLACE TRIGGER init_physician_rating
    AFTER
        INSERT
        OR DELETE ON physician FOR EACH ROW EXECUTE FUNCTION init_physician_rating();


create table temp_provider (
                               Provider_ID varchar,
                               Last_Name varchar,
                               First_Name varchar,
                               Address varchar
);

INSERT INTO temp_provider (Provider_ID, Last_Name, First_Name, Address) VALUES (N'**********', N'YUZ', N'MICHAEL', N'116 E 36th St');
INSERT INTO temp_provider (Provider_ID, Last_Name, First_Name, Address) VALUES (N'**********', N'WALLIS', N'CARLY', N'1000 PARK AVE');
INSERT INTO temp_provider (Provider_ID, Last_Name, First_Name, Address) VALUES (N'**********', N'WONG', N'TINA', N'138 MOTT ST');
INSERT INTO temp_provider (Provider_ID, Last_Name, First_Name, Address) VALUES (N'**********', N'BASKHRON', N'MINA', N'314 SEAVIEW AVE');
INSERT INTO temp_provider (Provider_ID, Last_Name, First_Name, Address) VALUES (N'**********', N'AREFI', N'MAHBOD', N'1958 FINSBURY CT');
INSERT INTO temp_provider (Provider_ID, Last_Name, First_Name, Address) VALUES (N'**********', N'KUVSHINIKOV', N'BRIAN', N'15 SOUTHMOOR CIR NE');
INSERT INTO temp_provider (Provider_ID, Last_Name, First_Name, Address) VALUES (N'**********', N'MORISETTY', N'SATYASAGAR', N'401 MARKET ST STE 601');
INSERT INTO temp_provider (Provider_ID, Last_Name, First_Name, Address) VALUES (N'**********', N'Capital Rx', N'Capital Rx', null);


update physician
set npi = provider.Provider_ID
    from (
    select *
    from physician ph
    join profile p on ph.physician_id = p.cognito_id
    join temp_provider tp
        on (p.first_name = tp.First_Name)
               and (p.last_name = tp.Last_Name)
               and (p.address = tp.Address)
    ) provider;

drop table temp_provider;

COMMIT;