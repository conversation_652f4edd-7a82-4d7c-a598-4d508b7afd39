package com.covet.profile.config;

import com.covet.profile.utils.SecretManagerUtils;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.OAuthFlow;
import io.swagger.v3.oas.annotations.security.OAuthFlows;
import io.swagger.v3.oas.annotations.security.SecurityScheme;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.security.oauth2.server.resource.authentication.JwtGrantedAuthoritiesConverter;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

@EnableWebSecurity
@EnableGlobalMethodSecurity(securedEnabled = true, prePostEnabled = true)
@SecurityScheme(type = SecuritySchemeType.OAUTH2, name = "oauth2", in = SecuritySchemeIn.HEADER,
    flows = @OAuthFlows(authorizationCode =
        @OAuthFlow(authorizationUrl = "${swagger.authorization-url}",
                   tokenUrl = "${swagger.token-url}")))
public class SecurityConfig {
    @Value("${spring.third-party.zendesk.secret-name-webhook-appointment}")
    private String webhookSecretName;

    @Value("${spring.third-party.security.pre-registration-secret-name}")
    private String preRegistrationSecretName;

    private final SecretManagerUtils secretManagerUtil;

    public SecurityConfig(SecretManagerUtils secretManagerUtil) {
        this.secretManagerUtil = secretManagerUtil;
    }

    private static final String[] WHITELIST = {
        "/covet-api/**",
        "/covet/v3/api-docs/**",
        "/covet/swagger-ui/**",
        "/covet/swagger-ui.html",
        "/actuator/health",
        "/api/profile/check",
        "/api/profile/provider/check",
        "/api/profile/create-user",
        "/api/zendesk/webhook-appointment",
        "/images/**"
    };

    private JwtAuthenticationConverter jwtAuthenticationConverter() {
        var jwtGrantedAuthoritiesConverter = new JwtGrantedAuthoritiesConverter();
        jwtGrantedAuthoritiesConverter.setAuthoritiesClaimName("permissions"); // Update to Auth0 permissions claim
        jwtGrantedAuthoritiesConverter.setAuthorityPrefix(""); // No prefix is needed

        var jwtAuthenticationConverter = new JwtAuthenticationConverter();
        jwtAuthenticationConverter.setJwtGrantedAuthoritiesConverter(jwtGrantedAuthoritiesConverter);
        return jwtAuthenticationConverter;
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http.cors().and().csrf().disable()
            .authorizeRequests(expressionInterceptUrlRegistry -> 
                expressionInterceptUrlRegistry
                    .antMatchers(WHITELIST).permitAll()
                    .anyRequest().authenticated()
            )
            .oauth2ResourceServer().jwt()
            .jwtAuthenticationConverter(jwtAuthenticationConverter());

        http.addFilterBefore(apiKeyAuthFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(zendeskVerificationFilter(), UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.addAllowedOrigin("http://localhost:3000");
        configuration.addAllowedOrigin("https://portal.covethealth.io");
        configuration.addAllowedMethod("*");
        configuration.addAllowedHeader("*");
        configuration.setAllowCredentials(true);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    @Bean
    public ZendeskVerificationFilter zendeskVerificationFilter() throws Exception {
        return new ZendeskVerificationFilter(secretManagerUtil, webhookSecretName);
    }

    @Bean
    public ApiKeyAuthFilter apiKeyAuthFilter() throws Exception {
        return new ApiKeyAuthFilter(secretManagerUtil, preRegistrationSecretName);
    }
}

