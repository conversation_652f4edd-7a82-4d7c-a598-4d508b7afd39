package com.covet.profile.clients.vba.VBADtos.Plans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.math.BigDecimal;

@JsonSerialize(as = ImmutableVBAPlaceCoPayItemDto.class)
@JsonDeserialize(as = ImmutableVBAPlaceCoPayItemDto.class, builder = ImmutableVBAPlaceCoPayItemDto.Builder.class)
@Value.Immutable

public interface VBAPlaceCoPayItemDto {
    @Value.Parameter
    @JsonProperty
    @JsonIgnore
    String getPlanID();

    @Value.Parameter
    @JsonProperty
    @JsonIgnore
    String getBenefitCode();

    @Value.Parameter
    @JsonProperty
    String getPlaceCode();

    @Value.Parameter
    @JsonProperty
    BigDecimal getCopayAmount();

    @Value.Parameter
    @JsonProperty
    BigDecimal getCopayAmountNet();

    @Value.Parameter
    @JsonProperty
    BigDecimal getCoInsurance();

    @Value.Parameter
    @JsonProperty
    BigDecimal getCoInsuranceNet();
}
