package com.covet.profile.clients;

import com.covet.profile.config.FeignClient.ZendeskClientConfig;
import com.fasterxml.jackson.databind.node.ObjectNode;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "zendeskClient",
        url = "https://covethealth.zendesk.com",
        configuration = ZendeskClientConfig.class)
public interface ZendeskClient {
    @PostMapping("/api/v2/tickets")
    Response createTicket(@RequestBody ObjectNode ticket);
}
