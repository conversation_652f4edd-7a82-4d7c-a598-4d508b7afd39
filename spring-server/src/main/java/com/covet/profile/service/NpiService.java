package com.covet.profile.service;

import com.covet.profile.clients.npi.NpiClient;
import com.covet.profile.clients.npi.request.NpiRequestParams;
import com.covet.profile.clients.npi.response.NpiProvider;
import com.covet.profile.dto.NpiDto.TaxonomyInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
@Slf4j
@RequiredArgsConstructor
public class NpiService {
    private final NpiClient npiClient;

    public NpiProvider getNpiInfo(String npi) {
        if (npi == null || npi.isEmpty()) {
            return null;
        }
        try {
            NpiRequestParams params = NpiRequestParams.builder()
                    .version("2.1")
                    .number(npi)
                    .build();
            var res = npiClient.getNpiInfo(params);
            if (res.getResults().isEmpty()) {
                return null;
            }
            return res.getResults().get(0);
        } catch (Exception e) {
            return null;
        }
    }

    public List<NpiProvider> getNpiInfoList(List<String> npiList) {
        if (!npiList.isEmpty()) {
            try {
                var nThreads = Math.min(4, npiList.size());
                ExecutorService executor = Executors.newFixedThreadPool(nThreads);
                var npiInfoFutures = npiList.stream().map(item ->
                        CompletableFuture.supplyAsync(() -> getNpiInfo(item), executor)).toList();
                CompletableFuture<Void> allOf = CompletableFuture.allOf(npiInfoFutures.toArray(new CompletableFuture[0]));
                allOf.join();

                executor.shutdown();
                return npiInfoFutures.stream().map(CompletableFuture::join).toList();
            } catch (Exception e) {
                return Collections.emptyList();
            }
        }
        return Collections.emptyList();
    }

    public TaxonomyInfo findTaxonomyInfo(List<TaxonomyInfo> taxonomyInfos) {
        if (CollectionUtils.isEmpty(taxonomyInfos)) {
            return null;
        }
        var result = taxonomyInfos.stream().filter(TaxonomyInfo::isPrimary);
        return result.findFirst().orElse(null);
    }

    public String buildNpiAddressTemplate(NpiProvider provider) {
        if (provider == null) {
            return "";
        }
        if (!provider.getPracticeLocations().isEmpty()) {
            var info = provider.getPracticeLocations().get(0);
            if (Objects.nonNull(info.getAddress2())) {

                return String.format("%s %s %s, %s %s", info.getAddress1(), info.getAddress2(), info.getCity(),
                        info.getState(), info.getPostalCode());
            }
            return String.format("%s %s, %s %s", info.getAddress1(), info.getCity(),
                    info.getState(), info.getPostalCode());
        }
        if (!provider.getAddresses().isEmpty()) {
            var info = provider.getAddresses()
                    .stream()
                    .filter(item -> item.getAddressPurpose()
                            .equals("LOCATION"))
                    .findFirst();
            if (info.isPresent()) {
                return String.format("%s %s, %s %s", info.get().getAddress1(), info.get().getCity(),
                        info.get().getState(), info.get().getPostalCode());
            }
        }
        return "";
    }
}
