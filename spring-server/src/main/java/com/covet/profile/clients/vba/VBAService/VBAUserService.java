package com.covet.profile.clients.vba.VBAService;

import com.covet.profile.converter.FinanceConverter;
import com.covet.profile.converter.SubscriberConverter;
import com.covet.profile.converter.VBA.VBACopayConverter;
import com.covet.profile.converter.VBA.VBAProviderConverter;
import com.covet.profile.converter.VBA.VBASpendingConverter;
import com.covet.profile.dto.ProviderProfileCheckDto;
import com.covet.profile.dto.VbaDto.ProfileCheckResultDto;
import com.covet.profile.dto.covet.profile.ProfileCheckDto;
import com.covet.profile.dto.covet.profile.ProfileInfoDto;
import com.covet.profile.persistence.model.Physician;
import com.covet.profile.persistence.model.Profile;
import com.covet.profile.service.Auth0Service;
import com.covet.profile.utils.AuthUtils;
import com.covet.profile.clients.vba.VBARepository.VBAPlanRepository;
import com.covet.profile.clients.vba.VBADtos.Financial.*;
import com.covet.profile.clients.vba.VBADtos.IDcard.ImmutableVBAIDcardDto;
import com.covet.profile.clients.vba.VBADtos.IDcard.Spending.ImmutableVBASpendingDto;
import com.covet.profile.clients.vba.VBADtos.IDcard.Spending.VBASpendingDto;
import com.covet.profile.clients.vba.VBADtos.IDcard.VBAIDcardDto;
import com.covet.profile.clients.vba.VBADtos.Member.SubscriberMemberDto;
import com.covet.profile.clients.vba.VBADtos.Member.VBAMemberDto;
import com.covet.profile.clients.vba.VBADtos.Providers.VBAProviderDto;
import com.covet.profile.clients.vba.VBADtos.Subscriber.EnrollmentSubscriberDto;
import com.covet.profile.clients.vba.VBAInterfaces.IVBAUserService;
import com.covet.profile.clients.vba.VBAModels.EnrollmentSubscriber;
import com.covet.profile.clients.vba.VBAModels.Financial.VBAMemberAccum;
import com.covet.profile.clients.vba.VBAModels.Financial.VBAPlan;
import com.covet.profile.clients.vba.VBAModels.Financial.VBAPlanAccumulator;
import com.covet.profile.clients.vba.VBAModels.Financial.VBAPlanEnrollmentPlan;
import com.covet.profile.clients.vba.VBAModels.Plans.VBAPlanDisplay;
import com.covet.profile.clients.vba.VBAModels.Subscriber;
import com.covet.profile.clients.vba.VBAModels.VBAMember;
import com.covet.profile.clients.vba.VBAModels.VBAProvider;
import com.covet.profile.clients.vba.VBARepository.VBAUserRepository;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
public class VBAUserService implements IVBAUserService {
    private final VBAUserRepository vbaUserRepository;
    private final VBAPlanRepository vbaPlanRepository;
    private final Auth0Service auth0Service;
    private final ObjectMapper objMapper;
    private final String[] benefitCodes = { "MEDPCPOV", "MEDSPECOV", "MEDUCC", "MEDER" };
    private final Logger log = LoggerFactory.getLogger(VBAUserService.class);

    private static final String DATA_PATH = "/data";

    private static final String MPX_IDCARD_INN_IND_DED_3TIER = "MPX_IDCARD_INN_IND_DED_3TIER";
    private static final String MPX_IDCARD_INN_FAM_DED_3TIER = "MPX_IDCARD_INN_FAM_DED_3TIER";
    private static final String MPX_IDCARD_INN_IND_OOP_3TIER = "MPX_IDCARD_INN_IND_OOP_3TIER";
    private static final String MPX_IDCARD_INN_FAM_OOP_3TIER = "MPX_IDCARD_INN_FAM_OOP_3TIER";

    private static final Set<String> THREE_TIER_INN_MAX_PARSE_DISPLAY_TYPES = Set.of(
            MPX_IDCARD_INN_IND_DED_3TIER, MPX_IDCARD_INN_FAM_DED_3TIER,
            MPX_IDCARD_INN_IND_OOP_3TIER, MPX_IDCARD_INN_FAM_OOP_3TIER
    );

    private static final Set<String> ALL_THREE_TIER_INDICATOR_DISPLAY_TYPES = Set.of(
            "MPX_IDCARD_INN_FAM_DED_3TIER", "MPX_IDCARD_INN_FAM_OOP_3TIER",
            "MPX_IDCARD_INN_IND_DED_3TIER", "MPX_IDCARD_INN_IND_OOP_3TIER",
            "MPX_IDCARD_OON_FAM_DED_3TIER", "MPX_IDCARD_OON_FAM_OOP_3TIER",
            "MPX_IDCARD_OON_IND_DED_3TIER", "MPX_IDCARD_OON_IND_OOP_3TIER"
    );

    @Autowired
    public VBAUserService(VBAUserRepository vbaUserRepository,
                          VBAPlanRepository vbaPlanRepository, Auth0Service auth0Service) {
        this.vbaUserRepository = vbaUserRepository;
        this.vbaPlanRepository = vbaPlanRepository;
        this.auth0Service = auth0Service;
        this.objMapper = new ObjectMapper();
        objMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public record PlanTierMaxValues(BigDecimal tier1Max, BigDecimal tier2Max) {
        public static PlanTierMaxValues fromDisplayString(String display, Logger logger) {
            if (display == null || display.isBlank()) {
                logger.warn("PlanTierMaxValues: Display string is null or blank for tier max parsing.");
                return new PlanTierMaxValues(BigDecimal.ZERO, BigDecimal.ZERO);
            }
            try {
                String[] parts = display.replace("$", "").replace(",", "").split("/");
                if (parts.length == 2) {
                    BigDecimal t1Max = new BigDecimal(parts[0].trim());
                    BigDecimal t2Max = new BigDecimal(parts[1].trim());
                    return new PlanTierMaxValues(t1Max, t2Max);
                } else {
                     logger.warn("PlanTierMaxValues: Display string '{}' not in expected '$X / $Y' format after cleaning.", display);
                }
            } catch (NumberFormatException | ArrayIndexOutOfBoundsException e) {
                logger.error("PlanTierMaxValues: Error parsing tier display string: '{}'", display, e);
            }
            return new PlanTierMaxValues(BigDecimal.ZERO, BigDecimal.ZERO);
        }
    }

    public record PlanTierInfo(
            boolean is3TierPlan,
            PlanTierMaxValues individualDeductibleTiers,
            PlanTierMaxValues familyDeductibleTiers,
            PlanTierMaxValues individualOoPTiers,
            PlanTierMaxValues familyOoPTiers
    ) {
        public static PlanTierInfo defaultTwoTier() {
            return new PlanTierInfo(false, null, null, null, null);
        }
    }

    private PlanTierInfo parsePlanTierInfo(List<VBAPlanDisplay> planDisplayList, String currentPlanID, Logger logger) {
        if (planDisplayList == null || planDisplayList.isEmpty() || currentPlanID == null) {
            logger.debug("parsePlanTierInfo: PlanDisplayList is null/empty or currentPlanID is null. Defaulting to 2-tier.");
            return PlanTierInfo.defaultTwoTier();
        }

        boolean is3TierCandidate = false;
        PlanTierMaxValues indDedTiers = null;
        PlanTierMaxValues famDedTiers = null;
        PlanTierMaxValues indOoPTiers = null;
        PlanTierMaxValues famOoPTiers = null;

        List<VBAPlanDisplay> relevantDisplays = planDisplayList.stream()
            .filter(d -> currentPlanID.equals(d.getPlanId()))
            .collect(Collectors.toList());

        if (relevantDisplays.isEmpty()) {
            logger.debug("parsePlanTierInfo: No VBAPlanDisplay entries found for planID: {}. Defaulting to 2-tier.", currentPlanID);
            return PlanTierInfo.defaultTwoTier();
        }

        for (VBAPlanDisplay displayItem : relevantDisplays) {
            if (ALL_THREE_TIER_INDICATOR_DISPLAY_TYPES.contains(displayItem.getDisplayType())) {
                is3TierCandidate = true;
            }

            if (THREE_TIER_INN_MAX_PARSE_DISPLAY_TYPES.contains(displayItem.getDisplayType())) {
                switch (displayItem.getDisplayType()) {
                    case MPX_IDCARD_INN_IND_DED_3TIER:
                        indDedTiers = PlanTierMaxValues.fromDisplayString(displayItem.getDisplay(), logger);
                        break;
                    case MPX_IDCARD_INN_FAM_DED_3TIER:
                        famDedTiers = PlanTierMaxValues.fromDisplayString(displayItem.getDisplay(), logger);
                        break;
                    case MPX_IDCARD_INN_IND_OOP_3TIER:
                        indOoPTiers = PlanTierMaxValues.fromDisplayString(displayItem.getDisplay(), logger);
                        break;
                    case MPX_IDCARD_INN_FAM_OOP_3TIER:
                        famOoPTiers = PlanTierMaxValues.fromDisplayString(displayItem.getDisplay(), logger);
                        break;
                }
            }
        }
        
        boolean isEffectively3Tier = is3TierCandidate && 
                                     (indDedTiers != null || famDedTiers != null || indOoPTiers != null || famOoPTiers != null);

        if (is3TierCandidate && !isEffectively3Tier) {
            logger.warn("Plan {} was marked as a 3-tier candidate by displayType, but failed to parse specific Tier 1/2 max values. Check VBAPlanDisplay data.", currentPlanID);
        }
        
        return new PlanTierInfo(isEffectively3Tier, indDedTiers, famDedTiers, indOoPTiers, famOoPTiers);
    }

    @SuppressWarnings("unchecked")
    public FinancialDto getFinancialOverview() throws InterruptedException, ExecutionException {
        FinancialItemDto financialItemDto = null;
        String auth0ID = AuthUtils.getAuth0Id();

        SubscriberMemberDto subscriberInfo;
        try {
            subscriberInfo = auth0Service.getSubscriberMemberID(auth0ID);
        } catch (Exception e) {
            log.error("Error getting SubscriberInfo for Auth0ID: {}", auth0ID, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Can't get SubscriberInfo!");
        }
        String subscriberID = subscriberInfo.getSubscriberId();
        String memberSeq = subscriberInfo.getMemberSequence();

        CompletableFuture<List<VBAMemberAccum>> memberAccumFuture = getListMemberAccum(subscriberID, memberSeq);
        CompletableFuture<String> getPlansFuture = vbaUserRepository.getEffectivePlanID(subscriberID);
        
        CompletableFuture<List<VBAPlanDisplay>> planDisplayFuture = getPlansFuture.thenComposeAsync(planId -> {
            if (planId == null) {
                log.info("No effective planID found for subscriberID: {}. Skipping plan display fetch.", subscriberID);
                return CompletableFuture.completedFuture(Collections.<VBAPlanDisplay>emptyList());
            }
            log.debug("Fetching plan display list for planID: {}", planId);
            return CompletableFuture.supplyAsync(() -> {
                 try {
                    return vbaPlanRepository.getPlanDisplayList(planId);
                } catch (Exception e) { 
                    log.error("Failed to get plan display list for planID: {}", planId, e);
                    throw new RuntimeException("Error fetching plan display list for plan " + planId, e);
                }
            });
        });

        CompletableFuture<Void> prepareData = CompletableFuture.allOf(
                memberAccumFuture,
                getPlansFuture,
                planDisplayFuture
        );

        try {
            prepareData.join(); 
        } catch (Exception e) { 
             log.error("Error during prepareData.join() for subscriberID: {}. Cause: {}", subscriberID, e.getCause() != null ? e.getCause().getMessage() : e.getMessage(), e);
             if (e.getCause() instanceof ResponseStatusException rse) {
                 throw rse;
             } else if (e.getCause() instanceof RuntimeException && e.getCause().getCause() instanceof ResponseStatusException rseNested) {
                 throw rseNested;
             }
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to prepare initial financial data: " + (e.getCause() != null ? e.getCause().getMessage() : e.getMessage()));
        }

        List<VBAMemberAccum> memberAccumList = memberAccumFuture.get();
        String planID = getPlansFuture.get();
        List<VBAPlanDisplay> planDisplayList = planDisplayFuture.get();

        final PlanTierInfo planTierInfo = planID != null ? 
            parsePlanTierInfo(planDisplayList, planID, log) : 
            PlanTierInfo.defaultTwoTier();

        if (planID != null) {
            log.debug("Processing financial overview for planID: {}", planID);
            log.info("Plan ID: {} determined as {}-tier plan.", planID, planTierInfo.is3TierPlan() ? "3" : "2");

            try {
                Map<String, BigDecimal> memberAccumData = buildMemberAccumData(memberAccumList, planTierInfo);

                var accumulatorFuture = futureGetPlanAccumulator(planID)
                        .thenApplyAsync(planAccumulatorList -> buildPlanAccumulatorData(memberAccumData, planAccumulatorList, planTierInfo, planID));

                var planFuture = getPlan(planID)
                        .thenApplyAsync(plan -> buildPlanFinancialData(memberAccumData, plan, planTierInfo, planID));

                List<CompletableFuture<?>> allSubsequentFutures = new ArrayList<>();
                allSubsequentFutures.add(accumulatorFuture);
                allSubsequentFutures.add(planFuture);
                
                CompletableFuture<Void> allOfSubsequent = CompletableFuture.allOf(allSubsequentFutures.toArray(new CompletableFuture[0]));
                
                try {
                    allOfSubsequent.join();
                } catch (Exception e) {
                     log.error("Error during allOfSubsequent.join() for planID: {}. Cause: {}", planID, e.getCause() != null ? e.getCause().getMessage() : e.getMessage(), e);
                     if (e.getCause() instanceof ResponseStatusException rse) {
                         throw rse;
                     }
                    throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to process plan details: " + (e.getCause() != null ? e.getCause().getMessage() : e.getMessage()));
                }

                financialItemDto = ImmutableFinancialItemDto.of(planID,
                        (Map<String, FinancialValuesDto>) accumulatorFuture.get().get("accum"),
                        (Map<String, FinancialValuesDto>) planFuture.get().get("individual"),
                        (Map<String, FinancialValuesDto>) planFuture.get().get("family"));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("Interrupted while building financial item for planID: {}", planID, e);
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Process interrupted: " + e.getMessage());
            } catch (ExecutionException e) {
                log.error("Execution error while building financial item for planID: {}. Cause: {}", planID, e.getCause() != null ? e.getCause().getMessage() : "N/A", e.getCause());
                 if (e.getCause() instanceof ResponseStatusException rse) {
                     throw rse;
                 }
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Execution error: " + (e.getCause() != null ? e.getCause().getMessage() : e.getMessage()));
            } catch (ResponseStatusException e) {
                throw e;
            }
             catch (Exception e) { 
                log.error("Unexpected error while building financial item for planID: {}", planID, e);
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Unexpected error processing financial overview: " + e.getMessage());
            }
        } else {
            log.warn("PlanID is null for subscriberID: {}. Cannot generate financial overview.", subscriberID);
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Your plan has expired or could not be determined.");
        }

        return ImmutableFinancialDto.of(subscriberID, memberSeq, financialItemDto);
    }

    public Map<String, BigDecimal> buildMemberAccumData(List<VBAMemberAccum> memberAccumList, PlanTierInfo planTierInfo) {
        Map<String, BigDecimal> result = new HashMap<>();
        
        BigDecimal sumDedPPOUsed = BigDecimal.ZERO;
        BigDecimal sumOutOfPocketNet = BigDecimal.ZERO;
        BigDecimal sumDeductibleUsedOON = BigDecimal.ZERO;
        BigDecimal sumOutOfPocketOON = BigDecimal.ZERO;

        BigDecimal sumDedPPOTier1Used = BigDecimal.ZERO;
        BigDecimal sumDedPPOTier2Used = BigDecimal.ZERO;
        BigDecimal sumOutOfPocketNetTier1 = BigDecimal.ZERO;
        BigDecimal sumOutOfPocketNetTier2 = BigDecimal.ZERO;

        for (VBAMemberAccum item : memberAccumList) {
            sumDeductibleUsedOON = sumDeductibleUsedOON.add(Optional.ofNullable(item.getDeductibleUsedOON()).orElse(BigDecimal.ZERO));
            sumOutOfPocketOON = sumOutOfPocketOON.add(Optional.ofNullable(item.getOutOfPocketOON()).orElse(BigDecimal.ZERO));

            BigDecimal itemDedPPOUsed = Optional.ofNullable(item.getDedPPOUsed()).orElse(BigDecimal.ZERO);
            BigDecimal itemOutOfPocketNet = Optional.ofNullable(item.getOutOfPocketNet()).orElse(BigDecimal.ZERO);

            if (planTierInfo.is3TierPlan()) {
                String benefitCode = item.getBenefitCode();
                if (benefitCode != null && benefitCode.endsWith("2")) {
                    sumDedPPOTier2Used = sumDedPPOTier2Used.add(itemDedPPOUsed);
                    sumOutOfPocketNetTier2 = sumOutOfPocketNetTier2.add(itemOutOfPocketNet);
                } else if (itemDedPPOUsed.compareTo(BigDecimal.ZERO) != 0 || itemOutOfPocketNet.compareTo(BigDecimal.ZERO) != 0) {
                    sumDedPPOTier1Used = sumDedPPOTier1Used.add(itemDedPPOUsed);
                    sumOutOfPocketNetTier1 = sumOutOfPocketNetTier1.add(itemOutOfPocketNet);
                }
            } else {
                sumDedPPOUsed = sumDedPPOUsed.add(itemDedPPOUsed);
                sumOutOfPocketNet = sumOutOfPocketNet.add(itemOutOfPocketNet);
            }
        }

        if (planTierInfo.is3TierPlan()) {
            result.put("sumDedPPOTier1Used", sumDedPPOTier1Used);
            result.put("sumDedPPOTier2Used", sumDedPPOTier2Used);
            result.put("sumOutOfPocketNetTier1", sumOutOfPocketNetTier1);
            result.put("sumOutOfPocketNetTier2", sumOutOfPocketNetTier2);
            log.debug("3-Tier Accumulations: DedT1Used={}, DedT2Used={}, OoPT1Used={}, OoPT2Used={}",
                sumDedPPOTier1Used, sumDedPPOTier2Used, sumOutOfPocketNetTier1, sumOutOfPocketNetTier2);
        } else {
            result.put("sumDedPPOUsed", sumDedPPOUsed);
            result.put("sumOutOfPocketNet", sumOutOfPocketNet);
            log.debug("2-Tier Accumulations: DedPPOUsed={}, OoPNetUsed={}", sumDedPPOUsed, sumOutOfPocketNet);
        }
        result.put("sumDeductibleUsedOON", sumDeductibleUsedOON);
        result.put("sumOutOfPocketOON", sumOutOfPocketOON);
        log.debug("OON Accumulations: DedOONUsed={}, OoPOONUsed={}", sumDeductibleUsedOON, sumOutOfPocketOON);
        
        return result;
    }
    
    public Map<String, Map<String, FinancialValuesDto>> buildPlanAccumulatorData(
            Map<String, BigDecimal> memberAccumData,
            List<VBAPlanAccumulator> planAccumulatorList,
            PlanTierInfo planTierInfo, String planIDContext) {
        
        Map<String, Map<String, FinancialValuesDto>> result = new HashMap<>();
        Map<String, FinancialValuesDto> accum = new HashMap<>();

        BigDecimal sumMemberDedPPOPlanDefault = BigDecimal.ZERO;
        BigDecimal sumOoPMaxNetMemPlanDefault = BigDecimal.ZERO;
        
        for (VBAPlanAccumulator item : planAccumulatorList) {
            sumMemberDedPPOPlanDefault = sumMemberDedPPOPlanDefault.add(Optional.ofNullable(item.getMemberDedPPO()).orElse(BigDecimal.ZERO));
            sumOoPMaxNetMemPlanDefault = sumOoPMaxNetMemPlanDefault.add(Optional.ofNullable(item.getOoPMaxNetMem()).orElse(BigDecimal.ZERO));
        }

        if (planTierInfo.is3TierPlan()) {
            log.debug("Building 3-tier plan accumulator data for planID: {} (will output flat camelCase keys for frontend AccumulatorData.dart compatibility)", planIDContext);
            PlanTierMaxValues indDedTiers = planTierInfo.individualDeductibleTiers();
            PlanTierMaxValues indOoPTiers = planTierInfo.individualOoPTiers();

            BigDecimal usedDedTier1 = memberAccumData.getOrDefault("sumDedPPOTier1Used", BigDecimal.ZERO);
            BigDecimal usedOoPTier1 = memberAccumData.getOrDefault("sumOutOfPocketNetTier1", BigDecimal.ZERO);

            // Use Tier 1 data for the flat keys expected by frontend AccumulatorData.dart
            if (indDedTiers != null && indDedTiers.tier1Max() != null) { // Check tier1Max is not null
                 accum.put("individualDeductible", FinanceConverter.financialValueToDto(usedDedTier1,
                        indDedTiers.tier1Max().subtract(usedDedTier1).max(BigDecimal.ZERO), indDedTiers.tier1Max()));
            } else {
                log.warn("Individual Deductible Tier 1 max values not found or null for 3-tier plan (accumulator section) planID: {}. Using default plan limits for individualDeductible.", planIDContext);
                 BigDecimal sumDedPPOUsed = memberAccumData.getOrDefault("sumDedPPOTier1Used", memberAccumData.getOrDefault("sumDedPPOUsed", BigDecimal.ZERO));
                 accum.put("individualDeductible", FinanceConverter.financialValueToDto(sumDedPPOUsed,
                    sumMemberDedPPOPlanDefault.subtract(sumDedPPOUsed).max(BigDecimal.ZERO), sumMemberDedPPOPlanDefault));
            }

            if (indOoPTiers != null && indOoPTiers.tier1Max() != null) { // Check tier1Max is not null
                accum.put("outOfPocketMax", FinanceConverter.financialValueToDto(usedOoPTier1,
                        indOoPTiers.tier1Max().subtract(usedOoPTier1).max(BigDecimal.ZERO), indOoPTiers.tier1Max()));
            } else {
                log.warn("Individual OOP Tier 1 max values not found or null for 3-tier plan (accumulator section) planID: {}. Using default plan limits for outOfPocketMax.", planIDContext);
                BigDecimal sumOutOfPocketNet = memberAccumData.getOrDefault("sumOutOfPocketNetTier1", memberAccumData.getOrDefault("sumOutOfPocketNet", BigDecimal.ZERO));
                accum.put("outOfPocketMax", FinanceConverter.financialValueToDto(sumOutOfPocketNet,
                    sumOoPMaxNetMemPlanDefault.subtract(sumOutOfPocketNet).max(BigDecimal.ZERO), sumOoPMaxNetMemPlanDefault));
            }
        } else {
            log.debug("Building 2-tier plan accumulator data for planID: {}", planIDContext);
            BigDecimal sumDedPPOUsed = memberAccumData.getOrDefault("sumDedPPOUsed", BigDecimal.ZERO);
            BigDecimal sumOutOfPocketNet = memberAccumData.getOrDefault("sumOutOfPocketNet", BigDecimal.ZERO);

            accum.put("individualDeductible", FinanceConverter.financialValueToDto(sumDedPPOUsed,
                    sumMemberDedPPOPlanDefault.subtract(sumDedPPOUsed).max(BigDecimal.ZERO), sumMemberDedPPOPlanDefault));
            accum.put("outOfPocketMax", FinanceConverter.financialValueToDto(sumOutOfPocketNet,
                    sumOoPMaxNetMemPlanDefault.subtract(sumOutOfPocketNet).max(BigDecimal.ZERO), sumOoPMaxNetMemPlanDefault));
        }
        
        result.put("accum", accum);
        return result;
    }
    
    public Map<String, Map<String, FinancialValuesDto>> buildPlanFinancialData(
            Map<String, BigDecimal> memberAccumData,
            VBAPlan plan,
            PlanTierInfo planTierInfo, String planIDContext) {

        Map<String, Map<String, FinancialValuesDto>> result = new HashMap<>();
        Map<String, FinancialValuesDto> individual = new HashMap<>();
        Map<String, FinancialValuesDto> family = new HashMap<>();

        BigDecimal sumDeductibleUsedOON = memberAccumData.getOrDefault("sumDeductibleUsedOON", BigDecimal.ZERO);
        BigDecimal sumOutOfPocketOON = memberAccumData.getOrDefault("sumOutOfPocketOON", BigDecimal.ZERO);

        BigDecimal planIndDedOON = Optional.ofNullable(plan.getMemberDedOON()).orElse(BigDecimal.ZERO);
        BigDecimal planIndOoPOON = Optional.ofNullable(plan.getOoPMaxOONMem()).orElse(BigDecimal.ZERO);
        BigDecimal planFamDedOON = Optional.ofNullable(plan.getSubscriberDedOON()).orElse(BigDecimal.ZERO);
        BigDecimal planFamOoPOON = Optional.ofNullable(plan.getOoPMaxOONSub()).orElse(BigDecimal.ZERO);

        // OON Keys (camelCase as per DetailData.dart flat field expectations)
        individual.put("deductibleOON", FinanceConverter.financialValueToDto(sumDeductibleUsedOON,
                planIndDedOON.subtract(sumDeductibleUsedOON).max(BigDecimal.ZERO), planIndDedOON));
        individual.put("outOfPocketMaxOON", FinanceConverter.financialValueToDto(sumOutOfPocketOON,
                planIndOoPOON.subtract(sumOutOfPocketOON).max(BigDecimal.ZERO), planIndOoPOON));
        
        family.put("deductibleOON", FinanceConverter.financialValueToDto(sumDeductibleUsedOON, 
                planFamDedOON.subtract(sumDeductibleUsedOON).max(BigDecimal.ZERO), planFamDedOON));
        family.put("outOfPocketMaxOON", FinanceConverter.financialValueToDto(sumOutOfPocketOON,
                planFamOoPOON.subtract(sumOutOfPocketOON).max(BigDecimal.ZERO), planFamOoPOON));

        if (planTierInfo.is3TierPlan()) {
            log.debug("Building 3-tier plan financial data (individual/family) for planID: {} with camelCase keys", planIDContext);
            BigDecimal indDedT1Used = memberAccumData.getOrDefault("sumDedPPOTier1Used", BigDecimal.ZERO);
            BigDecimal indDedT2Used = memberAccumData.getOrDefault("sumDedPPOTier2Used", BigDecimal.ZERO);
            BigDecimal indOoPT1Used = memberAccumData.getOrDefault("sumOutOfPocketNetTier1", BigDecimal.ZERO);
            BigDecimal indOoPT2Used = memberAccumData.getOrDefault("sumOutOfPocketNetTier2", BigDecimal.ZERO);
            
            PlanTierMaxValues indDedTiers = planTierInfo.individualDeductibleTiers();
            PlanTierMaxValues famDedTiers = planTierInfo.familyDeductibleTiers();
            PlanTierMaxValues indOoPTiers = planTierInfo.individualOoPTiers();
            PlanTierMaxValues famOoPTiers = planTierInfo.familyOoPTiers();

            // Individual INN Tiers (camelCase keys)
            if (indDedTiers != null) {
                individual.put("deductibleINTier1", FinanceConverter.financialValueToDto(indDedT1Used,
                        indDedTiers.tier1Max().subtract(indDedT1Used).max(BigDecimal.ZERO), indDedTiers.tier1Max()));
                individual.put("deductibleINTier2", FinanceConverter.financialValueToDto(indDedT2Used,
                        indDedTiers.tier2Max().subtract(indDedT2Used).max(BigDecimal.ZERO), indDedTiers.tier2Max()));
            } else { log.warn("Individual Deductible Tier INN max values not found for 3-tier planID: {}.", planIDContext); }
            if (indOoPTiers != null) {
                individual.put("outOfPocketMaxINTier1", FinanceConverter.financialValueToDto(indOoPT1Used,
                        indOoPTiers.tier1Max().subtract(indOoPT1Used).max(BigDecimal.ZERO), indOoPTiers.tier1Max()));
                individual.put("outOfPocketMaxINTier2", FinanceConverter.financialValueToDto(indOoPT2Used,
                        indOoPTiers.tier2Max().subtract(indOoPT2Used).max(BigDecimal.ZERO), indOoPTiers.tier2Max()));
            } else { log.warn("Individual OOP Tier INN max values not found for 3-tier planID: {}.", planIDContext); }

            // Family INN Tiers (camelCase keys)
            if (famDedTiers != null) {
                family.put("deductibleINTier1", FinanceConverter.financialValueToDto(indDedT1Used, 
                        famDedTiers.tier1Max().subtract(indDedT1Used).max(BigDecimal.ZERO), famDedTiers.tier1Max()));
                family.put("deductibleINTier2", FinanceConverter.financialValueToDto(indDedT2Used,
                        famDedTiers.tier2Max().subtract(indDedT2Used).max(BigDecimal.ZERO), famDedTiers.tier2Max()));
            } else { log.warn("Family Deductible Tier INN max values not found for 3-tier planID: {}.", planIDContext); }
            if (famOoPTiers != null) {
                family.put("outOfPocketMaxINTier1", FinanceConverter.financialValueToDto(indOoPT1Used,
                        famOoPTiers.tier1Max().subtract(indOoPT1Used).max(BigDecimal.ZERO), famOoPTiers.tier1Max()));
                family.put("outOfPocketMaxINTier2", FinanceConverter.financialValueToDto(indOoPT2Used,
                        famOoPTiers.tier2Max().subtract(indOoPT2Used).max(BigDecimal.ZERO), famOoPTiers.tier2Max()));
            } else { log.warn("Family OOP Tier INN max values not found for 3-tier planID: {}.", planIDContext); }

        } else {
            log.debug("Building 2-tier plan financial data (individual/family) for planID: {}", planIDContext);
            BigDecimal sumDedPPOUsed = memberAccumData.getOrDefault("sumDedPPOUsed", BigDecimal.ZERO);
            BigDecimal sumOutOfPocketNet = memberAccumData.getOrDefault("sumOutOfPocketNet", BigDecimal.ZERO);

            BigDecimal planIndDedINN = Optional.ofNullable(plan.getMemberDedPPO()).orElse(BigDecimal.ZERO);
            BigDecimal planIndOoPINN = Optional.ofNullable(plan.getOoPMaxNetMem()).orElse(BigDecimal.ZERO);
            BigDecimal planFamDedINN = Optional.ofNullable(plan.getSubscriberDedPPO()).orElse(BigDecimal.ZERO);
            BigDecimal planFamOoPINN = Optional.ofNullable(plan.getOoPMaxNetSub()).orElse(BigDecimal.ZERO);

            // 2-Tier INN Keys (camelCase as per DetailData.dart flat field expectations)
            individual.put("deductibleIN", FinanceConverter.financialValueToDto(sumDedPPOUsed,
                    planIndDedINN.subtract(sumDedPPOUsed).max(BigDecimal.ZERO), planIndDedINN));
            individual.put("outOfPocketMaxIn", FinanceConverter.financialValueToDto(sumOutOfPocketNet,
                    planIndOoPINN.subtract(sumOutOfPocketNet).max(BigDecimal.ZERO), planIndOoPINN));
            
            family.put("deductibleIN", FinanceConverter.financialValueToDto(sumDedPPOUsed,
                    planFamDedINN.subtract(sumDedPPOUsed).max(BigDecimal.ZERO), planFamDedINN));
            family.put("outOfPocketMaxIn", FinanceConverter.financialValueToDto(sumOutOfPocketNet,
                    planFamOoPINN.subtract(sumOutOfPocketNet).max(BigDecimal.ZERO), planFamOoPINN));
        }

        result.put("individual", individual);
        result.put("family", family);
        return result;
    }

    public List<Physician> mappingProviders() {
        return vbaUserRepository.getAllProviders();
    }

    public List<Profile> mappingSubscribers() {
        return vbaUserRepository.getAllSubscribers();
    }

    public SubscriberMemberDto getSubscriberMemberInfo(String email) {
        return vbaUserRepository.getSubscriberMemberInfo(email);
    }

    public CompletableFuture<List<VBAMemberAccum>> getListMemberAccum(String subscriberID, String memberSeq) {
        return CompletableFuture.supplyAsync(() -> {
            JsonNode rawData = vbaUserRepository.getListMemberAccum(subscriberID, memberSeq);
            JsonNode data = rawData.at(DATA_PATH);
            if (data.isMissingNode()) {
                log.error("getListMemberAccum: Data node is missing for subscriberID: {}, memberSeq: {}", subscriberID, memberSeq);
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Can't get list member accums!");
            }
            TypeReference<List<VBAMemberAccum>> typeRef = new TypeReference<>() {};
            return objMapper.convertValue(data, typeRef);
        });
    }

    public CompletableFuture<List<VBAPlanAccumulator>> futureGetPlanAccumulator(String planID) {
        return CompletableFuture.supplyAsync(() -> {
            JsonNode rawData = vbaUserRepository.getPlanAccumulator(planID);
            JsonNode data = rawData.at(DATA_PATH);
            if (data.isMissingNode()) {
                 log.error("futureGetPlanAccumulator: Data node is missing for planID: {}", planID);
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Can't get plan accumulator!");
            }
            TypeReference<List<VBAPlanAccumulator>> typeRef = new TypeReference<>() {};
            return objMapper.convertValue(data, typeRef);
        });
    }

    public CompletableFuture<VBAPlan> getPlan(String planID) {
        return CompletableFuture.supplyAsync(() -> {
            JsonNode rawData = vbaUserRepository.getPlan(planID);
            JsonNode data = rawData.at(DATA_PATH);
             if (data.isMissingNode() || data.isNull()) { 
                log.error("getPlan: Data node is missing or null for planID: {}", planID);
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Can't get plan details for planID: " + planID);
            }
            TypeReference<VBAPlan> typeRef = new TypeReference<>() {};
            return objMapper.convertValue(data, typeRef);
        });
    }

    public CompletableFuture<List<VBAPlanEnrollmentPlan>> getSubscriberEnrollmentPlans(String subscriberID) {
        return CompletableFuture.supplyAsync(() -> {
            JsonNode rawData = vbaUserRepository.getSubscriberEnrollmentPlans(subscriberID);
            JsonNode data = rawData.at(DATA_PATH);
             if (data.isMissingNode()) {
                log.error("getSubscriberEnrollmentPlans: Data node is missing for subscriberID: {}", subscriberID);
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Can't get subscriber enrollment plans!");
            }
            TypeReference<List<VBAPlanEnrollmentPlan>> typeRef = new TypeReference<>() {};
            return objMapper.convertValue(data, typeRef);
        });
    }
    
    public CompletableFuture<Subscriber> futureGetSubscriber() {
        String auth0ID = AuthUtils.getAuth0Id();
        String email = "";
        try {
            email = auth0Service.getUserEmail(auth0ID);
        } catch (Exception e) {
            log.error("futureGetSubscriber: Can't get email for auth0ID: {}", auth0ID, e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Can't get email!");
        }
        
        final String finalEmail = email; 
        return CompletableFuture.supplyAsync(() -> {
            SubscriberMemberDto si = getSubscriberMemberInfo(finalEmail); 
            if (si == null) {
                log.error("futureGetSubscriber: SubscriberMemberInfo not found for email obtained from auth0ID: {}", auth0ID);
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Subscriber identification failed.");
            }
            String subscriberID = si.getSubscriberId();
            String memberSeq = si.getMemberSequence();
            
            JsonNode rawData = vbaUserRepository.getSubscriber(subscriberID, memberSeq);
            JsonNode data = rawData.at(DATA_PATH);
            if (data.isMissingNode()) {
                log.error("futureGetSubscriber: Data node is missing for subscriber: {}/{}", subscriberID, memberSeq);
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Can't get subscriber details!");
            }
            return objMapper.convertValue(data, Subscriber.class);
        });
    }

    private CompletableFuture<VBASpendingDto> futureCardSpendingSection(String planID) {
        return futureGetPlanAccumulator(planID).thenApplyAsync(planAccumulators -> {
            BigDecimal deductiblesInNetwork = BigDecimal.ZERO;
            BigDecimal deductiblesOutOfNetwork = BigDecimal.ZERO; 
            BigDecimal outOfPocketMaxInNetwork = BigDecimal.ZERO;
            BigDecimal outOfPocketMaxOutOfNetwork = BigDecimal.ZERO;

            for (VBAPlanAccumulator item : planAccumulators) {
                deductiblesInNetwork = deductiblesInNetwork.add(Optional.ofNullable(item.getMemberDedPPO()).orElse(BigDecimal.ZERO));
                deductiblesOutOfNetwork = deductiblesOutOfNetwork.add(Optional.ofNullable(item.getMemberDedOON()).orElse(BigDecimal.ZERO));
                outOfPocketMaxInNetwork = outOfPocketMaxInNetwork.add(Optional.ofNullable(item.getOoPMaxNetMem()).orElse(BigDecimal.ZERO));
                outOfPocketMaxOutOfNetwork = outOfPocketMaxOutOfNetwork.add(Optional.ofNullable(item.getOoPMaxOONMem()).orElse(BigDecimal.ZERO));
            }
            var deductibles = VBASpendingConverter.vbaSpendingAttributesToDto(deductiblesInNetwork, deductiblesOutOfNetwork);
            var outOfPocketMax = VBASpendingConverter.vbaSpendingAttributesToDto(outOfPocketMaxInNetwork, outOfPocketMaxOutOfNetwork);
            return ImmutableVBASpendingDto.of(deductibles, outOfPocketMax);
        });
    }

    private List<CompletableFuture<BigDecimal>> futureGetCopayField(String planID) {
        return Arrays.stream(benefitCodes)
                .map(code -> CompletableFuture.supplyAsync(() -> vbaPlanRepository.getPlaceCopayList(planID, code))
                        .thenApplyAsync(item -> {
                            if (item != null && !item.isEmpty() && item.get(0) != null) { 
                                return Optional.ofNullable(item.get(0).getCopayAmountNet()).orElse(BigDecimal.ZERO);
                            }
                            return BigDecimal.ZERO;
                        }))
                .collect(Collectors.toList());
    }

    public VBAIDcardDto getIDCardInfo() {
        try {
            AtomicReference<CompletableFuture<VBASpendingDto>> cacheFutureCardSpendingSection = new AtomicReference<>();
            AtomicReference<List<CompletableFuture<BigDecimal>>> cacheFutureGetCopayField = new AtomicReference<>();
            var resultBuilder = ImmutableVBAIDcardDto.builder();

            CompletableFuture<List<CompletableFuture<?>>> processingChain = futureGetSubscriber().thenComposeAsync(sub -> {
                resultBuilder.name(sub.getFirstName() + " " + sub.getLastName());
                resultBuilder.subscriberID(sub.getSubscriberID());
                resultBuilder.memberSeq(sub.getMemberSeq());
                String inputDate = Optional.ofNullable(sub.getDateEnrolled()).orElse("2023-01-01T00:00:00"); 
                try {
                    LocalDateTime dateTime = LocalDateTime.parse(inputDate, DateTimeFormatter.ISO_LOCAL_DATE_TIME); 
                    resultBuilder.issued(dateTime.format(DateTimeFormatter.ofPattern("MM/dd/yyyy")));
                } catch (Exception e) {
                    log.error("Error parsing dateEnrolled for ID Card: '{}'", inputDate, e);
                    resultBuilder.issued("N/A"); 
                }
                
                return vbaUserRepository.getEffectivePlanID(sub.getSubscriberID()).thenApplyAsync(planID -> {
                    if (planID == null) {
                        log.warn("No effective planID for subscriber {} in getIDCardInfo. Spending/Copay sections will be empty.", sub.getSubscriberID());
                        return Collections.<CompletableFuture<?>>emptyList();
                    }
                    log.debug("Processing ID card for planID: {}", planID);
                    CompletableFuture<VBASpendingDto> spendingFuture = futureCardSpendingSection(planID);
                    List<CompletableFuture<BigDecimal>> copayFutures = futureGetCopayField(planID);
                    
                    cacheFutureCardSpendingSection.set(spendingFuture);
                    cacheFutureGetCopayField.set(copayFutures);
                    
                    List<CompletableFuture<?>> allFuturesForPlan = new ArrayList<>();
                    allFuturesForPlan.add(spendingFuture);
                    allFuturesForPlan.addAll(copayFutures);
                    return allFuturesForPlan;
                });
            });

            try {
                List<CompletableFuture<?>> futuresToJoin = processingChain.join();
                if (futuresToJoin != null && !futuresToJoin.isEmpty()) {
                    CompletableFuture.allOf(futuresToJoin.toArray(new CompletableFuture[0])).join();

                    VBASpendingDto vbaSpendingDto = cacheFutureCardSpendingSection.get().get();
                    
                    BigDecimal pcp = BigDecimal.ZERO;
                    BigDecimal urgentCare = BigDecimal.ZERO;
                    BigDecimal specialist = BigDecimal.ZERO;
                    BigDecimal emergencyRoom = BigDecimal.ZERO;

                    List<CompletableFuture<BigDecimal>> copayResults = cacheFutureGetCopayField.get();
                    if (copayResults != null) { 
                        if (copayResults.size() > 0) pcp = copayResults.get(0).get(); else log.warn("Missing copay future for PCP");
                        if (copayResults.size() > 1) specialist = copayResults.get(1).get(); else log.warn("Missing copay future for Specialist"); 
                        if (copayResults.size() > 2) urgentCare = copayResults.get(2).get(); else log.warn("Missing copay future for Urgent Care");
                        if (copayResults.size() > 3) emergencyRoom = copayResults.get(3).get(); else log.warn("Missing copay future for ER");
                    } else {
                        log.warn("Copay futures list (cacheFutureGetCopayField) is null. Copay values will be zero.");
                    }

                    var vbaCopayDto = VBACopayConverter.vbaCopayToDto(pcp, urgentCare, specialist, emergencyRoom);
                    resultBuilder.spending(vbaSpendingDto);
                    resultBuilder.copays(vbaCopayDto);
                } else {
                    log.info("No plan-specific futures to join for ID card (e.g., planID was null).");
                }
                return resultBuilder.build();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("getIDCardInfo interrupted: ", e);
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Cannot get ID Card Info: interrupted");
            } catch (ExecutionException e) {
                log.error("getIDCardInfo execution error: ", e.getCause() != null ? e.getCause() : e);
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Cannot get ID Card Info: execution error");
            } catch (Exception e) {
                log.error("Unexpected error in getIDCardInfo: ", e);
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Cannot get ID Card Info: " + e.getMessage());
            }
        } catch (Exception e) {
            log.error("Unexpected error in getIDCardInfo: ", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Cannot get ID Card Info: " + e.getMessage());
        }
    }

    public EnrollmentSubscriberDto getEnrollmentSubscriber(String subscriberId) {
        EnrollmentSubscriber enrollmentSubscriber = vbaUserRepository.getEnrollmentSubscriber(subscriberId);
        if (enrollmentSubscriber == null) {
            log.warn("EnrollmentSubscriber not found for ID: {}", subscriberId);
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Enrollment subscriber not found.");
        }
        return SubscriberConverter.toEnrollmentSubscriberDto(enrollmentSubscriber);
    }

    public List<VBAMemberDto> geMemberListBySubscriberId(String subscriberID) {
        JsonNode rawData = vbaUserRepository.getListMemberBySubscriberId(subscriberID);
        JsonNode data = rawData.at(DATA_PATH);
        if (data.isMissingNode()) {
            log.error("geMemberListBySubscriberId: Data node is missing for subscriberID: {}", subscriberID);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Can't get list member for subscriber!");
        }
        TypeReference<List<VBAMember>> typeRef = new TypeReference<>() {};
        List<VBAMember> memberList = objMapper.convertValue(data, typeRef);
        
        LocalDate dateMinus18 = LocalDate.now().minusYears(18);
        return memberList.stream()
            .filter(member -> member.getBirthDate() != null && ("01".equals(member.getRelationship()) || 
                           member.getBirthDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().isAfter(dateMinus18)))
            .map(SubscriberConverter::toMemberDto)
            .collect(Collectors.toList());
    }

    public List<VBAProviderDto> fetchVbaProviderList(List<String> providerList) {
        if (providerList == null || providerList.isEmpty()) {
            return Collections.emptyList();
        }
        List<CompletableFuture<VBAProviderDto>> futures = providerList.stream()
            .map(this::fetchProviderAsync)
            .collect(Collectors.toList());
        
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

        try {
            allOf.join(); 
        } catch (Exception e) { 
            log.error("Error fetching one or more VBA providers. Cause: {}", e.getCause() != null ? e.getCause().getMessage() : e.getMessage(), e);
            if (e.getCause() instanceof ResponseStatusException rse) throw rse;
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error fetching provider details: " + (e.getCause() != null ? e.getCause().getMessage() : e.getMessage()));
        }

        return futures.stream()
            .map(CompletableFuture::join) 
            .filter(Objects::nonNull) 
            .collect(Collectors.toList());
    }

    private CompletableFuture<VBAProviderDto> fetchProviderAsync(String providerId) {
        return CompletableFuture.supplyAsync(() -> {
            JsonNode rawData = vbaUserRepository.getProviderById(providerId); 
            JsonNode data = rawData.at(DATA_PATH);
            if (data.isMissingNode() || data.isNull()) { 
                log.warn("fetchProviderAsync: Provider data node is missing or null for providerId: {}", providerId);
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Provider not found: " + providerId);
            }
            TypeReference<VBAProvider> typeRef = new TypeReference<>() {};
            VBAProvider provider = objMapper.convertValue(data, typeRef);
            if (provider == null) { 
                 log.warn("fetchProviderAsync: Provider object is null after conversion for providerId: {}", providerId);
                 throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to convert provider data for ID: " + providerId);
            }
            return VBAProviderConverter.vbaProviderToDto(provider);
        });
    }
    public ProfileCheckResultDto checkUserProfile(ProfileCheckDto profileCheckDto) {
        return vbaUserRepository.checkUser(profileCheckDto);
    }

    public String checkProviderProfiles(ProviderProfileCheckDto providerProfileCheckDto) {
        return vbaUserRepository.checkProvider(providerProfileCheckDto);
    }

    public boolean checkForPrescriptionSearch(String groupId) {
        return vbaUserRepository.checkForPrescriptionSearch(groupId);
    }

    public ProfileInfoDto getProfileBySubscriberId(String subscriberID) {
        return vbaUserRepository.getVBAProfile(subscriberID);
    }
}