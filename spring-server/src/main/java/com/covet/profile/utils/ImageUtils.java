package com.covet.profile.utils;

import com.covet.profile.persistence.model.Picture;
import com.covet.profile.systemEnum.EDefaultForm;

import java.net.URL;
import java.util.*;
import java.util.concurrent.TimeUnit;

public class ImageUtils {

    public static long getDateBetween(Date date1, Date date2) {
        long diffInMilliseconds = Math.abs(date1.getTime() - date2.getTime());
        return TimeUnit.DAYS.convert(diffInMilliseconds, TimeUnit.MILLISECONDS);
    }

    /**
     * @param expiredDays
     * @param image - image from DB
     * @return the imageURL from DB image if date is not expired; otherwise, return null
     */
    public static URL getImageUrl(int expiredDays, Optional<Picture> image) {
        if (image.isPresent()) {
            Date theLastDateGetLink = image.get().getCreatedUrlDate();
            return expiredDays - 1 >= getDateBetween(new Date(), theLastDateGetLink) ?
                    image.get().getImageURL() : null;
        }
        return null;
    }

    public static List<Map<UUID, URL>> getImgURLs(int expiredDays, Optional<List<Picture>> images) {
        List<Map<UUID, URL>> imgURLs = new ArrayList<>();

        if (images.isPresent()) {
            for (Picture image : images.get()) {
                Map<UUID, URL> imgURLMap = new HashMap<>();

                    Date theLastDateGetLink = image.getCreatedUrlDate();
                    URL imgURL = expiredDays - 1 >= getDateBetween(new Date(), theLastDateGetLink) ? image.getImageURL() : null;
                    imgURLMap.put(image.getCognitoId(), imgURL);

                imgURLs.add(imgURLMap);
            }
        }

        return imgURLs;
    }

    /**
     * Helper method to set data for Picture before saving to DB.
     * @return Picture with imageUUID, current time, and image URL
     */
    public static Picture getSavedImage(Optional<Picture> image, UUID imageUUID, URL presignedUrl) {
        Date now = new Date();
        if (image.isPresent()) {
            image.get().setImageURL(presignedUrl);
            image.get().setCreatedUrlDate(now);
            return image.get();
        }
        return new Picture(imageUUID, now, now, null, presignedUrl, now);
    }

    public static String getMatchFormKey(String form) {
        var targetForm = EDefaultForm.OTHERS.name();
        for (var item : EDefaultForm.values()) {
            if (form.contains(item.name().toLowerCase())) {
                targetForm = item.name();
                break;
            }
        }
        return targetForm;
    }
}