package com.covet.profile.persistence.model;

import com.covet.profile.persistence.compositeKey.PrescriptionID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "prescription")

public class Prescription implements Serializable {

    @EmbeddedId
    private PrescriptionID prescriptionId;

    @Column(name = "quantity")
    @NotNull
    private Integer quantity;

    @Column(name = "form")
    @NotNull
    private String form;

    @Column(name = "strength")
    @NotNull
    private String strength;

    @Column(name = "physician_id")
    private UUID physicianID;

    @Column(name = "init_refill_date")
    private Date initRefillDate;

    @Column(name = "next_refill_date")
    private Date nextRefillDate;

    @Column(name = "drug_id")
    @NotNull
    private int drugID;
}


