package com.covet.profile.service;

import com.covet.profile.converter.ProfileConverter;
import com.covet.profile.converter.notification.PrescriptionSchedulerConverter;
import com.covet.profile.converter.notification.response.GetListMedicineNotificationResponse;
import com.covet.profile.dto.PrescriptionSchedulerDto.UpdateNotificationRequest;
import com.covet.profile.dto.covet.profile.ProfileDto;
import com.covet.profile.dto.common.PageRequestParams;
import com.covet.profile.dto.location.ICoordinate;
import com.covet.profile.persistence.model.Picture;
import com.covet.profile.persistence.model.Profile;
import com.covet.profile.persistence.model.RegistrationFCM;
import com.covet.profile.persistence.repository.NotificationRepository;
import com.covet.profile.persistence.repository.PictureRepository;
import com.covet.profile.persistence.repository.ProfileRepository;
import com.covet.profile.persistence.repository.RegistrationFcmRepository;
import com.covet.profile.systemEnum.ETemplateType;
import com.covet.profile.utils.AuthUtils;
import com.covet.profile.utils.ImageUtils;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.io.FilenameUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.*;
import javax.validation.Valid;
import java.net.URL;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProfileService {

    @PersistenceContext
    EntityManager em;

    private final ProfileRepository profileRepository;
    private final PictureRepository pictureRepository;
    private final RegistrationFcmRepository registrationFcmRepository;
    private final NotificationRepository notificationRepository;
    private final S3Service s3Service;

    private final PrescriptionSchedulerConverter prescriptionSchedulerConverter;

    public Optional<ProfileDto> getProfileById(UUID profileId) {
        return profileRepository.findById(profileId).map(profile -> ProfileConverter.profileToProfileDto(profile,
                getAvatarUrl(profileId)));
    }

    public Profile fetchMe() {
        return profileRepository.getReferenceById(AuthUtils.getCognitoId());
    }

    public ProfileDto createOrUpdate(ProfileDto newProfileDto) {
        if (newProfileDto == null) {
            throw new IllegalArgumentException("Profile data cannot be null");
        }
        URL avatarUrl = null;
        Optional<UUID> cognitoId = newProfileDto.getCognitoId();
        if (cognitoId.isPresent()) {
            avatarUrl = getAvatarUrl(cognitoId.get());
        }
        return ProfileConverter
                .profileToProfileDto(profileRepository.save(ProfileConverter.profileDtoToProfile(newProfileDto)),
                        avatarUrl);
    }

    public void deleteProfile(UUID profileId) {
        profileRepository.deleteById(profileId);
    }

    public void uploadImage(@NonNull Picture picture) {
        pictureRepository.save(picture);
    }

    public Optional<Picture> getImage(@NonNull UUID cognitoId) {
        return pictureRepository.findById(cognitoId);
    }

    public URL postAvatar(@NonNull MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("Image file cannot be null or empty");
        }
        String contentType = file.getContentType();
        boolean isImage = contentType != null && contentType.startsWith("image/");

        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        List<String> allowedExtensions = List.of("png", "jpg", "jpeg", "gif", "bmp");

        if (!isImage && !allowedExtensions.contains(extension.toLowerCase())) {
            throw new IllegalArgumentException("Invalid file type. Only image files are allowed");
        }
        UUID cognitoUuid = AuthUtils.getCognitoId();
        String imageUUID = cognitoUuid.toString();
        s3Service.deleteFile(imageUUID);
        s3Service.putFile(file, imageUUID);
        URL imageUrl = s3Service.getPresignedURL(imageUUID);
        pictureRepository
                .save(ImageUtils.getSavedImage(pictureRepository.findById(cognitoUuid), cognitoUuid, imageUrl));
        return imageUrl;
    }

    public List<Profile> saveManyProfiles(List<Profile> profiles) {
        return profileRepository.saveAll(profiles);
    }

    /**
     * @param uuid
     * @return avatarUrl from DB image if not expired; otherwise, return S3
     *         presignedUrl
     */
    private URL getAvatarUrl(@NonNull UUID uuid) {
        URL avatarUrl = ImageUtils.getImageUrl(s3Service.getExpiredDays(), pictureRepository.findById(uuid));
        if (avatarUrl == null) {
            avatarUrl = s3Service.getPresignedURL(uuid.toString());
        }
        return avatarUrl;
    }

    public RegistrationFCM setRegistrationToken(@NonNull String registrationToken) {
        UUID cognitoId = AuthUtils.getCognitoId();
        Profile profile = profileRepository.getReferenceById(cognitoId);
        RegistrationFCM registrationFCM = new RegistrationFCM(cognitoId, registrationToken, profile);
        return registrationFcmRepository.save(registrationFCM);
    }

    public List<Profile> getProfilesByIds(List<UUID> ids) {
        return profileRepository.findAllById(ids);
    }

    public void updateCoordinates(UUID id, Double lat, Double lng) {
        if (lat == null || lng == null) {
            throw new IllegalArgumentException("Latitude and longitude cannot be null");
        }
        if (lat < -90 || lat > 90) {
            throw new IllegalArgumentException("Latitude must be between -90 and 90 degrees");
        }
        if (lng < -180 || lng > 180) {
            throw new IllegalArgumentException("Longitude must be between -180 and 180 degrees");
        }
        profileRepository.updateCoordinate(id, lat, lng);
    }

    public List<Double> calculateDistance(Double lat, Double lng) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Double> cq = cb.createQuery(Double.class);
        Root<Profile> profile = cq.from(Profile.class);

        Expression<Double> x_2 = cb.prod(
                cb.diff(profile.get(Profile.Fields.latitude), lat),
                cb.diff(profile.get(Profile.Fields.latitude), lat));

        Expression<Double> y_2 = cb.prod(
                cb.diff(profile.get(Profile.Fields.longitude), lng),
                cb.diff(profile.get(Profile.Fields.longitude), lng));

        Order sorter = cb.asc(cb.sum(x_2, y_2));
        cq.select(cb.sum(x_2, y_2));
        cq.orderBy(sorter);

        return em.createQuery(cq).getResultList().stream().limit(10).toList();
    }

    @Transactional(readOnly = true)
    public GetListMedicineNotificationResponse getDailyPrescriptionScheduler(@Valid PageRequestParams pageRequest) {

        UUID cognitoId = AuthUtils.getCognitoId();
        Pageable page = PageRequest.of(pageRequest.getPageNumber(), pageRequest.getPageSize());

        var notificationPage = notificationRepository.filterDailyMedicineNotification(cognitoId,
                ETemplateType.MEDICINE, page);

        var totalTaken = notificationRepository.countTaken(cognitoId, ETemplateType.MEDICINE);
        var totalRemain = notificationRepository.countRemain(cognitoId, ETemplateType.MEDICINE);
        var overdue = notificationRepository.countOverdue(cognitoId, ETemplateType.MEDICINE);

        return GetListMedicineNotificationResponse.builder()
                .notificationPage(notificationPage.map(prescriptionSchedulerConverter::convertToMedicineTemplateRes))
                .totalOverdue(overdue)
                .totalRemain(totalRemain)
                .totalTaken(totalTaken)
                .build();

    }

    @Transactional
    public void switchModeNotificationScheduler(UpdateNotificationRequest body) {
        var notification = notificationRepository.getReferenceById(body.getNotificationId());
        if (Objects.nonNull(body.getIsActive())) {
            notification.setIsActive(body.getIsActive());
        }
        if (Objects.nonNull(body.getIsRead())) {
            notification.setIsRead(body.getIsRead());
        }
        notificationRepository.save(notification);
    }

    @Transactional
    public ICoordinate fetchMeCoordinate() {
        UUID cognitoId = AuthUtils.getCognitoId();
        return profileRepository.getCoordinate(cognitoId);
    }
}