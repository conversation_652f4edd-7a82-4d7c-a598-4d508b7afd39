package com.covet.profile.converter;

import com.covet.profile.dto.AddressDto;
import com.covet.profile.dto.ImmutableAddressDto;
import com.covet.profile.persistence.model.Address;

import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

public class AddressConverter {

    private AddressConverter() {
    }

    public static Set<AddressDto> addressDtoSet(Set<Address> addressSet) {
        return addressSet.stream().map(address -> addressToDto(address)).collect(Collectors.toSet());
    }

    public static AddressDto addressToDto(Address address) {
        return ImmutableAddressDto.of(
                Optional.ofNullable(address.getId()),
                address.getLine1(),
                Optional.ofNullable(address.getLine2()),
                Optional.ofNullable(address.getCity()),
                Optional.ofNullable(address.getState()),
                Optional.ofNullable(address.getZipCode()),
                Optional.ofNullable(address.getCountry()),
                Optional.ofNullable(address.getDescription())
        );
    }

    public static Set<Address> dtoToAddressSet(Set<AddressDto> addressDtoSet ) {

        return addressDtoSet.stream().map(dto -> dtoToAddress(dto)).collect(Collectors.toSet());
    }

    public static Address dtoToAddress(AddressDto addressDto ) {
        return new Address(
                addressDto.getId().orElse(null),
                addressDto.getLine1(),
                addressDto.getLine2().orElse(null),
                addressDto.getCity().orElse(null),
                addressDto.getState().orElse(null),
                addressDto.getZipCode().orElse(null),
                addressDto.getCountry().orElse(null),
                addressDto.getDescription().orElse(null),
                null
        );
    }
}
