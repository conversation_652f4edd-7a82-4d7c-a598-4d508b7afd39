package com.covet.profile.persistence.repository;

import com.covet.profile.persistence.model.Drug;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.UUID;

public interface DrugRepository extends JpaRepository<Drug, UUID> {
    // 20 is an arbitrary limit as to not overflow the ResultSet
    @Query(value = """
        with scored_drugs as (
            select d.brand_name as brand_name,
                    case
                        when d.brand_name = :textSearch then 0
                        else 1
                    end as priority,
                    similarity(d.brand_name, :textSearch) as score
            from drug d
            where d.brand_name ilike concat('%', :textSearch, '%') or d.brand_name = :textSearch
            limit 20
        )
        select brand_name
        from (
            select distinct on (brand_name) brand_name, priority, score
            from scored_drugs sc
            order by brand_name, priority, score desc
        ) as sorted_drugs
        order by priority, score desc, brand_name
        """, nativeQuery = true)
    Page<String> getDrugString(@Param("textSearch") String textSearch, Pageable page);
}
