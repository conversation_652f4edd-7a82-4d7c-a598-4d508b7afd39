package com.covet.profile.responses;

import java.util.List;

public class PriorAuthorPendingResponse {

    private long totalPendingStatus;
    private List<PriorAuthorResponse> priorAuthorList;

    public PriorAuthorPendingResponse() {
    }

    public PriorAuthorPendingResponse(long totalPendingStatus, List<PriorAuthorResponse> priorAuthorList) {

        this.totalPendingStatus = totalPendingStatus;
        this.priorAuthorList = priorAuthorList;
    }

    public long getTotalPendingStatus() {
        return this.totalPendingStatus;
    }

    public void setTotalPendingStatus(long totalPendingStatus) {
        this.totalPendingStatus = totalPendingStatus;
    }

    public List<PriorAuthorResponse> getPriorAuthorList() {
        return this.priorAuthorList;
    }

    public void setPriorAuthorList(List<PriorAuthorResponse> priorAuthorList) {
        this.priorAuthorList = priorAuthorList;
    }

}
