package com.covet.profile.persistence.model;

import com.covet.profile.systemEnum.ETemplateType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "notification_template")
public class NotificationTemplate implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "id")
    private UUID templateId;

    @Column(name = "template_type")
    @NotNull
    @Enumerated(EnumType.STRING)
    private ETemplateType templateType;

    @Column(name = "route")
    @NotNull
    private String route;

    @Column(name = "target_id")
    private UUID targetId;

    @Column(name = "notification_info")
    @NotNull
    private String notificationInfo;

    @JsonIgnore
    @OneToMany(mappedBy = "template", cascade = CascadeType.REMOVE) // fetch = FetchType.LAZY by default
    private List<Notification> notificationList = new ArrayList<>();

}
