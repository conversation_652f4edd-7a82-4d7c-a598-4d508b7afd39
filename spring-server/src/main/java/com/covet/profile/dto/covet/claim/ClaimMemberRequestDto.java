package com.covet.profile.dto.covet.claim;

import java.util.List;
import java.util.Optional;

import org.immutables.value.Value;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonSerialize(as = ImmutableClaimMemberRequestDto.class)
@JsonDeserialize(as = ImmutableClaimMemberRequestDto.class, builder = ImmutableClaimMemberRequestDto.Builder.class)
@Value.Immutable
public interface ClaimMemberRequestDto {
    @Value.Parameter
    @JsonProperty
    Optional<Integer> getPageSize();

    @Value.Parameter
    @JsonProperty
    Optional<Integer> getPage();

    @Value.Parameter
    @JsonProperty
    List<String> getMemberSequences();
}