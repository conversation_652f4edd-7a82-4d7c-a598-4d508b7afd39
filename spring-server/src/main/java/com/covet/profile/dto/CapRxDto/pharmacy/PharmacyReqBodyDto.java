package com.covet.profile.dto.CapRxDto.pharmacy;

import java.util.List;

import com.covet.profile.dto.CapRxDto.pharmacy.DistanceReqBodyDto;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class PharmacyReqBodyDto {

    @JsonProperty("all_pharmacies")
    String allPharmacy;

    @JsonProperty("order_by")
    List<DistanceReqBodyDto> orderBy;

    @JsonProperty("network_ids")
    List<Integer> networkIds;

    @JsonProperty("closest_to_address")
    String closestToAddress;

    @JsonProperty("page_number")
    int pageNumber;

    @JsonProperty("results_per_page")
    int resultsPerPage;

    @JsonProperty("latitude")
    String userLatitude;

    @JsonProperty("longitude")
    String userLongitude;


}
