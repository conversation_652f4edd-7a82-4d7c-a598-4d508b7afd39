package com.covet.profile.persistence.repository;

import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.covet.profile.persistence.model.Scheduler;

public interface SchedulerRepository extends JpaRepository<Scheduler, UUID> {
    @Query(value = """
            select s
            from Scheduler s
            join fetch s.notificationList nl
            join fetch nl.template
            """, countQuery = """
            select count(distinct s)
            from Scheduler s
            join s.notificationList nl
            join nl.template
            """)
    Page<Scheduler> findAllSchedulers(Pageable page);

    @Query("""
            select s
            from Scheduler s
            join fetch s.notificationList nl
            join fetch nl.template
            where s.schedulerId = :schedulerId
            """)
    Scheduler findAllNotificationsBySchedulerId(@Param("schedulerId") UUID schedulerId);
}
