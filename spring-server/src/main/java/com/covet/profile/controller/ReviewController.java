package com.covet.profile.controller;

import com.covet.profile.dto.covet.provider.GroupProviderDto;
import com.covet.profile.dto.covet.provider.PhysicianDto;
import com.covet.profile.dto.covet.provider.review.CustomReviewDto;
import com.covet.profile.dto.covet.provider.review.ImmutableReviewDto;
import com.covet.profile.dto.covet.provider.review.ReviewDetailDto;
import com.covet.profile.dto.covet.provider.review.ReviewDto;
import com.covet.profile.persistence.model.Review;
import com.covet.profile.searchCriteria.GroupReviewSearchCriteria;
import com.covet.profile.searchCriteria.PhysicianReviewSearchCriteria;
import com.covet.profile.service.GroupService;
import com.covet.profile.service.PhysicianService;
import com.covet.profile.service.ReviewService;
import com.covet.profile.utils.AuthUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Optional;
import java.util.UUID;



@Validated
@RestController
@RequestMapping("/api")
public class ReviewController {
    public static final Logger log = LoggerFactory.getLogger(ReviewController.class);
    private final PhysicianService physicianService;

    private final ReviewService reviewService;

    private final GroupService groupService;



    @Autowired
    public ReviewController(PhysicianService physicianService, ReviewService reviewService, GroupService groupService){

        this.physicianService = physicianService;
        this.reviewService = reviewService;
        this.groupService = groupService;
    }

    // @PreAuthorize("hasRole('patient') || hasRole('physician')")
    @GetMapping("/{physicianId}/reviews/")
    public ResponseEntity<Page<CustomReviewDto>> getAllReviewOfOnePhysician(
            @Valid @ParameterObject PhysicianReviewSearchCriteria criteria,
            @PathVariable("physicianId") UUID physicianId, Optional<Integer> page, Optional<Integer> pageSize) {
        Optional<PhysicianDto> physicianDto = physicianService.getPhysicianById(physicianId);
        Page<CustomReviewDto> foundReviews;
        if (!physicianDto.isPresent()) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        if (page.isPresent() && pageSize.isPresent()) {
            foundReviews = reviewService.getReviewsWithFilter(criteria, page.get(), pageSize.get());
        } else if (page.isPresent() || pageSize.isPresent()) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } else {
            foundReviews = reviewService.getAllReviewByPhysicianId(physicianId);
        }
        return new ResponseEntity<>(foundReviews, HttpStatus.OK);
    }

    // @PreAuthorize("hasRole('patient') || hasRole('physician')")
    @GetMapping("/{groupId}/group-reviews/")
    public ResponseEntity<Page<CustomReviewDto>> getAllReviewOfOneGroup(
            @Valid @ParameterObject GroupReviewSearchCriteria criteria, @PathVariable("groupId") UUID groupId,
            Optional<Integer> page, Optional<Integer> pageSize) {
        Optional<GroupProviderDto> groupDto = groupService.getGroupById(groupId);
        if (!groupDto.isPresent()) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        Page<CustomReviewDto> foundReviews;
        if (page.isPresent() && pageSize.isPresent()) {
            foundReviews = reviewService.getGroupReviewsWithFilter(criteria, page.get(), pageSize.get(), groupId);
        }
        else if (page.isPresent() || pageSize.isPresent()) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } else {
            foundReviews = reviewService.getAllReviewByGroupId(groupId);
        }
        return new ResponseEntity<>(foundReviews, HttpStatus.OK);
    }

    @GetMapping("{reviewId}/review-detail/")
    public ResponseEntity<ReviewDetailDto> getReviewDetail(@PathVariable UUID reviewId) {
        Optional<ReviewDetailDto> review = reviewService.getReviewDetail(reviewId);

        return review.map(r ->
            new ResponseEntity<>(r, HttpStatus.OK)).orElseGet(() -> {
            log.error("ReviewId {} doesn't exist", reviewId);
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        });
    }

    // @PreAuthorize("hasAuthority('read:patientdata')")
    @PostMapping(path = "/patient/review")
    public  ResponseEntity<Review> createReview(
            @Valid @RequestBody final ReviewDto reviewDto){
        UUID patientId = AuthUtils.getCognitoId();
        var reviewPhysicianId = reviewDto.getPhysicianId();
        if (reviewPhysicianId.isPresent()) {
            return physicianService.getPhysicianById(reviewPhysicianId.get())
                    .map(physicianDto -> {
                        Review review = reviewService.createOrUpdateReview(patientId, ImmutableReviewDto.copyOf(reviewDto));
                        return new ResponseEntity<>( review, HttpStatus.CREATED);
                    })
                    .orElseGet(() -> {
                        log.error("PhysicianId {} doesn't exist for creating review", reviewDto.getPhysicianId());
                        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
                    });
        } else {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
    }

    // @PreAuthorize("hasAuthority('read:patientdata')")
    @PostMapping(path = "/patient/group-review")
    public  ResponseEntity<Review> createGroupReview(
            @Valid @RequestBody final ReviewDto reviewDto){
        UUID patientId = AuthUtils.getCognitoId();
        var reviewGroupId = reviewDto.getGroupId();
        if (reviewGroupId.isPresent()) {
            return groupService.getGroupById(reviewGroupId.get())
                    .map(physicianDto -> {
                        Review review = reviewService.createOrUpdateReview(patientId, ImmutableReviewDto.copyOf(reviewDto));
                        return new ResponseEntity<>( review, HttpStatus.CREATED);
                    })
                    .orElseGet(() -> {
                        log.error("Group {} doesn't exist for creating review", reviewDto.getGroupId());
                        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
                    });
        } else {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
    }

    // @PreAuthorize("hasAuthority('read:patientdata')")
    @DeleteMapping("/patient/review/{reviewId}")
    public ResponseEntity<Review> deleteReview(@PathVariable("reviewId") UUID reviewId) {
        Optional<Review> review = reviewService.getReviewById(reviewId);
        return review.map(r -> {
            reviewService.deleteReview(r.getId());
            return new ResponseEntity<>(r, HttpStatus.OK);
        }).orElseGet(() -> {
            log.error("ReviewId {} doesn't exist for delete", reviewId);
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        });
    }

    // @PreAuthorize("hasAuthority('read:patientdata')")
    @PostMapping(path = "/patient/update-review-status")
    public ResponseEntity updateReviewStatus(@RequestParam UUID reviewId, @RequestParam boolean isHelpful){
        return reviewService.getReviewById(reviewId)
                .map(review -> {
                    reviewService.updateReviewHelpfulStatus(reviewId, isHelpful);
                    return new ResponseEntity<>(HttpStatus.OK);
                })
                .orElseGet(() -> {
                    log.error("Review doesn't exist for update ");
                    return new ResponseEntity<>(HttpStatus.NOT_FOUND);
                });
    }
}