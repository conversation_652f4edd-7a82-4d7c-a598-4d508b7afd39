package com.covet.profile.clients.vba.VBADtos.Financial;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.util.Map;

@JsonSerialize(as = ImmutableFinancialItemDto.class)
@JsonDeserialize(as = ImmutableFinancialItemDto.class, builder = ImmutableFinancialItemDto.Builder.class)
@Value.Immutable
public interface FinancialItemDto {
    @Value.Parameter
    @JsonProperty
    String getPlanID();

    @Value.Parameter
    @JsonProperty
    Map<String, FinancialValuesDto> getAccum();

    @Value.Parameter
    @JsonProperty
    Map<String, FinancialValuesDto> getIndividual();

    @Value.Parameter
    @JsonProperty
    Map<String, FinancialValuesDto> getFamily();
}
