
package com.covet.profile.persistence.repository;

import com.covet.profile.persistence.compositeKey.PreferredPharmacyID;
import com.covet.profile.persistence.model.PreferredPharmacy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface PreferredPharmacyRepository
                extends JpaRepository<PreferredPharmacy, PreferredPharmacyID>,
                JpaSpecificationExecutor<PreferredPharmacy> {

    Page<PreferredPharmacy> findAll(Pageable pageable);

    Page<PreferredPharmacy> findByPreferredPharmacyID_CognitoId(UUID patientId, Pageable pageable);


    @Query(value =  "select * from preferred_pharmacy " +
                    "where pharmacy_id in :pharmacyIds", nativeQuery = true)
    Optional<List<PreferredPharmacy>> findPreferredPharmaciesByIds(@Param("pharmacyIds") List<String> pharmacyIds);

    @Query(value =  "select * from preferred_pharmacy " +
            "where cognito_id = :patientId", nativeQuery = true)
    Optional<PreferredPharmacy> findPreferredPharmaciesByPatientId(@Param("patientId") UUID patientId);

    @Query(value = "select count(*) from preferred_pharmacy " +
            "where cognito_id = :patientId", nativeQuery = true)
    Long countPreferredPharmaciesByPatientId(@Param("patientId") UUID patientId);
}
