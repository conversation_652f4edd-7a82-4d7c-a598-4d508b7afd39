package com.covet.profile.converter;

import com.covet.profile.dto.covet.provider.GroupPhysicianDto;
import com.covet.profile.dto.covet.provider.ImmutableGroupPhysicianDto;
import com.covet.profile.persistence.model.GroupPhysician;
import lombok.NoArgsConstructor;

import java.util.Optional;

@NoArgsConstructor
public class GroupPhysicianConverter {
    public static GroupPhysicianDto groupPhysicianToDto(GroupPhysician groupPhysician) {
        return ImmutableGroupPhysicianDto.of(
                groupPhysician.getId(),
                groupPhysician.getCreatedDate(),
                groupPhysician.getUpdatedDate(),
                groupPhysician.getIsInGroup(),
                Optional.ofNullable(groupPhysician.getGroup()),
                Optional.ofNullable(groupPhysician.getPhysician())
        );
    }

    public static GroupPhysician dtoToGroupPhysician(GroupPhysicianDto dto) {
        return new GroupPhysician(
                dto.getGroupPhysicianId(),
                dto.getGroup().orElse(null),
                dto.getPhysician().orElse(null),
                dto.getCreatedDate(),
                dto.getUpdatedDate(),
                dto.getIsInGroup()
        );
    }
}
