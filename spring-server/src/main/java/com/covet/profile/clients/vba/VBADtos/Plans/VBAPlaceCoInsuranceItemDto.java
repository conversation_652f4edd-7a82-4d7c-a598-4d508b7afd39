package com.covet.profile.clients.vba.VBADtos.Plans;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

@JsonSerialize(as = ImmutableVBAPlaceCoInsuranceItemDto.class)
@JsonDeserialize(as = ImmutableVBAPlaceCoInsuranceItemDto.class, builder = ImmutableVBAPlaceCoInsuranceItemDto.Builder.class)
@Value.Immutable
public interface VBAPlaceCoInsuranceItemDto {
    @Value.Parameter
    @JsonProperty("plan_ID")
    String getPlanID();

    @Value.Parameter
    @JsonProperty("benefit_Code")
    String getBenefitCode();

    @Value.Parameter
    @JsonProperty("place_Code")
    String getPlaceCode();

    @Value.Parameter
    @JsonProperty("co_Insurance")
    String getCoInsurance();

    @Value.Parameter
    @JsonProperty("co_Insurance_Net")
    String getCoInsuranceNet();
}
