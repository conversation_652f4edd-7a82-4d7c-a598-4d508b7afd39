package com.covet.profile.persistence.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sun.istack.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.*;
import java.util.Date;
import java.util.UUID;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "claim")
public class Claim implements Serializable {
    @Id
    @Column(name = "claim_code")
    private String claimCode;

    @Column(name = "patient_id")
    @NotNull
    private UUID patientId;

    @Column(name = "physician_id")
    @NotNull
    private UUID physicianId;

    @Column(name = "is_paid")
    @ColumnDefault(value = "false")
    private Boolean isPaid;

    @Temporal(TemporalType.TIMESTAMP)
    @NotNull
    @Column(name = "created_date")
    private Date createdDate;

    @Column(name = "total_claim")
    @ColumnDefault(value = "0")
    private Double totalClaim;

    @Column(name = "claim_type")
    @ColumnDefault(value = "0")
    private int claimType;

    @JsonIgnore
    @ManyToOne
    @MapsId("patientId")
    @JoinColumn(name = "patient_id")
    @NotNull
    private Profile patient;

    @JsonIgnore
    @ManyToOne
    @MapsId("physicianId")
    @JoinColumn(name = "physician_id")
    @NotNull
    private Physician physician;
}
