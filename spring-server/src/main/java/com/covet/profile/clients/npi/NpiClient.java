package com.covet.profile.clients.npi;

import com.covet.profile.clients.npi.response.NpiResponse;
import com.covet.profile.config.NpiClientConfig;
import com.covet.profile.clients.npi.request.NpiRequestParams;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(
        value = "npiClient",
        url = "https://npiregistry.cms.hhs.gov/api",
        configuration = NpiClientConfig.class
)
public interface NpiClient {
    @GetMapping()
    NpiResponse getNpiInfo(@SpringQueryMap NpiRequestParams request);
}
