package com.covet.profile.controller;

import com.covet.profile.clients.cigna.response.location.LocationResponse;
import com.covet.profile.clients.cigna.response.organization.OrganizationResponse;
import com.covet.profile.dto.ProviderDto.LocationDto;
import com.covet.profile.dto.covet.provider.*;
import com.covet.profile.dto.response.FilterResponse;
import com.covet.profile.searchCriteria.GroupProviderSearchCriteria;
import com.covet.profile.searchCriteria.PhysicianProviderSearchCriteria;
import com.covet.profile.service.CignaService;
import com.covet.profile.service.CoordinateService;
import com.covet.profile.service.GroupService;
import com.covet.profile.service.PhysicianService;
import com.covet.profile.utils.AuthUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Nullable;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

// @PreAuthorize("hasRole('patient') || hasRole('physician')")
@Validated
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@Slf4j
public class ProviderController implements CommandLineRunner {
    private final PhysicianService physicianService;
    private final GroupService groupService;
    private final CignaService cignaService;
    private final CoordinateService coordinateService;

    @Value("${spring.third-party.metabase.key}")
    private String metabaseSecretKey;


    @Operation(summary = "Create Group Provider - No need to pass field groupId and groupRating")
    @PostMapping(path = "/provider/group-provider", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<GroupProviderDto> createGroupProvider(@Valid @RequestBody final GroupProviderDto newGroupDto) {
        UUID physicianId = AuthUtils.getCognitoId();
        return physicianService.getPhysicianById(physicianId).map(physician ->
                new ResponseEntity<>(
                        groupService.createOrUpdateGroupProvider(newGroupDto),
                        HttpStatus.CREATED)
        ).orElseGet(() -> {
            log.error("You must be physician to create Physician Group");
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        });
    }

    @Operation(summary = "Add physician to group - No need to pass field physician and group")
    @PostMapping(path = "/provider/physician-group", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<GroupPhysicianDto> addPhysicianToGroup(@Valid @RequestBody final GroupPhysicianDto groupPhysicianDto) {
        return new ResponseEntity<>(groupService.createOrUpdateGroupPhysician(groupPhysicianDto), HttpStatus.CREATED);
    }

    @GetMapping(path = "/provider/physician-provider")
    public ResponseEntity<?> getPhysicianProviderWithFilter(
            @Valid @ParameterObject PhysicianProviderSearchCriteria criteria, @Nullable Integer page,
            @Nullable Integer pageSize, @ParameterObject LocationDto locationDto) throws Exception {
        return physicianService.getPhysicianProvidersWithFilter(page, pageSize, criteria, locationDto);
    }

    @GetMapping(path = "/provider/physician-provider/{id}/detail")
    public ResponseEntity<ProviderDetailDto> getProviderDetail(
            @PathVariable("id") UUID physicianId,
            @Parameter String npi,
            @ParameterObject LocationDto location) {
        ProviderDetailDto result = physicianService.getProviderDetail(physicianId, npi, location);
        return new ResponseEntity<>((result), HttpStatus.OK);
    }

    @GetMapping(path = "/provider/group-provider/{id}/detail")
    public ResponseEntity<GroupProviderDetailDto> getGroupProviderDetail(
            @PathVariable("id") UUID groupId,
            @Parameter String npi,
            @ParameterObject LocationDto location) {
        GroupProviderDetailDto result = groupService.getGroupProviderDetail(groupId, npi, location);
        return new ResponseEntity<>((result), HttpStatus.OK);
    }

    @GetMapping(path = "/provider/group-provider")
    public ResponseEntity<Page<GroupProviderDto>> getGroupProvidersWithFilter(@Valid @ParameterObject GroupProviderSearchCriteria criteria,
            @Nullable Integer page, @Nullable Integer pageSize, @ParameterObject LocationDto location) {
        return new ResponseEntity<>(groupService.getGroupProvidersWithFilter(page, pageSize, criteria, location),
                HttpStatus.OK);
    }

    @GetMapping(path = "/provider/filter-options")
    @ResponseStatus(HttpStatus.OK)
    public FilterResponse GetProviderFilterOptions() {
        var providerFilter = physicianService.getProviderFilterOptions();
        var groupProviderFilter = groupService.getGroupProviderFilterOptions();
        return FilterResponse.builder()
                .groupProvider(groupProviderFilter)
                .provider(providerFilter)
                .build();
    }

    @GetMapping(path = "/provider/metabase/key")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<String> getMetabaseKey() {
        return ResponseEntity.ok(metabaseSecretKey);
    }


    @GetMapping(path = "/provider/cigna-practitioner")
    @ResponseStatus(HttpStatus.OK)
    public List<PhysicianDetailDto> getPratitioner(@Valid @ParameterObject PhysicianProviderSearchCriteria criteria) {
        return cignaService.getCignaProvider("");
    }


    @GetMapping(path = "/provider/cigna-location")
    @ResponseStatus(HttpStatus.OK)
    public LocationResponse getLocation() {
        return cignaService.getLocationTest();
    }

    @GetMapping(path = "/provider/cigna-organization")
    @ResponseStatus(HttpStatus.OK)
    public OrganizationResponse getOrganization() {
        return cignaService.getOrganization();
    }

    @Override
    public void run(String... args) {
//        cignaService.readFile();
    }

//    public void run(String... args) {
//        coordinateService.setAddressCoordinates();
//    }
}

