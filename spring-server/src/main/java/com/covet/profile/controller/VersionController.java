package com.covet.profile.controller;

import com.covet.profile.persistence.model.AppVersion;
import com.covet.profile.service.AppVersionService;

import java.util.Optional;

import javax.validation.Valid;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/version")
public class VersionController {
    private final AppVersionService service;

    public VersionController(AppVersionService service) {
        this.service = service;
    }

    @PostMapping
    public ResponseEntity<AppVersion> createVersion(@Valid @RequestBody AppVersion appVersion) {
        AppVersion savedVersion = service.saveVersion(appVersion);
        return ResponseEntity.ok(savedVersion);
    }

    @GetMapping("/latest")
    public ResponseEntity<AppVersion> getLatestVersion() {
        Optional<AppVersion> latestVersion = service.getLatestVersion();
        return latestVersion.map(ResponseEntity::ok)
                           .orElseGet(() -> ResponseEntity.notFound().build());
    }
}
