package com.covet.profile.utils;


import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueRequest;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueResponse;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class SecretManagerUtils {

    @Value("${spring.cloud.aws.s3.region}")
    private String region;

    private SecretsManagerClient secretsManagerClient;
    private final ObjectMapper objectMapper = new ObjectMapper();

    // Cache to store fetched secrets
    private final Map<String, String> secretsCache = new ConcurrentHashMap<>();

    @PostConstruct
    private void init() {
        this.secretsManagerClient = SecretsManagerClient.builder()
                .region(Region.of(region))
                .build();
    }

    public Map<String, String> getSecrets(String secretName) throws Exception {
        // Check if secret is in the cache
        if (secretsCache.containsKey(secretName)) {
            return objectMapper.readValue(secretsCache.get(secretName), new TypeReference<Map<String, String>>() {});
        }

        // Fetch from AWS Secrets Manager
        GetSecretValueRequest getSecretValueRequest = GetSecretValueRequest.builder()
                .secretId(secretName)
                .build();

        GetSecretValueResponse valueResponse = secretsManagerClient.getSecretValue(getSecretValueRequest);
        String secretString = valueResponse.secretString();

        // Store in cache
        secretsCache.put(secretName, secretString);

        return objectMapper.readValue(secretString, new TypeReference<Map<String, String>>() {});
    }
}
