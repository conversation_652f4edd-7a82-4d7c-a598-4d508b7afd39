package com.covet.profile.persistence.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

import javax.persistence.*;


import com.sun.istack.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "external_provider_facility")
public class ExternalProvider {

    @Id
    @Column(name = "id")
    @NotNull
    private UUID id;

    @Column(name = "npi")
    @NotNull
    private String npi;

    @Column(name = "primary_specialty")
    private String primarySpecialty;

    @Column(name = "primary_facility_name")
    private String primaryFacilityName;

    @Column(name = "facility_npi")
    private String facilityNpi;

    @Column(name = "city")
    @NotNull
    private String city;

    @Column(name = "state")
    @NotNull
    private String state;

    @Column(name = "value")
    @NotNull
    private Double value;

    @Column(name = "quality")
    @NotNull
    private Double quality;

    @Column(name = "efficiency")
    @NotNull
    private Double efficiency;

}
