package com.covet.profile.clients.vba.VBARepository;

import com.covet.profile.clients.vba.VBAModels.EnrollmentSubscriber;
import com.covet.profile.dto.covet.profile.ProfileCheckDto;
import com.covet.profile.dto.covet.profile.ProfileInfoDto;
import com.covet.profile.clients.vba.VBADtos.Member.SubscriberMemberDto;
import com.covet.profile.dto.ProviderProfileCheckDto;
import com.covet.profile.dto.VbaDto.ProfileCheckResultDto;
import com.covet.profile.persistence.model.GroupPhysician;
import com.covet.profile.persistence.model.Network;
import com.covet.profile.persistence.model.Physician;
import com.covet.profile.persistence.model.PhysicianRating;
import com.covet.profile.persistence.model.Profile;
import com.covet.profile.persistence.model.Specialty;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.util.concurrent.RateLimiter;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.web.client.RestTemplate;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@Repository
@Slf4j
public class VBAUserRepository {
    private final JdbcTemplate jdbcTemplate;
    private final RestTemplate vbaRestTemplate;
    private final String domain;
    private final SimpleDateFormat formatter = new SimpleDateFormat("yyyy-M-dd hh:mm:ss");
    private final RateLimiter externalServiceRateLimiter;

    private static final String COLUMN_PROVIDER_ID = "Provider_ID";

    private static final String COLUMN_SUBSCRIBER_ID = "Subscriber_ID";

    private static final String COLUMN_LAST_NAME = "Last_Name";

    private static final String COLUMN_FIRST_NAME = "First_Name";

    private static final String COLUMN_MIDDLE_NAME = "Middle_Name";

    private static final String COLUMN_ENTRY_DATE = "Entry_Date";

    private static final String COLUMN_UPDATE_DATE= "Update_Date";

    private static final String COLUMN_ADDRESS= "Address";

    private static final String COLUMN_SOC_SEC_NUMBER = "Soc_Sec_Number";

    private static final String COLUMN_STATE= "State";

    private static final String COLUMN_ZIP_CODE = "Zip_Code";

    private static final String COLUMN_SPECIALTIES = "Specialties";

    private static final String SELECT = "SELECT ";

    public VBAUserRepository(@Qualifier("vbaDataSource") DataSource dataSource,
                             @Qualifier("vbaRestTemplate") RestTemplate vbaRestTemplate,
                             @Value("${spring.third-party.vba.domain}") String domain,
                             RateLimiter externalServiceRateLimiter) {
        this.jdbcTemplate = new JdbcTemplate(dataSource);
        this.vbaRestTemplate = vbaRestTemplate;
        this.domain = domain;
        this.externalServiceRateLimiter = externalServiceRateLimiter;
    }

    private Profile createProfileCovet(ResultSet rs, boolean isProvider) throws SQLException {

        String idColumn = isProvider ? COLUMN_PROVIDER_ID : COLUMN_SUBSCRIBER_ID;
        UUID cognitoId = UUID.nameUUIDFromBytes(rs.getString(idColumn).getBytes());
        String firstName = rs.getString(COLUMN_FIRST_NAME);
        String middleName = rs.getString(COLUMN_MIDDLE_NAME);
        String lastName = rs.getString(COLUMN_LAST_NAME);
        Date createdDate = new Date();
        Date updatedDate = new Date();
        try {
            createdDate = formatter.parse(rs.getString(COLUMN_ENTRY_DATE));
            updatedDate = formatter.parse(rs.getString(COLUMN_UPDATE_DATE));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        String socialSecurityNumber = rs.getString(COLUMN_SOC_SEC_NUMBER) != null ? rs.getString(COLUMN_SOC_SEC_NUMBER)
                : "***********";
        Date dateOfBirth = null;
        String address = rs.getString(COLUMN_ADDRESS);
        String addressLine = null;
        String city = rs.getString("City");
        String state = rs.getString(COLUMN_STATE);
        String zipCode = rs.getString(COLUMN_ZIP_CODE);
        Double latitude = 0.0;
        Double longitude = 0.0;

        return new Profile(cognitoId,
                firstName,
                middleName,
                lastName,
                createdDate,
                updatedDate,
                socialSecurityNumber,
                dateOfBirth,
                address,
                addressLine,
                city,
                state,
                zipCode,
                latitude,
                longitude);
    }

    private Physician createPhysicianCovet(Profile profile, ResultSet rs) throws SQLException {
        UUID physicianId = UUID.nameUUIDFromBytes(rs.getString(COLUMN_PROVIDER_ID).getBytes());
        Boolean isAcceptNewPatient = rs.getBoolean("Accepting_Patients");
        Boolean isVirtual = false;
        String specialty = rs.getString(COLUMN_SPECIALTIES) != null ? rs.getString(COLUMN_SPECIALTIES) : "Other";
        String address = rs.getString(COLUMN_ADDRESS) != null ? rs.getString(COLUMN_ADDRESS) : "string";
        String addressLine = null;
        String city = rs.getString("City");
        String state = rs.getString(COLUMN_STATE);
        String zipCode = rs.getString(COLUMN_ZIP_CODE);
        String npi = "";
        String facilityName = "";
        Double value = 0.0;
        Double quality = 0.0;
        Double efficiency = 0.0;
        String source = "vba";
        List<GroupPhysician> groups = null;
        PhysicianRating physicianRating = null;

        //**CH-196: temporary set to null to run initial model
        List<Specialty> specialties = null;
        Set<Network> networks = null;
        //CH-196**

        return new Physician(physicianId,
                isAcceptNewPatient,
                isVirtual,
                address,
                addressLine,
                city,
                state,
                zipCode,
                npi,
                value,
                quality,
                efficiency,
                source,
                facilityName,
                profile,
                groups,
                physicianRating,
                specialties,
                networks
                );
    }

    public List<Physician> getAllProviders() {
        List<String> columns = new ArrayList<>();
        // columns for profile
        columns.add(COLUMN_ADDRESS);
        columns.add("City");
        columns.add(COLUMN_STATE);
        columns.add(COLUMN_ZIP_CODE);
        columns.add(COLUMN_FIRST_NAME);
        columns.add(COLUMN_MIDDLE_NAME);
        columns.add(COLUMN_LAST_NAME);
        columns.add(COLUMN_ENTRY_DATE);
        columns.add(COLUMN_UPDATE_DATE);
        columns.add(COLUMN_SOC_SEC_NUMBER);
        columns.add("Birth_Date");
        // additional for physician
        columns.add(COLUMN_PROVIDER_ID);
        columns.add("Accepting_Patients");
        columns.add(COLUMN_SPECIALTIES);

        String selectedColumns = String.join(", ", columns);
        String sql = SELECT + selectedColumns + " FROM Provider";
        return jdbcTemplate.query(sql,
                (rs, rowNum) -> {
                    Profile profile = createProfileCovet(rs, true);
                    return createPhysicianCovet(profile, rs);
                });
    }

    public List<Profile> getAllSubscribers() {
        List<String> columns = new ArrayList<>();
        columns.add(COLUMN_SUBSCRIBER_ID);
        columns.add(COLUMN_FIRST_NAME);
        columns.add(COLUMN_MIDDLE_NAME);
        columns.add(COLUMN_LAST_NAME);
        columns.add(COLUMN_ENTRY_DATE);
        columns.add(COLUMN_UPDATE_DATE);
        columns.add(COLUMN_ADDRESS);
        columns.add("City");
        columns.add(COLUMN_STATE);
        columns.add(COLUMN_ZIP_CODE);
        String selectedColumns = String.join(", ", columns);
        String sql = SELECT + selectedColumns + " FROM Subscribers s\n";
        return jdbcTemplate.query(sql,
                (rs, rowNum) -> createProfileCovet(rs, false));
    }

    public String getSubscriberID(String email) {
        String checkIsSubscriberQuery = "SELECT CASE WHEN EXISTS (SELECT 1 FROM Subscribers WHERE Email = ?) THEN 1 ELSE 0 END";

        Integer result = jdbcTemplate.queryForObject(checkIsSubscriberQuery, Integer.class, email);

        boolean isSubscriber = result != null && result == 1;
        String subscriberID = "";

        if (isSubscriber) {
            String getSubscriberIdQuery = "SELECT Subscriber_ID FROM Subscribers WHERE Email = ?";
            subscriberID = jdbcTemplate.queryForObject(getSubscriberIdQuery, String.class, email);
        }

        return subscriberID;
    }

    public SubscriberMemberDto getSubscriberMemberInfo(String email) {
        String query = "SELECT s.Subscriber_ID, m.Member_Seq " +
                "FROM Subscribers s " +
                "JOIN Members m ON s.Subscriber_ID = m.Subscriber_ID " +
                "WHERE s.Email = ?";

        List<SubscriberMemberDto> result = jdbcTemplate.query(query, (rs, rowNum) -> {
            String subscriberId = rs.getString(COLUMN_SUBSCRIBER_ID);
            String memberSequence = rs.getString("Member_Seq");
            return new SubscriberMemberDto(subscriberId, memberSequence);
        }, email);

        return result.isEmpty() ? null : result.get(0);
    }

    public JsonNode getListMemberAccum(String subscriberID,
                                       String memberSeq) {
        externalServiceRateLimiter.acquire();
        String endpoint = String.format("%s/subscribers/%s/members/%s/accums", domain, subscriberID, memberSeq);
        ResponseEntity<JsonNode> response = vbaRestTemplate.getForEntity(endpoint,
                JsonNode.class);
        return response.getBody();
    }

    public JsonNode getPlanAccumulator(String planID) {
        externalServiceRateLimiter.acquire();
        String endpoint = String.format("%s/plans/%s/accumulators", domain, planID);
        ResponseEntity<JsonNode> response = vbaRestTemplate.getForEntity(endpoint,
                JsonNode.class);
        return response.getBody();
    }

    public JsonNode getPlan(String planID) {
        externalServiceRateLimiter.acquire();
        String endpoint = String.format("%s/plans/%s", domain, planID);
        ResponseEntity<JsonNode> response = vbaRestTemplate.getForEntity(endpoint,
                JsonNode.class);
        return response.getBody();
    }

    public JsonNode getSubscriberEnrollmentPlans(String subscriberID) {
        externalServiceRateLimiter.acquire();
        String endpoint = String.format("%s/subscribers/%s/plans", domain, subscriberID);
        ResponseEntity<JsonNode> response = vbaRestTemplate.getForEntity(endpoint,
                JsonNode.class);
        return response.getBody();
    }

    public JsonNode getSubscriber(String subscriberID, String memberSeq) {
        externalServiceRateLimiter.acquire();
        String endpoint = String.format("%s/subscribers/%s/members/%s", domain, subscriberID, memberSeq);
        ResponseEntity<JsonNode> response = vbaRestTemplate.getForEntity(endpoint, JsonNode.class);
        return response.getBody();
    }

    public JsonNode getPlanBenefits(String planID) {
        externalServiceRateLimiter.acquire();
        String endpoint = String.format("%s/plans/%s/benefits", domain, planID);
        ResponseEntity<JsonNode> response = vbaRestTemplate.getForEntity(endpoint, JsonNode.class);
        return response.getBody();
    }

    public CompletableFuture<String> getEffectivePlanID(String subscriberID) {
        return CompletableFuture.supplyAsync(() -> {
            String query = "SELECT Plan_ID from SubEnrollmentPlan sep "
                    + "where sep.Subscriber_ID = ? and "
                    + "sep.Plan_End > CURRENT_TIMESTAMP;";
            List<String> planIDs = jdbcTemplate.query(query, (rs, rowNum) -> rs.getString("Plan_ID"), subscriberID);
            if (!planIDs.isEmpty()) {
                return planIDs.get(0);
            }
            return null;
        });
    }
    public EnrollmentSubscriber getEnrollmentSubscriber(String subscriberId) {

        List<String> columns = new ArrayList<>();

        columns.add(COLUMN_SUBSCRIBER_ID);
        columns.add("Plan_ID");
        columns.add("Group_ID");
        columns.add("Plan_Start");
        columns.add("Plan_End");

        String selectedColumns = String.join(", ", columns);

        String query = SELECT + selectedColumns +
                " FROM AllEnrollmentsSubscriber(?) " +
                " WHERE GETUTCDATE() BETWEEN Plan_Start AND Plan_End";

        List<EnrollmentSubscriber> results = jdbcTemplate.query(query,
                new BeanPropertyRowMapper<>(EnrollmentSubscriber.class), subscriberId);

        if (results.isEmpty()) {
            // Handle the case where no results were found
            return null;
        } else if (results.size() > 1) {
            // Handle the case where more than one result was found
            return results.get(0);
        }

        // Return the first (and presumably only) result
        return results.get(0);
    }

    public JsonNode getListMemberBySubscriberId(String subscriberID) {
        String endpoint = String.format("%s/subscribers/%s/members", domain, subscriberID);
        ResponseEntity<JsonNode> response = vbaRestTemplate.getForEntity(endpoint,
                JsonNode.class);
        return response.getBody();
    }

    public JsonNode getProviderById(String providerId) {
        String endpoint = String.format("%s/providers/%s", domain, providerId);
        ResponseEntity<JsonNode> response = vbaRestTemplate.getForEntity(endpoint, JsonNode.class);
        return response.getBody();
    }

    //checks if the eligible member has been enrolled
    

    public ProfileCheckResultDto checkUser(ProfileCheckDto profileCheckDto) {
    String sql = 
        "WITH RankedSubscribers AS ( " +
        "  SELECT COALESCE(s.Email, '') AS Email, " +
        "         s.Subscriber_ID AS subscriberID, " +
        "         m.Member_Seq AS memberSequence, " +
        "         ROW_NUMBER() OVER (PARTITION BY m.SSN ORDER BY s.Entry_date DESC) AS rn " +
        "  FROM IDCodeValue idv " +
        "  INNER JOIN Subscribers s ON s.subscriber_ID = idv.Key_Value1 " +
        "  INNER JOIN Members m ON m.subscriber_ID = idv.Key_Value1 AND m.member_Seq = idv.Key_Value2 " +
        "  WHERE idv.ID_Code = 'VBAGATEWAY' " +
        "    AND idv.Type = 'MEMBER' " +
        "    AND LOWER(m.First_Name) = LOWER(?) " +
        "    AND LOWER(m.Last_Name) = LOWER(?) " +
        "    AND m.SSN = ? " +
        "    AND m.Birth_Date = ? " +
        ") " +
        "SELECT Email, subscriberID, memberSequence " +
        "FROM RankedSubscribers " +
        "WHERE rn = 1";
        
    log.info(sql);
    return jdbcTemplate.queryForObject(
        sql, 
        new BeanPropertyRowMapper<>(ProfileCheckResultDto.class), 
        profileCheckDto.getFirstName(), 
        profileCheckDto.getLastName(), 
        profileCheckDto.getSsn(), 
        profileCheckDto.getBirthday()
    );
}

    /**
     * Checks for a Claim_ID based on Claim ID/Number and Payee Federal ID (TIN).
     * It searches both dbo.Claim and dbo.ClaimBatch tables.
     *
     * @param providerProfileCheckDto DTO containing Claim ID/Number and TIN (mapped to Federal_ID).
     * @return The matching Claim_ID as a String, or null if not found.
     * @throws RuntimeException if a database error occurs.
     */
    public String checkProvider(ProviderProfileCheckDto providerProfileCheckDto) {
        // Modified SQL to join with dbo.Payee instead of dbo.Provider
        String sql = "SELECT CAST(Claim_ID AS VARCHAR) FROM (" +
                     "SELECT c.Claim_ID " +
                     "FROM dbo.Claim c " +
                     // Join with Payee table using Payee_ID
                     "INNER JOIN dbo.Payee py ON c.Payee_ID = py.Payee_ID " +
                     // Check if the input matches either Claim_ID or Claim_Number (case-insensitive)
                     "WHERE (LOWER(c.Claim_ID) = ? OR LOWER(c.Claim_Number) = ?) " +
                     // Filter by Payee's Federal_ID
                     "AND py.Federal_ID = ? " +
                     "UNION " + // Combine results from ClaimBatch
                     "SELECT cb.Claim_ID " +
                     "FROM dbo.ClaimBatch cb " +
                     // Join with Payee table using Payee_ID
                     "INNER JOIN dbo.Payee py ON cb.Payee_ID = py.Payee_ID " +
                     // Check only Claim_ID for ClaimBatch (case-insensitive)
                     "WHERE LOWER(cb.Claim_ID) = ? " +
                     // Filter by Payee's Federal_ID
                     "AND py.Federal_ID = ?) AS combined_results";

        try {
            // Parameters remain in the same order as the placeholders '?'
            return jdbcTemplate.queryForObject(sql, String.class,
                    // Parameters for the first SELECT (dbo.Claim):
                    providerProfileCheckDto.getClaimID().toLowerCase(), // For LOWER(c.Claim_ID) = ?
                    providerProfileCheckDto.getClaimID().toLowerCase(), // For LOWER(c.Claim_Number) = ?
                    providerProfileCheckDto.getTin(),                   // For py.Federal_ID = ?
                    // Parameters for the second SELECT (dbo.ClaimBatch):
                    providerProfileCheckDto.getClaimID().toLowerCase(), // For LOWER(cb.Claim_ID) = ?
                    providerProfileCheckDto.getTin()                    // For py.Federal_ID = ?
            );
        } catch (EmptyResultDataAccessException e) {
            // No matching record found - this is often a valid business scenario
            System.err.println("No claim found matching the criteria (ClaimID/Number: '" + providerProfileCheckDto.getClaimID() + "', TIN: '" + providerProfileCheckDto.getTin() + "') joining with Payee.");
            return null; // Return null to indicate not found
        } catch (Exception e) {
            // Log other potential exceptions more informatively
            System.err.println("Error executing payee claim check query: " + e.getMessage());
            // Rethrow as a runtime exception or a custom application exception
            throw new RuntimeException("Database error during payee claim check", e);
        }
    }

    public boolean checkForPrescriptionSearch(String groupId){
        String sql = "select distinct Key_Value1 from IDCodeValue iv where ID_Code = 'CAPITALRX' and Type = 'GROUPPLAN'";
        List<String> groupIds = jdbcTemplate.queryForList(sql, String.class);
        return groupIds.contains(groupId);
    }


    public ProfileInfoDto getVBAProfile(String subscriberID){
        String sql = "SELECT m.First_Name as \"firstName\", " +
             "m.Middle_Name as \"middleName\", " +
             "m.Last_Name as \"lastName\", " +
             "s.Address as \"address\", " +
             "s.Address2 as \"address2\", " +
             "s.City as \"city\", " +
             "s.State as \"state\", " +
             "s.Zip_Code as \"zipCode\", " +
             "s.Email as \"email\", " +
             "m.SSN as \"ssn\", " +
             "m.Birth_Date as \"birthday\" " +
             "FROM IDCodeValue idv " +
             "INNER JOIN Subscribers s ON s.subscriber_ID = idv.Key_Value1 " +
             "INNER JOIN Members m ON m.subscriber_ID = idv.Key_Value1 AND m.member_Seq = idv.Key_Value2 " +
             "WHERE ID_Code = 'VBAGATEWAY' AND Type = 'MEMBER' AND m.Member_Seq = '01' AND s.Email IS NOT NULL " +
             "AND s.email != '' AND s.Subscriber_ID = ?";

    return jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(ProfileInfoDto.class), subscriberID);

    }

}
