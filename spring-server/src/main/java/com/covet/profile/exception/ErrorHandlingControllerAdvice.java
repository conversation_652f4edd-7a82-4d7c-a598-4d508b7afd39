package com.covet.profile.exception;

import com.covet.profile.utils.MessageUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.NonNull;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.orm.jpa.JpaObjectRetrievalFailureException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.Map;


@ControllerAdvice
public class ErrorHandlingControllerAdvice {

    @ExceptionHandler(JsonProcessingException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    ResponseEntity<Object> onJsonProcessingException(
            JsonProcessingException ex) {
        Map<String, String> message = new HashMap<>();
        String cause =   (ex.getCause() != null && ex.getCause().getCause() != null) ? ex.getCause().getCause().getMessage() : ex.getMessage();
        message.put("message", cause);
        return new ResponseEntity<>(message, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    ResponseEntity<Object> onConstraintValidationException(
            ConstraintViolationException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getConstraintViolations().forEach((error) -> {
            String fieldName =  error.getPropertyPath().toString();
            String errorMessage = error.getMessage();
            errors.put(fieldName, errorMessage);
        });
        return new ResponseEntity<>(buildErrorMap(errors), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    ResponseEntity<Object> onMethodArgumentNotValidException(
            MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getFieldErrors().forEach(error -> {
            String fieldName = error.getField();
            String defaultMessage = error.getDefaultMessage();
            errors.put(fieldName, defaultMessage);
        });
        return new ResponseEntity<>(buildErrorMap(errors), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    ResponseEntity<Object> onHttpMessageNotReadableException(
            HttpMessageNotReadableException ex) {
        Map<String, String> message = new HashMap<>();
        String cause =   (ex.getCause() != null && ex.getCause().getCause() != null) ? ex.getCause().getCause().getMessage() : ex.getMessage();
        message.put("message", cause);
        return new ResponseEntity<>(message, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ResourceNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    @ResponseBody
    ResponseEntity<Object> onNotFoundException(ResourceNotFoundException ex) {
        Map<String, String> message = new HashMap<>();
        message.put("message", ex.getMessage());
        return new ResponseEntity<>(message, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(JpaObjectRetrievalFailureException.class)
    @ResponseBody
    ResponseEntity<Object> onJpaObjectRetrievalFailureException(JpaObjectRetrievalFailureException ex) {
        Map<String, String> message = new HashMap<>();
        String cause =   (ex.getCause() != null && ex.getCause().getCause() != null) ? ex.getCause().getCause().getMessage() : ex.getMessage();
        message.put("message", cause);
        return new ResponseEntity<>(message, HttpStatus.NOT_FOUND);
    }

    private Map<String, String> buildErrorMap(@NonNull Map<String, String> errors) {
        Map<String, String> message = new HashMap<>();
        message.put("message", MessageUtils.convertWithStream(errors));
        return message;
    }

    @ExceptionHandler(value = RestTemplateException.class)
    ResponseEntity<Object> handleMyRestTemplateException(RestTemplateException ex) {
        return new ResponseEntity<>(ex.getError(), ex.getStatusCode());
    }

}
