package com.covet.profile.service;

import com.covet.profile.clients.ZendeskClient;
import com.covet.profile.converter.AppointmentConverter;
import com.covet.profile.converter.PhysicianConverter;
import com.covet.profile.dto.covet.appointment.AppointmentDto;
import com.covet.profile.dto.covet.appointment.AppointmentTicketDto;
import com.covet.profile.dto.covet.provider.PhysicianDto;
import com.covet.profile.clients.vba.VBADtos.Member.SubscriberMemberDto;
import com.covet.profile.dto.zendeskDto.ZendeskAppointmentDto;
import com.covet.profile.persistence.model.Appointment;
import com.covet.profile.persistence.model.Physician;
import com.covet.profile.persistence.model.Profile;
import com.covet.profile.persistence.repository.AppointmentRepository;
import com.covet.profile.persistence.repository.ProfileRepository;
import com.covet.profile.persistence.repository.RegistrationFcmRepository;
import com.covet.profile.systemEnum.EAppointmentStatus;
import com.covet.profile.systemEnum.SysEnum;
import com.covet.profile.utils.AuthUtils;
import com.covet.profile.utils.JsonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.Message;
import com.google.firebase.messaging.Notification;
import feign.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;

import javax.persistence.EntityManager;
import javax.persistence.EntityNotFoundException;
import javax.persistence.PersistenceContext;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@Service
@RequiredArgsConstructor
@Slf4j
public class AppointmentService {

    @PersistenceContext
    private final EntityManager entityManager;

    private final ProfileRepository profileRepository;
    private final ZendeskClient zendeskClient;

    @Value("${spring.third-party.zendesk.group-id}")
    private String zendeskGroupId;

    private final AppointmentRepository appointmentRepository;
    private final RegistrationFcmRepository registrationFcmRepository;
    private final FirebaseMessaging firebaseMessaging;
    private final PushNotificationService pushNotificationService;

    public String createAppointmentTicket(AppointmentTicketDto dto, SubscriberMemberDto subscriberMember) throws Exception {
        UUID cognitoId = AuthUtils.getCognitoId();

        // Extract the user's name from the JWT or another method
        String userName = extractUserName(cognitoId);

        // Create the request body
         ObjectNode ticket = buildTicketBody(dto, subscriberMember, userName);

         try (Response response = zendeskClient.createTicket(ticket)) {
             return response.body().toString();
         } catch (HttpClientErrorException.UnprocessableEntity e) {
             throw new UnprocessEntityException("Server could not parse JSON. Please check the request payload.", e);
         }
    }

    public void triggerAppointmentNotification(Appointment appointment) {

        if (Objects.isNull(appointment)) {
            log.info("Cannot parse appointment from zendesk");
            return;
        }
        var patientId = appointment.getPatientId();
        var firebaseToken = registrationFcmRepository.findById(patientId);

        if (firebaseToken.isEmpty()) {
            log.info("UserId: {}, don't have firebase token", patientId);
            return;
        }

        var token = firebaseToken.get().getRegistrationToken();
        var notification = Notification.builder()
                .setTitle("Covet Health")
                .setBody("A appointment have been solved")
                .build();
        Message msg = Message.builder()
                .setToken(token)
                .setNotification(notification)
                .putData("body", JsonUtils.toJson(AppointmentConverter.appointmentToDto(appointment)))
                .putData("type", "appointment")
                .build();

        pushNotiAppointment(msg);
    }

    @Retryable(
            retryFor = {Exception.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000)
    )
    public void pushNotiAppointment(Message msg) {
        try {
            firebaseMessaging.send(msg);
        } catch (Exception e) {
            log.info("Push appointment notification fail with message: {}", e.getMessage());
        }
    }

    private String extractUserName(UUID userId) {
        Optional<Profile> profile = profileRepository.findById(userId);
        if (profile.isPresent()) {
            return profile.get().getFirstName() + " " + profile.get().getLastName();
        } else {
            throw new EntityNotFoundException("Profile not found");
        }
    }

    private ObjectNode buildTicketBody(AppointmentTicketDto dto, SubscriberMemberDto subscriberMember, String userName) {
        // Build the subject
        String subject = "Appointment request: " + userName + " " + new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");

        // Build the body
        String daysOfWeek = getAppointmentDaysOfWeek(dto);

        StringJoiner timeRangesJoiner = new StringJoiner("\n");
        dto.getTimeRanges().forEach(range -> timeRangesJoiner.add(range.getStartTime().format(formatter) + " - " + range.getEndTime().format(formatter)));

        String visitPreference = "";
        if (dto.isVirtual() && dto.isInPerson()) {
            visitPreference = "both";
        } else if (dto.isVirtual()) {
            visitPreference = "virtual";
        } else if (dto.isInPerson()) {
            visitPreference = "in-person";
        } else {
            visitPreference = "unspecified";
        }

        String body = userName + " ("+subscriberMember.getSubscriberId()+" "+subscriberMember.getMemberSequence()+")"+ " requests an appointment with " + dto.getProviderName() + " ("+dto.getNpi()+")"+ " at the following date/time:\n"
                + daysOfWeek+ "\nfrom:\n" + timeRangesJoiner
                + "\nVisit Preference: " + visitPreference
                + "\nDescription: " + dto.getDescription()
                + "\nAppointment Type: " + SysEnum.EAppointmentType.getStringFromValue(dto.getAppointmentType());

        var appointmentId = dto.getAppointmentId();
        if (appointmentId.isPresent()) {
            body += "\nCovet Appointment Id: " + appointmentId.get();
        }

        ObjectMapper mapper = new ObjectMapper();
        ObjectNode ticket = mapper.createObjectNode();
        ticket.put("subject", subject);
        ticket.set("comment", mapper.createObjectNode().put("body", body));
        ticket.put("group_id", zendeskGroupId);
        ObjectNode requestJson = mapper.createObjectNode();
        requestJson.set("ticket", ticket);

        return requestJson;
    }

    public AppointmentDto createOrUpdateAppointment(AppointmentTicketDto ticket) throws EntityNotFoundException {
        UUID patientId = AuthUtils.getCognitoId();
        Physician physician = entityManager.getReference(Physician.class, ticket.getPhysicianId());
        Profile profile = entityManager.getReference(Profile.class, patientId);
        String daysOfWeek = getAppointmentDaysOfWeek(ticket);
        AppointmentDto newAppointmentDto = AppointmentConverter.zendeskTicketToAppointmentDto(ticket, patientId, daysOfWeek, physician);

        return AppointmentConverter.appointmentToDto(
                createOrUpdateAppointment(
                        AppointmentConverter.dtoToAppointment(newAppointmentDto, profile, physician)));
    }

    public Appointment createOrUpdateAppointment(Appointment updateAppointment) throws EntityNotFoundException {
        return appointmentRepository.save(updateAppointment);
    }

    private String getAppointmentDaysOfWeek(AppointmentTicketDto ticket) {
        StringJoiner daysJoiner = new StringJoiner(", ");
        var daysOfWeek = ticket.getTimeRanges().stream().map(x -> x.getDayOfWeek()).distinct().toList();
        daysOfWeek.forEach(daysJoiner::add);
        return daysJoiner.toString();
    }

    @Transactional
    public void updateZendeskAppointment(ZendeskAppointmentDto dto, PhysicianDto physicianDto, LocalDateTime appointmentDateTime) {
        Physician physicianFromZendesk = PhysicianConverter.dtoToPhysician(physicianDto);

        var existedAppointment = appointmentRepository.findById(dto.getAppointmentId());

        if (existedAppointment.isPresent()) {
            var appointment = existedAppointment.get();

            appointment.setAppointmentDate(appointmentDateTime);
            appointment.setAppointmentType(dto.getAppointmentType());
            appointment.setStatus(SysEnum.EAppointmentStatus.UPCOMING.getValue());

            Appointment createdOrUpdatedAppointment;
            boolean isUpdate;

            if (appointment.getPhysicianId().equals(physicianFromZendesk.getPhysicianId())) {
                isUpdate = false;
                createdOrUpdatedAppointment = createOrUpdateAppointment(appointment);
            }
            else {
                createdOrUpdatedAppointment = reCreateAppointment(appointment, physicianFromZendesk.getPhysicianId());
                isUpdate = true;
            }

            var createAppointmentFuture = CompletableFuture.runAsync(
                    () -> triggerAppointmentNotification(createdOrUpdatedAppointment)
            );
            var saveAppointmentFuture = CompletableFuture.runAsync(
                    () -> pushNotificationService.createOrUpdateAppointmentNoti(createdOrUpdatedAppointment, isUpdate)
            );

            var futures = CompletableFuture.allOf(createAppointmentFuture, saveAppointmentFuture);
            futures.join();
        }
    }

    @Transactional
    public Appointment reCreateAppointment(Appointment existingAppointment, UUID newPhysicianId) {
        var deepCopyAppointment = JsonUtils.toObj(JsonUtils.toJson(existingAppointment), Appointment.class);
        if (Objects.isNull(deepCopyAppointment)) {
            return null;
        }
        appointmentRepository.delete(existingAppointment);
        deepCopyAppointment.setPhysicianId(newPhysicianId);
        deepCopyAppointment.setStatus(EAppointmentStatus.UPCOMING.getValue());
        return appointmentRepository.saveAndFlush(deepCopyAppointment);
    }

    public void deleteAppointment(UUID id) {
        appointmentRepository.deleteById(id);
    }
}

class UnprocessEntityException extends Exception {
    public UnprocessEntityException(String message, Throwable cause) {
        super(message, cause);
    }
}