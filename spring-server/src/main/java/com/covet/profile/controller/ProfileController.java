package com.covet.profile.controller;

import com.covet.profile.data.State;
import com.covet.profile.dto.Auth0UserDTO;
import com.covet.profile.dto.ConfirmSignUpDto;
import com.covet.profile.dto.ProviderProfileCheckDto;
import com.covet.profile.dto.covet.profile.ImmutableProfileDto;
import com.covet.profile.dto.covet.profile.ProfileCheckDto;
import com.covet.profile.dto.covet.profile.ProfileDto;
import com.covet.profile.dto.covet.profile.ProfileInfoDto;
import com.covet.profile.dto.ProviderProfileRequest;
import com.covet.profile.dto.PrescriptionSchedulerDto.PushNotificationDto;
import com.covet.profile.dto.PrescriptionSchedulerDto.SchedulerPayloadDto;
import com.covet.profile.clients.vba.VBADtos.Member.VBAPRofileSubRequestDto;
import com.covet.profile.dto.VbaDto.ProfileCheckResultDto;
import com.covet.profile.persistence.model.Notification;
import com.covet.profile.persistence.model.RegistrationFCM;
import com.covet.profile.service.Auth0Service;
import com.covet.profile.service.CognitoService;
import com.covet.profile.service.ProfileService;
import com.covet.profile.service.PushNotificationService;
import com.covet.profile.utils.AuthUtils;
import com.covet.profile.clients.vba.VBAService.VBAUserService;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

// @PreAuthorize("hasAuthority('read:patientdata')")
// @PreAuthorize("hasAuthority('SCOPE_profile')")
@Validated
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class ProfileController {
    public static final Logger log = LoggerFactory.getLogger(ProfileController.class);
    private final ProfileService profileService;
    private final CognitoService cognitoService;
    private final PushNotificationService pushNotificationService;
    private final VBAUserService vbaUserService;
    private final Auth0Service auth0Service;

    @GetMapping("/profile")
    public ResponseEntity<ProfileDto> getProfile() {
        UUID cognitoId = AuthUtils.getCognitoId();
        return profileService.getProfileById(cognitoId).map(profile -> new ResponseEntity<>((profile), HttpStatus.OK))
                .orElseGet(() -> {
                    log.error("ProfileId {} not found", cognitoId);
                    return new ResponseEntity<>(HttpStatus.NOT_FOUND);
                });
    }

    @GetMapping("/data/states")
    public ResponseEntity<Map<String, Map<String, String>>> getStates() {
        var allStates = State.getAllStatesWithName();
        Map<String, Map<String, String>> message = new HashMap<>();
        message.put("states", allStates);
        return new ResponseEntity<>(message, HttpStatus.OK);
    }

    // @PreAuthorize("hasAuthority('read:patientdata')")
    @GetMapping("/data/states/code")
    public ResponseEntity<Map<String, List<String>>> getStateCodes() {
        var allStates = State.getAllStateCodes();
        Map<String, List<String>> message = new HashMap<>();
        message.put("states", allStates);
        return new ResponseEntity<>(message, HttpStatus.OK);
    }

    @PostMapping(path = "/profile", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ProfileDto> createProfile(
            @Valid @RequestBody final ProfileDto newProfileDto) {
        UUID cognitoId = AuthUtils.getCognitoId();
        return profileService.getProfileById(cognitoId).map(profile -> {
            log.error("ProfileId {} already exist for create", cognitoId);
            return new ResponseEntity<ProfileDto>(HttpStatus.CONFLICT);
        }).orElseGet(() -> {
            var profileDto = ImmutableProfileDto.copyOf(newProfileDto)
                    .withCognitoId(cognitoId);
            return new ResponseEntity<>(profileService.createOrUpdate(profileDto), HttpStatus.CREATED);
        });
    }

    @PutMapping(path = "/profile", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ProfileDto> updateProfile(@Valid @RequestBody ProfileDto newProfileDetails) {
        UUID cognitoId = AuthUtils.getCognitoId();
        return profileService.getProfileById(cognitoId).map(profile -> {
            ProfileDto updatedProfileDto = profileService.createOrUpdate(ImmutableProfileDto.copyOf(newProfileDetails)
                    .withCreatedDate(profile.getCreatedDate())
                    .withCognitoId(profile.getCognitoId()));
            return new ResponseEntity<>(updatedProfileDto, HttpStatus.OK);
        }).orElseGet(() -> {
            log.error("ProfileId {} doesn't exist for update", cognitoId);
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        });
    }

    @DeleteMapping("/profile")
    public ResponseEntity<ProfileDto> deleteProfile() {
        // UUID cognitoId = AuthUtils.getCognitoId();
        // return profileService.getProfileById(cognitoId).map(profile -> {
        //     profileService.deleteProfile(cognitoId);
        //     return new ResponseEntity<>(profile, HttpStatus.OK);
        // }).orElseGet(() -> {
        //     log.error("ProfileId {} doesn't exist for delete", cognitoId);
        //     return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        // });
        try {
            String auth0UserId = AuthUtils.getAuth0Id();
            auth0Service.deleteUser(auth0UserId); //delete user from auth0
            UUID cognitoId = AuthUtils.getCognitoId();
            profileService.deleteProfile(cognitoId);
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (Exception e) {
            log.error("Failed to delete profile", e);
            return new ResponseEntity(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // @PreAuthorize("hasAnyRole('patient', 'physician')")
    @PostMapping(path = "/profile/avatar", consumes = { MediaType.MULTIPART_FORM_DATA_VALUE })
    public ResponseEntity<Map<String, Object>> postAvatar(
            @RequestParam MultipartFile file) {
        URL result = profileService.postAvatar(file);
        Map<String, Object> response = new HashMap<>();
        response.put("avatarUrl", result);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    // @PreAuthorize("hasAnyRole('patient', 'physician')")
    @PostMapping(path = "/registration-token")
    public ResponseEntity<RegistrationFCM> postRegistrationToken(String registrationToken) {
        return new ResponseEntity<>(profileService.setRegistrationToken(registrationToken), HttpStatus.CREATED);
    }

    // @PreAuthorize("hasAnyRole('patient', 'physician')")
    @PostMapping(path = "/schedulers")
    public ResponseEntity<List<Notification>> createScheduler(
            @RequestBody final PushNotificationDto pushNotificationDto) {
        List<Notification> notifications = pushNotificationService.saveSchedulerSchemes(pushNotificationDto);
        return new ResponseEntity<>(notifications, HttpStatus.CREATED);
    }

    // @PreAuthorize("hasAnyRole('patient', 'physician')")
    @PutMapping(path = "/schedulers")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void updateScheduler(@RequestBody final SchedulerPayloadDto schedulerPayloadDto)
            throws JsonProcessingException {
        pushNotificationService.updateScheduler(schedulerPayloadDto);
    }

    // @PreAuthorize("hasAnyRole('patient', 'physician')")
    @DeleteMapping(path = "/schedulers/{schedulerId}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void deleteScheduler(@PathVariable final UUID schedulerId) {
        pushNotificationService.deleteScheduler(schedulerId);
    }

    // @PreAuthorize("hasAnyRole('patient', 'physician')")
    @GetMapping(path = "/schedulers/{schedulerId}")
    public ResponseEntity<SchedulerPayloadDto> getSchedulerDetail(@PathVariable final UUID schedulerId)
            throws Exception {
        SchedulerPayloadDto result = pushNotificationService.getScheduler(schedulerId);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PostMapping(path = "/sign-up/confirm")
    public ResponseEntity<Object> confirmSignUp(@Valid @RequestBody ConfirmSignUpDto confirmSignUpDto) {
        cognitoService.confirmSignUp(confirmSignUpDto);
        return new ResponseEntity<>("Successful!", HttpStatus.CREATED);
    }

    @PostMapping(path = "/calculate-distance")
    public List<Double> calculateDistance(Double lat, Double lng) {
        return profileService.calculateDistance(lat, lng);
    }

    // Qualification check

    // @PostMapping(path = "/profile/check")
    // public ResponseEntity<Boolean> checkProfile(@Valid @RequestBody ProfileCheckDto profileCheckDto) {
    //     boolean isEligible = vbaUserService.checkUserProfile(profileCheckDto);
    //     return ResponseEntity.ok(isEligible);
    // }
    @PostMapping(path = "/profile/check")
    public ResponseEntity<ProfileCheckResultDto> checkProfile(@Valid @RequestBody ProfileCheckDto profileCheckDto) {
        ProfileCheckResultDto result = vbaUserService.checkUserProfile(profileCheckDto);
        if (result != null) {
            log.info(result.toString());
            return ResponseEntity.ok(result);
        } else {
            log.error("Profile check failed for given details");
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    @CrossOrigin(origins = {"http://localhost:3000", "https://portal.covethealth.io"}, allowedHeaders = {"Content-Type", "x-api-key"})
    @PostMapping(path = "/profile/provider/check")
    public ResponseEntity<?> checkProviderProfiles(@Valid @RequestBody ProviderProfileRequest request) {
        try {
            List<ProviderProfileCheckDto> providerProfiles = request.getProviders();
            // Iterate through the list of provider profiles and perform the checks
            List<String> results = providerProfiles.stream()
                    .map(vbaUserService::checkProviderProfiles)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (!results.isEmpty()) {
                return ResponseEntity.ok(results);
            } else {
                log.error("Profile check failed for all provided details");
                return new ResponseEntity<>("No matches found for the provided details", HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            log.error("Error occurred while checking provider profiles: {}", e.getMessage(), e);
            return new ResponseEntity<>("An error occurred while processing the request", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @PostMapping(path = "/profile/vba")
    public ResponseEntity<?> getProfileBySubscriberId(@RequestBody VBAPRofileSubRequestDto requestDto) {
        ProfileInfoDto profileInfo = vbaUserService.getProfileBySubscriberId(requestDto.getSubscriberID());
        if (profileInfo != null) {
            return ResponseEntity.ok(profileInfo);
        } else {
            log.error("Profile retrieval failed for Subscriber ID: " + requestDto.getSubscriberID());
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
}

//TODO: move to other controller if we have other auth0 calls to make
@PostMapping(path = "/profile/create-user")
public ResponseEntity<?> createUser(@Valid @RequestBody Auth0UserDTO userDto) {
    try {
        String result = auth0Service.createUser(userDto);
        return new ResponseEntity<>(result, HttpStatus.CREATED);
    } catch (Exception e) {
        log.error("Error creating user: ", e);
        return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }



}