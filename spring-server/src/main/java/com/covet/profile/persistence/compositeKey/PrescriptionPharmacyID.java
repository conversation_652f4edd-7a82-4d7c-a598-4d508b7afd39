package com.covet.profile.persistence.compositeKey;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Embeddable;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

@Embeddable
@Data
@NoArgsConstructor
public class PrescriptionPharmacyID implements Serializable {
    @Column(name = "prescription_name")
    private String prescriptionName;

    @Column(name = "prescription_code")
    private String prescriptionCode;

    @Column(name = "pharmacy_id")
    private String pharmacyId;

    @JsonIgnore
    @Column(name = "patient_id")
    private UUID patientID;

    public PrescriptionPharmacyID(String prescriptionName, String prescriptionCode, String pharmacyId) {
        this.prescriptionName = prescriptionName;
        this.prescriptionCode = prescriptionCode;
        this.pharmacyId = pharmacyId;
    }

    public PrescriptionPharmacyID(String prescriptionName, String prescriptionCode, String pharmacyId, UUID patientID) {
        this.prescriptionName = prescriptionName;
        this.prescriptionCode = prescriptionCode;
        this.pharmacyId = pharmacyId;
        this.patientID = patientID;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null) {
            return false;
        }
        if (getClass() != o.getClass()) {
            return false;
        }
        PrescriptionPharmacyID salePrescription = (PrescriptionPharmacyID) o;
        return Objects.equals(getPrescriptionName(), salePrescription.prescriptionName)
                && Objects.equals(getPrescriptionCode(), salePrescription.prescriptionCode)
                && getPharmacyId().equals(salePrescription.pharmacyId)
                && getPatientID().equals(salePrescription.patientID);
    }

    @Override
    public int hashCode() {
        return Objects.hash(getPrescriptionName(), getPrescriptionCode(), getPharmacyId(), getPatientID());
    }
}
