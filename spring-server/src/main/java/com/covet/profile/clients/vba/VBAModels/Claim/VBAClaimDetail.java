package com.covet.profile.clients.vba.VBAModels.Claim;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class VBAClaimDetail {

    @JsonProperty("claim_Number")
    private long claimNumber;

    @JsonProperty("claim_Seq")
    private String claimSeq;

    @JsonProperty("adjustment_Seq")
    private String adjustmentSeq;

    @JsonProperty("ansI_Code")
    private String ansICode;

    @JsonProperty("apC_Code")
    private String apCCode;

    @JsonProperty("account_Key")
    private int accountKey;

    @JsonProperty("adjusted_Line")
    private boolean adjustedLine;

    @JsonProperty("admin_Amt")
    private BigDecimal adminAmt;

    @JsonProperty("auth_Number")
    private String authNumber;

    @JsonProperty("auth_Penalty")
    private BigDecimal authPenalty;

    @JsonProperty("auth_Required")
    private String authRequired;

    @JsonProperty("benefit_Code")
    private String benefitCode;

    @JsonProperty("benefit_Rate_Applied")
    private String benefitRateApplied;

    @JsonProperty("billed_Price")
    private BigDecimal billedPrice;

    @JsonProperty("brand_Indicator")
    private String brandIndicator;

    @JsonProperty("buccal")
    private boolean buccal;

    @JsonProperty("cliA_Number")
    private String cliANumber;

    @JsonProperty("coB_Amt")
    private BigDecimal coBAmt;

    @JsonProperty("coB_Hold")
    private String coBHold;

    @JsonProperty("coB_Mem_Resp")
    private String coBMemResp;

    @JsonProperty("coB_Savings")
    private BigDecimal coBSavings;

    @JsonProperty("coB_Savings_Used")
    private BigDecimal coBSavingsUsed;

    @JsonProperty("calendar_Year")
    private String calendarYear;

    @JsonProperty("capitation_Claim")
    private boolean capitationClaim;

    @JsonProperty("check_ID")
    private String checkID;

    @JsonProperty("co_Ins_Amt")
    private BigDecimal coInsAmt;

    @JsonProperty("co_Pay_Amt")
    private BigDecimal coPayAmt;

    @JsonProperty("currency_ID")
    private String currencyID;

    @JsonProperty("days_Supply")
    private String daysSupply;

    @JsonProperty("ded_Credit_Amt")
    private BigDecimal dedCreditAmt;

    @JsonProperty("deductible_Amt")
    private BigDecimal deductibleAmt;

    @JsonProperty("diagnosis_Code")
    private String diagnosisCode;

    @JsonProperty("discount_Amt")
    private BigDecimal discountAmt;

    @JsonProperty("dispense_As_Written")
    private String dispenseAsWritten;

    @JsonProperty("distal")
    private boolean distal;

    @JsonProperty("drug_Code")
    private String drugCode;

    @JsonProperty("drug_Quantity")
    private String drugQuantity;

    @JsonProperty("entry_Date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private Date entryDate;

    @JsonProperty("entry_User")
    private String entryUser;

    @JsonProperty("ex_Code")
    private String exCode;

    @JsonProperty("ex_Code2")
    private String exCode2;

    @JsonProperty("ex_Code3")
    private String exCode3;

    @JsonProperty("fica")
    private BigDecimal fica;

    @JsonProperty("facial")
    private boolean facial;

    @JsonProperty("federal_Amount_To_Tax")
    private BigDecimal federalAmountToTax;

    @JsonProperty("federal_Tax")
    private BigDecimal federalTax;

    @JsonProperty("federal_Tax_Flat")
    private BigDecimal federalTaxFlat;

    @JsonProperty("fill_Date")
    private String fillDate;

    @JsonProperty("flex_Description")
    private String flexDescription;

    @JsonProperty("formulary_Indicator")
    private String formularyIndicator;

    @JsonProperty("incisal")
    private boolean incisal;

    @JsonProperty("interest")
    private BigDecimal interest;

    @JsonProperty("labial")
    private boolean labial;

    @JsonProperty("life_Benefit")
    private String lifeBenefit;

    @JsonProperty("life_Benefit_Volume")
    private String lifeBenefitVolume;

    @JsonProperty("line_Control_Number")
    private String lineControlNumber;

    @JsonProperty("lingual")
    private boolean lingual;

    @JsonProperty("local_Tax")
    private BigDecimal localTax;

    @JsonProperty("mail_Order_Indicator")
    private String mailOrderIndicator;

    @JsonProperty("medicare_Amount_To_Tax")
    private BigDecimal medicareAmountToTax;

    @JsonProperty("medicare_Tax")
    private BigDecimal medicareTax;

    @JsonProperty("mesial")
    private boolean mesial;

    @JsonProperty("modifier")
    private String modifier;

    @JsonProperty("modifier_2")
    private String modifier2;

    @JsonProperty("modifier_3")
    private String modifier3;

    @JsonProperty("modifier_4")
    private String modifier4;

    @JsonProperty("not_Covered_Amt")
    private BigDecimal notCoveredAmt;

    @JsonProperty("occlusal")
    private boolean occlusal;

    @JsonProperty("oral_Cavity_Designation_Code_1")
    private String oralCavityDesignationCode1;

    @JsonProperty("oral_Cavity_Designation_Code_2")
    private String oralCavityDesignationCode2;

    @JsonProperty("oral_Cavity_Designation_Code_3")
    private String oralCavityDesignationCode3;

    @JsonProperty("oral_Cavity_Designation_Code_4")
    private String oralCavityDesignationCode4;

    @JsonProperty("oral_Cavity_Designation_Code_5")
    private String oralCavityDesignationCode5;

    @JsonProperty("other_Deduction_Amount")
    private BigDecimal otherDeductionAmount;

    @JsonProperty("override_Benefit_Code")
    private boolean overrideBenefitCode;

    @JsonProperty("override_COB_Amt")
    private boolean overrideCOBAmt;

    @JsonProperty("override_Co_Ins_Amt")
    private boolean overrideCoInsAmt;

    @JsonProperty("override_Co_Pay_Amt")
    private boolean overrideCoPayAmt;

    @JsonProperty("override_Ded_Credit_Amt")
    private boolean overrideDedCreditAmt;

    @JsonProperty("override_Deductible_Amt")
    private boolean overrideDeductibleAmt;

    @JsonProperty("override_Federal_Tax")
    private boolean overrideFederalTax;

    @JsonProperty("override_Local_Tax")
    private boolean overrideLocalTax;

    @JsonProperty("override_Medicare_Tax")
    private boolean overrideMedicareTax;

    @JsonProperty("override_Not_Covered_Amt")
    private boolean overrideNotCoveredAmt;

    @JsonProperty("override_Plan_Price")
    private boolean overridePlanPrice;

    @JsonProperty("override_Social_Security")
    private boolean overrideSocialSecurity;

    @JsonProperty("override_State_Tax")
    private boolean overrideStateTax;

    @JsonProperty("override_With_Hold_Amt")
    private boolean overrideWithHoldAmt;

    @JsonProperty("ppO_Price_Plan_Price_Diff")
    private String ppOPricePlanPriceDiff;

    @JsonProperty("paid_Amt")
    private BigDecimal paidAmt;

    @JsonProperty("partial_Loss")
    private BigDecimal partialLoss;

    @JsonProperty("payor_ID")
    private String payorID;

    @JsonProperty("pharmacy_Name")
    private String pharmacyName;

    @JsonProperty("place_Of_Service")
    private String placeOfService;

    @JsonProperty("plan_Price")
    private BigDecimal planPrice;

    @JsonProperty("premium_Deduction")
    private BigDecimal premiumDeduction;

    @JsonProperty("prescriber_ID")
    private String prescriberID;

    @JsonProperty("prescription_Date")
    private String prescriptionDate;

    @JsonProperty("prescription_Number")
    private String prescriptionNumber;

    @JsonProperty("pricing_Method")
    private String pricingMethod;

    @JsonProperty("pricing_Rate")
    private String pricingRate;

    @JsonProperty("principal")
    private BigDecimal principal;

    @JsonProperty("procedure_Code")
    private String procedureCode;

    @JsonProperty("procedure_Code2")
    private String procedureCode2;

    @JsonProperty("qualifying_Payment_Amount")
    private String qualifyingPaymentAmount;

    @JsonProperty("reason_Code")
    private String reasonCode;

    @JsonProperty("refill_Indicator")
    private String refillIndicator;

    @JsonProperty("repriced_Savings_Amount")
    private String repricedSavingsAmount;

    @JsonProperty("root")
    private boolean root;

    @JsonProperty("service_Date")
    private Date serviceDate;

    @JsonProperty("service_Thru")
    private Date serviceThru;

    @JsonProperty("socSec_Amount_To_Tax")
    private BigDecimal socSecAmountToTax;

    @JsonProperty("social_Security")
    private BigDecimal socialSecurity;

    @JsonProperty("state_Amount_To_Tax")
    private BigDecimal stateAmountToTax;

    @JsonProperty("state_Tax")
    private BigDecimal stateTax;

    @JsonProperty("state_Tax_Flat")
    private BigDecimal stateTaxFlat;

    @JsonProperty("state_Tax_State")
    private String stateTaxState;

    @JsonProperty("status_Code")
    private String statusCode;

    @JsonProperty("svc_Count")
    private double svcCount;

    @JsonProperty("taxable_Amount")
    private BigDecimal taxableAmount;

    @JsonProperty("tooth_Number")
    private String toothNumber;

    @JsonProperty("ucR_Price")
    private BigDecimal ucRPrice;

    @JsonProperty("unit_Dose")
    private String unitDose;

    @JsonProperty("unit_Of_Measure")
    private String unitOfMeasure;

    @JsonProperty("update_Date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private Date updateDate;

    @JsonProperty("update_User")
    private String updateUser;

    @JsonProperty("with_Hold_Amt")
    private BigDecimal withHoldAmt;
}
