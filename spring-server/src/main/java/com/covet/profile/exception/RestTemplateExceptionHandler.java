package com.covet.profile.exception;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.stream.Collectors;

import org.springframework.http.HttpStatus;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.client.ResponseErrorHandler;

@Component
public class RestTemplateExceptionHandler
    implements ResponseErrorHandler {

  @Override
  public boolean hasError(ClientHttpResponse httpResponse)
      throws IOException {

    return (httpResponse.getStatusCode().is4xxClientError() || httpResponse.getStatusCode().is5xxServerError());
  }

  @Override
  public void handleError(ClientHttpResponse httpResponse)
      throws IOException {

    if (httpResponse.getStatusCode().is4xxClientError() || httpResponse.getStatusCode().is5xxServerError()) {
      try (BufferedReader reader = new BufferedReader(new InputStreamReader(httpResponse.getBody()))) {
        String httpBodyResponse = reader.lines().collect(Collectors.joining(""));
        String errorMessage = httpBodyResponse;
        String api = httpResponse.getHeaders().getOrigin();

        throw new RestTemplateException(api, httpResponse.getStatusCode(), errorMessage);
      }
    }
  }
}