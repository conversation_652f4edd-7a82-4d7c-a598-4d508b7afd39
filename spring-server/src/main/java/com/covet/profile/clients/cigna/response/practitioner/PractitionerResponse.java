package com.covet.profile.clients.cigna.response.practitioner;

import com.covet.profile.clients.cigna.response.common.Link;
import com.covet.profile.clients.cigna.response.common.BaseMeta;
import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
public class PractitionerResponse {
    public String resourceType;
    public BaseMeta meta;
    public String type;
    public int total;
    public List<Link> link = Collections.emptyList();
    public List<PractitionerEntry> entry = Collections.emptyList();
}
