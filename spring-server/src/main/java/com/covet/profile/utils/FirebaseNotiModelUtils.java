package com.covet.profile.utils;

import com.covet.profile.converter.notification.response.MedicineNotificationResponse;
import com.google.firebase.messaging.Message;
import com.google.firebase.messaging.Notification;

public class FirebaseNotiModelUtils {

    public static Message buildPrescriptionNotiMsg(MedicineNotificationResponse noti, String token) {
        var notification = Notification.builder()
                .setTitle("Covet Health")
                .setBody("It's time to take some prescriptions")
                .build();
        return Message.builder()
                .setToken(token)
                .setNotification(notification)
                .putData("body", JsonUtils.toJson(noti))
                .putData("type", "active_prescription")
                .build();
    }

}
