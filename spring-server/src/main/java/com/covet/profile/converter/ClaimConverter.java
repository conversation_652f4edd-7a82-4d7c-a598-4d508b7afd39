package com.covet.profile.converter;

import com.covet.profile.clients.vba.VBADtos.Claim.ImmutableVBAClaimDetailDto;
import com.covet.profile.clients.vba.VBADtos.Claim.ImmutableVBAClaimDto;
import com.covet.profile.dto.covet.claim.ClaimDto;
import com.covet.profile.dto.covet.claim.ImmutableClaimDto;
import com.covet.profile.persistence.model.Claim;
import com.covet.profile.persistence.model.Physician;
import com.covet.profile.persistence.model.Profile;
import com.covet.profile.utils.RoundingUtils;
import com.covet.profile.clients.vba.VBADtos.Claim.VBAClaimDetailDto;
import com.covet.profile.clients.vba.VBADtos.Claim.VBAClaimDto;
import com.covet.profile.clients.vba.VBAModels.Claim.VBAClaim;
import com.covet.profile.clients.vba.VBAModels.Claim.VBAClaimDetail;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;

public class ClaimConverter {

    private ClaimConverter() {
    }

    public static ClaimDto claimToDto(Claim claim, Profile physicianProfile) {
        return ImmutableClaimDto.of(
                claim.getClaimCode(),
                claim.getPatientId(),
                claim.getPhysicianId(),
                claim.getIsPaid(),
                claim.getCreatedDate(),
                claim.getTotalClaim(),
                claim.getClaimType(),
                Optional.ofNullable(physicianProfile));
    }

    public static Claim dtoToClaim(ClaimDto claimDto, Profile profile, Physician physician) {
        return new Claim(
                claimDto.getClaimCode(),
                profile.getCognitoId(),
                physician.getPhysicianId(),
                claimDto.getIsPaid(),
                claimDto.getCreatedDate(),
                claimDto.getTotalClaim(),
                claimDto.getClaimType(),
                profile,
                physician);
    }

    public static VBAClaimDetailDto vbaClaimDetailToDto(VBAClaimDetail claimDetail, UUID patientId, String providerId,
                                                        String providerName, boolean isPaid, String claimType, BigDecimal totalPrice) {
        return ImmutableVBAClaimDetailDto.of(
                claimDetail.getClaimNumber(),
                patientId,
                providerId,
                claimDetail.getStatusCode(),
                claimDetail.getServiceDate(),
                providerName,
                isPaid,
                claimType,
                RoundingUtils.customRoundValue(totalPrice)
        );
    }

    public static VBAClaimDto vbaClaimToDto(VBAClaim claim) {
        return ImmutableVBAClaimDto.of(
                claim.getClaimNumber(),
                claim.getSubscriberId(),
                claim.getMemberSeq(),
                claim.getProviderId(),
                claim.getClaimType()
        );
    }
}