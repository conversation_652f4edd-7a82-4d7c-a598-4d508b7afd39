package com.covet.profile.persistence.specification;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.Predicate;

import org.springframework.data.jpa.domain.Specification;

import com.covet.profile.persistence.model.Claim;
import com.covet.profile.persistence.model.PriorAuthor;
import com.covet.profile.persistence.model.Profile;

public class PriorAuthorSpec {
    public static Specification<PriorAuthor> withinRangeOfDate(Optional<Date> fromDate, Optional<Date> toDate) {
        return (root, query, builder) -> {
            if (!Optional.ofNullable(fromDate).isPresent() || !Optional.ofNullable(toDate).isPresent()) {
                return null;
            }
            return builder.between(root.get("createdDate"), fromDate.get(), toDate.get());
        };
    }

    public static Specification<PriorAuthor> findByStatusValue(String statusValue) {
        return (root, query, builder) -> {
            if (!Optional.ofNullable(statusValue).isPresent()) {
                return null;
            }
            return builder.equal(root.get(
                    "authorStatus"), statusValue);
        };
    }

    public static Specification<PriorAuthor> findByRequestMedicineWithPending(String requestMedicine) {
        return (root, query, builder) -> {
            Predicate predicateReqMed = builder.equal(root.get(
                    "reqMed"), requestMedicine);
            Predicate predicateStatus = builder.equal(root.get(
                    "authorStatus"), "pending");
            List<Predicate> predicates = new ArrayList<Predicate>();
            predicates.add(predicateStatus);
            predicates.add(predicateReqMed);
            return builder.and(predicates.toArray(new Predicate[predicates.size()]));
        };
    }

    public static Specification<PriorAuthor> searchByKeywordOnPriorAuthor(List<String> columnNames,
            Optional<String> keyword) {
        return (root, query, builder) -> {
            if (!Optional.ofNullable(keyword).isPresent()) {
                return null;
            }
            List<Predicate> predicates = new ArrayList<Predicate>();
            for (int i = 0; i < columnNames.size(); i++) {
                Predicate predicate = builder.like(root.get(columnNames.get(i)), "%" + keyword.get() + "%");
                predicates.add(predicate);
            }
            return builder.or(predicates.toArray(new Predicate[predicates.size()]));
        };
    }

    public static Specification<PriorAuthor> searchByKeywordOnPhysicianInfo(List<String> columnNames,
            Optional<String> keyword) {
        return (root, query, builder) -> {
            Join<PriorAuthor, Profile> priorAuthorProfile = root.join("physicianInfo");
            if (!Optional.ofNullable(keyword).isPresent()) {
                return null;
            }
            List<Predicate> predicates = new ArrayList<Predicate>();
            for (int i = 0; i < columnNames.size(); i++) {
                Predicate predicate = builder.like(priorAuthorProfile.get(columnNames.get(i)),
                        "%" + keyword.get() + "%");
                predicates.add(predicate);
            }
            return builder.or(predicates.toArray(new Predicate[predicates.size()]));
        };
    }

    public static Specification<PriorAuthor> claimStatus(String claimCode, String authorStatus) {
        return (root, query, builder) -> {
            Join<PriorAuthor, Claim> claimPriorAuthor = root.join("claim");

            Predicate statusPriorAuthor = builder.equal(root.get("authorStatus"), authorStatus);
            Predicate claimPredicate = builder.equal(claimPriorAuthor.get("claimCode"), claimCode);
            return builder.and(statusPriorAuthor, claimPredicate);
        };
    }
}
