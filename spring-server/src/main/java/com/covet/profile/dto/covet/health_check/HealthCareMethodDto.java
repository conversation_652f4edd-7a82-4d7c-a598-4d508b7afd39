package com.covet.profile.dto.covet.health_check;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.util.Optional;
import java.util.UUID;

@JsonSerialize(as = ImmutableHealthCareMethodDto.class)
@JsonDeserialize(as = ImmutableHealthCareMethodDto.class, builder = ImmutableHealthCareMethodDto.Builder.class)
@Value.Immutable
public interface HealthCareMethodDto {
    @Value.Parameter
    @JsonProperty
    Optional<UUID> getMethodId();

    @Value.Parameter
    @JsonProperty
    String getMethodName();

    @Value.Parameter
    @JsonProperty
    Double getCost();
}
