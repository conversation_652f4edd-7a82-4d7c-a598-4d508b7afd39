package com.covet.profile.persistence.model;


import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "patient_favorite")
public class PatientFavorite extends BaseEntity<UUID> {

    @Id
    @GeneratedValue
    @Column(name = "id")
    private UUID id;

    @Column(name = "patient_id")
    @NotNull
    private UUID patientId;

    @Column(name = "favorite_id")
    @NotNull
    private UUID favoriteId;

    @Column(name = "is_active", columnDefinition = "boolean default true")
    private Boolean isActive;

    @Column(name = "favorite_type")
    private String favoriteType;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "patient_id", insertable = false, updatable = false)
    private Profile profile;
}