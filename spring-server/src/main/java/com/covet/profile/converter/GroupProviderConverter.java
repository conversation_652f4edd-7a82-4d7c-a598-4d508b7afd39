package com.covet.profile.converter;

import com.covet.profile.dto.covet.provider.GroupProviderDto;
import com.covet.profile.dto.covet.provider.ImmutableGroupProviderDto;
import com.covet.profile.persistence.model.GroupProvider;
import com.covet.profile.utils.RoundingUtils;
import lombok.NoArgsConstructor;

import java.util.Optional;

@NoArgsConstructor
public class GroupProviderConverter {

    public static GroupProviderDto groupProviderToDto(GroupProvider groupProvider,
            Optional<Object> npiInfo, Double distance, Boolean isFavorite) {
        return ImmutableGroupProviderDto.of(
                Optional.ofNullable(groupProvider.getGroupId()),
                groupProvider.getGroupName(),
                groupProvider.getGroupDescription(),
                groupProvider.getGroupAddress(),
                groupProvider.getGroupEmail(),
                groupProvider.getGroupPhoneNumber(),
                groupProvider.getIsVirtualOnly(),
                groupProvider.getGroupSpecialty(),
                Optional.ofNullable(groupProvider.getNpi()),
                Optional.ofNullable(groupProvider.getCity()),
                Optional.ofNullable(groupProvider.getState()),
                Optional.of(RoundingUtils.roundDoubleValue(groupProvider.getValue())),
                Optional.of(RoundingUtils.roundDoubleValue(groupProvider.getQuality())),
                Optional.of(RoundingUtils.roundDoubleValue(groupProvider.getEfficiency())),
                Optional.ofNullable(groupProvider.getSource()),
                distance,
                Optional.ofNullable(groupProvider.getGroupRating()),
                npiInfo,
                Optional.ofNullable(isFavorite)
        );
    }

    public static GroupProvider dtoToGroupProvider(GroupProviderDto dto) {
        return new GroupProvider(
                dto.getGroupId().orElse(null),
                dto.getGroupName(),
                dto.getGroupDescription(),
                dto.getGroupAddress(),
                dto.getGroupEmail(),
                dto.getGroupPhoneNumber(),
                dto.getIsVirtualOnly(),
                dto.getGroupSpecialty(),
                dto.getNpi().orElse(null),
                dto.getCity().orElse(null),
                dto.getState().orElse(null),
                dto.getValue().orElse(0.0),
                dto.getQuality().orElse(0.0),
                dto.getEfficiency().orElse(0.0),
                dto.getSource().orElse("other"),
                0.0,
                0.0,
                null,
                dto.getGroupRating().orElse(null)
        );
    }
}
