package com.covet.profile.service.interfaces;

import com.covet.profile.dto.CapRxDto.drug.response.CapDrugPriceListResponseDto;
import com.covet.profile.dto.CapRxDto.pharmacy.CapPharmacyDto;
import com.covet.profile.dto.CapRxDto.plan.CapRxMemberListResponseDto;
import com.covet.profile.dto.CapRxDto.pharmacy.PharmacyReqBodyDto;
import com.covet.profile.dto.CapRxDto.drug.request.PriceDrugParamsDto;
import com.covet.profile.clients.vba.VBADtos.Member.SubscriberMemberDto;

import java.util.List;

public interface ICapitalRxService {
    List<CapPharmacyDto> getCapitalRxPharmacies(PharmacyReqBodyDto pharmacyReqBodyDto);
    CapPharmacyDto getPharmacyById(String providerId);
    CapDrugPriceListResponseDto getDrugPrice(PriceDrugParamsDto priceDrugParamsDto);
    CapRxMemberListResponseDto getMemberList();
    CapRxMemberListResponseDto getMember(SubscriberMemberDto subscriberID);
}
