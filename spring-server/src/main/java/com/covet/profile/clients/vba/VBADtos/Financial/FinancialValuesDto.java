package com.covet.profile.clients.vba.VBADtos.Financial;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.math.BigDecimal;

@JsonSerialize(as = ImmutableFinancialValuesDto.class)
@JsonDeserialize(as = ImmutableFinancialValuesDto.class, builder = ImmutableFinancialValuesDto.Builder.class)
@Value.Immutable
public interface FinancialValuesDto {
    @Value.Parameter
    @JsonProperty
    BigDecimal getApplied();

    @Value.Parameter
    @JsonProperty
    BigDecimal getRemaining();

    @Value.Parameter
    @JsonProperty
    BigDecimal getMax();
}
