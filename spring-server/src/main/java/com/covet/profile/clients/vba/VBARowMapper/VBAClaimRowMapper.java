package com.covet.profile.clients.vba.VBARowMapper;

import com.covet.profile.clients.vba.VBAModels.Claim.VBAClaim;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class VBAClaimRowMapper implements RowMapper<VBAClaim> {
    @Override
    public VBAClaim mapRow(ResultSet rs, int rowNum) throws SQLException {
        VBAClaim claim = new VBAClaim();
        claim.setClaimNumber(rs.getLong("Claim_Number"));
        claim.setSubscriberId(rs.getString("Subscriber_ID"));
        claim.setMemberSeq(rs.getString("Member_Seq"));
        claim.setProviderId(rs.getString("Provider_ID"));
        claim.setClaimType(rs.getString("Claim_Type"));
        return claim;
    }
}



