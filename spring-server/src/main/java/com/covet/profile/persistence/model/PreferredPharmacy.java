package com.covet.profile.persistence.model;

import com.covet.profile.persistence.compositeKey.PreferredPharmacyID;
import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "preferred_pharmacy")

public class PreferredPharmacy implements Serializable {

    @EmbeddedId
    private PreferredPharmacyID preferredPharmacyID;

    // relationship
    @JsonIgnore
    @MapsId("cognitoId")
    @JoinColumn(name = "cognito_id")
    @ManyToOne
    private Profile profile;
}
