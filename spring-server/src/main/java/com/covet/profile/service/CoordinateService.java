package com.covet.profile.service;

import com.covet.profile.persistence.model.AddressCoordinateEntity;
import com.covet.profile.persistence.repository.AddressCoordinatesRepository;
import com.covet.profile.persistence.repository.NpiAddressEntityRepository;
import com.google.gson.JsonArray;
import com.google.gson.JsonParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class CoordinateService {
    private final NpiAddressEntityRepository npiAddressEntityRepository;
    private final AddressCoordinatesRepository addressCoordinatesRepository;

    public void setAddressCoordinates() {
        var current = 29171L;
        var max = 73269L;
        while (current <= max) {
            var ids = new LinkedList<Long>();
            for (int i = 1; i <= 50; i++) {
                ids.add(current + i);
            }
            // find all by ids
            var npiAddressPage = npiAddressEntityRepository.findAllById(ids);
            Map<Long, String> addressMap = new HashMap<>();
            npiAddressPage.forEach(item -> {
                var address = item.getAddress();
                // some convert string methods
                var postalZipPattern = Pattern.compile("[0-9]{9}$");
                var postalCodePattern = Pattern.compile("[0-9]{5}$");
                if (postalZipPattern.matcher(address).find()){
                    // substring text and put to map
                    var newAddress = address.substring(0, address.length() - 4);
                    addressMap.put(item.getId(), newAddress);
                } else if (postalCodePattern.matcher(address).find()) {
                    // fetch coordinate
                    addressMap.put(item.getId(), address);
                }
            });
            List<AddressCoordinateEntity> addressCoordinateEntities = new LinkedList<>();

            addressMap.forEach((addressId, address) -> {
                getCoordinate(addressId, address, addressCoordinateEntities);
                try {
                    Thread.sleep(1300);
                } catch (InterruptedException e) {
                    // handle exception
                }
            });

            if (!CollectionUtils.isEmpty(addressCoordinateEntities)) {
                addressCoordinatesRepository.saveAll(addressCoordinateEntities);
            }

            current += 50;
        }
    }

    public void getCoordinate(Long addressId, String address, List<AddressCoordinateEntity> addressCoordinateEntities) {
        try {
            String encodedAddress = URLEncoder.encode(address, StandardCharsets.UTF_8);
            String urlStr = "https://nominatim.openstreetmap.org/search?q=" + encodedAddress + "&format=json&limit=1";

            URL url = new URL(urlStr);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestProperty("User-Agent", "Mozilla/5.0");

            BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String response = in.lines().collect(Collectors.joining());
            JsonArray jsonArray = JsonParser.parseString(response).getAsJsonArray();
            if (Objects.nonNull(jsonArray) && !jsonArray.isEmpty()) {
                var coordinates = jsonArray.get(0).getAsJsonObject();
                var lat = Optional.of(coordinates.get("lat").getAsBigDecimal().doubleValue()).orElseThrow(Exception::new);
                var lon = Optional.of(coordinates.get("lon").getAsBigDecimal().doubleValue()).orElseThrow(Exception::new);
                var item = new AddressCoordinateEntity();
                item.setAddressId(addressId);
                item.setLatitude(lat);
                item.setLongitude(lon);

                addressCoordinateEntities.add(item);
            } else {
                // handle empty
            }
        } catch (Exception e) {
            // handle fail
        }
    }
}
