package com.covet.profile.config.RestTemlate;

import com.covet.profile.exception.RestTemplateExceptionHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.accessanalyzer.model.ResourceNotFoundException;
import software.amazon.awssdk.services.acm.model.InvalidParameterException;
import software.amazon.awssdk.services.acmpca.model.InvalidRequestException;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueRequest;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueResponse;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Configuration
public class CapRxRestTemplateConfig {

    private String accessKey;
    private String secretKey;
    private String region;
    private static final String AWS_SERVICE = "execute-api";
    private static final String TYPE_REQUEST = "aws4_request";
    private static final String ACCESS_KEY = "AccessKey";// "capRX_Acess_key";
    private static final String SECRET_KEY = "SecretKey";// "capRX_Secret_key";
    private static final String ALGORITHMS = "AWS4-HMAC-SHA256";

    public CapRxRestTemplateConfig(@Value("${spring.third-party.capital-rx.secret-name}") String capitalRxSecretName,
            @Value("${spring.cloud.aws.s3.region}") String region) throws Exception {
        this.region = region;
        SecretsManagerClient client = SecretsManagerClient.builder()
                .region(Region.of(region))
                .build();

        GetSecretValueRequest getSecretValueRequest = GetSecretValueRequest.builder()
                .secretId(capitalRxSecretName)
                .build();
        try {
            GetSecretValueResponse valueResponse = client.getSecretValue(getSecretValueRequest);
            String rawSecretString = valueResponse.secretString();
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, String> properties = objectMapper.readValue(rawSecretString,
                    new TypeReference<Map<String, String>>() {
                    });
            this.accessKey = properties.get(ACCESS_KEY);
            this.secretKey = properties.get(SECRET_KEY);
        } catch (ResourceNotFoundException | InvalidParameterException | InvalidRequestException e) {
            throw e;
        }
    }

    @Bean
    @Primary
    public RestTemplate restTemplate() {
        PoolingHttpClientConnectionManager poolingConnManager = new PoolingHttpClientConnectionManager();
        poolingConnManager.setMaxTotal(50);
        poolingConnManager.setDefaultMaxPerRoute(50);
        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(poolingConnManager)
                .build();
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);
        RestTemplate restTemplate = new RestTemplate(factory);
        restTemplate.setErrorHandler(new RestTemplateExceptionHandler());
        restTemplate.getInterceptors().add(interceptor());
        return restTemplate;
    }

    @Bean
    public ClientHttpRequestInterceptor interceptor() {
        return new ClientHttpRequestInterceptor() {
            @Override
            public ClientHttpResponse intercept(HttpRequest request, byte[] body,
                    ClientHttpRequestExecution execution)
                    throws IOException {
                HttpHeaders headers = request.getHeaders();

                String contentSha256 = null;
                try {
                    contentSha256 = computeSHA256(body);
                } catch (Exception e) {
                    throw new IOException(e.getMessage());
                }

                DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss'Z'");
                DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");

                // calculate the String to Sign steps
                /*
                 * Algorithm
                 * RequestDateTime
                 * CredentialScope
                 * HashedCanonicalRequest
                 */

                String currentTimeStamp = LocalDateTime.now(ZoneId.of("UTC")).format(inputFormatter);
                LocalDateTime date = LocalDateTime.parse(currentTimeStamp, inputFormatter);
                String formattedDate = date.format(outputFormatter);

                headers.set("Host", request.getURI().getHost());
                headers.set("X-Amz-Content-Sha256", contentSha256);
                headers.set("X-Amz-Date", currentTimeStamp);

                String authorization = null;
                try {
                    authorization = computeAuthorization(formattedDate, request, headers, contentSha256,
                            currentTimeStamp, ALGORITHMS);
                } catch (Exception e) {
                    throw new IOException(e.getMessage());
                }
                headers.set("Authorization", authorization);

                return execution.execute(request, body);
            }

            private String computeAuthorization(String formattedDate, HttpRequest request, HttpHeaders headers,
                    String contentSha256, String currentTimeStamp, String algorithms) throws Exception {
                String credentialScope = formattedDate + "/" + region + "/" + AWS_SERVICE +
                        "/" + TYPE_REQUEST;
                String canonicalRequest = buildCanonicalRequest(request.getMethod(),
                        request.getURI(),
                        getCanonicalHeaders(headers), contentSha256);
                String hashedCanonicalRequest = computeSHA256(canonicalRequest.getBytes());

                String stringToSign = algorithms + "\n" + currentTimeStamp + "\n" + credentialScope + "\n" +
                        hashedCanonicalRequest;

                byte[] signatureBytes = computeHmacSha256(generateSigningKey(formattedDate),
                        stringToSign);
                String signatureString = DatatypeConverter.printHexBinary(signatureBytes).toLowerCase();
                String canonicalHeaders = getCanonicalHeaders(headers);
                String signedHeaders = getSignedHeaders(canonicalHeaders);

                String authorization = algorithms + " " +
                        "Credential=" + accessKey + "/" + formattedDate + "/" + region + "/" +
                        AWS_SERVICE
                        + "/" + TYPE_REQUEST + ", " +
                        "SignedHeaders=" + signedHeaders + ", " +
                        "Signature=" + signatureString;
                return authorization;
            }

            private String getCanonicalHeaders(HttpHeaders headers) {
                // Sort headers alphabetically
                List<String> sortedHeaders = headers.entrySet().stream()
                        .map(entry -> entry.getKey().toLowerCase() + ":"
                                + entry.getValue().stream().collect(Collectors.joining(",")))
                        .sorted()
                        .collect(Collectors.toList());

                return String.join("\n", sortedHeaders) + "\n";
            }

            private String buildCanonicalRequest(HttpMethod method, URI uri, String canonicalHeaders,
                    String payloadHash) {
                if (method == null || uri == null) {
                    throw new IllegalArgumentException("Method and URI cannot be null.");
                }
                String canonicalUri = uri.getRawPath();
                String canonicalQuery = uri.getRawQuery() != null ? uri.getRawQuery() : "";
                String canonicalRequest = method.name() + "\n" +
                        canonicalUri + "\n" +
                        canonicalQuery + "\n" +
                        canonicalHeaders + "\n" +
                        getSignedHeaders(canonicalHeaders) + "\n" +
                        payloadHash;

                return canonicalRequest;
            }

            private String getSignedHeaders(String canonicalHeaders) {
                List<String> headerNames = Arrays.stream(canonicalHeaders.split("\n"))
                        .map(header -> header.substring(0, header.indexOf(":")))
                        .collect(Collectors.toList());
                Collections.sort(headerNames);
                return String.join(";", headerNames);
            }

            private byte[] generateSigningKey(String formattedDate) throws Exception {
                byte[] prefixSecretKey = ("AWS4" + secretKey).getBytes(StandardCharsets.UTF_8);
                byte[] dateKey = computeHmacSha256(prefixSecretKey, formattedDate);
                byte[] dateRegion = computeHmacSha256(dateKey, region);
                byte[] dateRegionServiceKey = computeHmacSha256(dateRegion, AWS_SERVICE);

                return computeHmacSha256(dateRegionServiceKey, TYPE_REQUEST);
            }

            private String computeSHA256(byte[] requestPayload) throws Exception {
                try {
                    MessageDigest digest = MessageDigest.getInstance("SHA-256");
                    byte[] hash = digest.digest(requestPayload);

                    return DatatypeConverter.printHexBinary(hash).toLowerCase();
                } catch (Exception e) {
                    throw e;
                }
            }

            private byte[] computeHmacSha256(byte[] key, String data) throws GeneralSecurityException {
                try {
                    Mac hmacSha256 = Mac.getInstance("HmacSHA256");
                    SecretKeySpec secretKeySpec = new SecretKeySpec(key, "HmacSHA256");
                    hmacSha256.init(secretKeySpec);
                    byte[] hash = hmacSha256.doFinal(data.getBytes(StandardCharsets.UTF_8));

                    return hash;
                } catch (NoSuchAlgorithmException | InvalidKeyException e) {
                    throw e;
                }
            }
        };
    }
}
