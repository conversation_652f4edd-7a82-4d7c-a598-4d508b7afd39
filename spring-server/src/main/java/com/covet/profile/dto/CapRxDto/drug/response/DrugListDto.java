package com.covet.profile.dto.CapRxDto.drug.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.List;

import org.immutables.value.Value;

@JsonSerialize(as = ImmutableDrugListDto.class)
@JsonDeserialize(as = ImmutableDrugListDto.class, builder = ImmutableDrugListDto.Builder.class)
@Value.Immutable
public interface DrugListDto {

    @Value.Parameter
    @JsonProperty("results")
    List<DrugItemDto> getDrugList();

}
