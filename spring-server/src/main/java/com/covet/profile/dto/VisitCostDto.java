package com.covet.profile.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.util.Optional;
import java.util.UUID;

@JsonSerialize(as = ImmutableVisitCostDto.class)
@JsonDeserialize(as = ImmutableVisitCostDto.class, builder = ImmutableVisitCostDto.Builder.class)
@Value.Immutable
public interface VisitCostDto {
    @Value.Parameter
    @JsonProperty
    @JsonIgnore
    Optional<UUID> getId();

    @Value.Parameter
    @JsonProperty
    @JsonIgnore
    Optional<UUID> getPhysicianId();

    @Value.Parameter
    @JsonProperty
    Double getVisitCost();

    @Value.Parameter
    @JsonProperty
    Double getVirtualVisitCost();

}
