package com.covet.profile.converter;

import com.covet.profile.dto.covet.provider.review.ImmutableReviewDetailDto;
import com.covet.profile.dto.covet.provider.review.ImmutableReviewDto;
import com.covet.profile.dto.covet.provider.review.ReviewDetailDto;
import com.covet.profile.dto.covet.provider.review.ReviewDto;
import com.covet.profile.persistence.model.GroupProvider;
import com.covet.profile.persistence.model.Physician;
import com.covet.profile.persistence.model.Profile;
import com.covet.profile.persistence.model.Review;

import java.util.Optional;

public class ReviewConverter {
    private ReviewConverter() {}
    public static ReviewDto reviewToDto(Review review, Profile profile) {
        return ImmutableReviewDto.of(
                Optional.ofNullable(review.getId()),
                Optional.ofNullable(review.getPhysicianId()),
                Optional.ofNullable(review.getGroupId()),
                review.getPatientId(),
                Optional.ofNullable(review.getContent()),
                review.getRating(),
                review.getCreatedDate(),
                review.getVisitType(),
                review.getIsHelpful(),
                review.getHelpfulTotal(),
                Optional.ofNullable(profile),
                Optional.ofNullable(review.getGroup())
        );
    }

    public static ReviewDetailDto reviewToReviewDetailDto(Review review, Profile profile) {
        return ImmutableReviewDetailDto.of(
                Optional.ofNullable(review.getId()),
                Optional.ofNullable(review.getPhysicianId()),
                Optional.ofNullable(review.getGroupId()),
                review.getPatientId(),
                Optional.ofNullable(review.getContent()),
                review.getRating(),
                review.getCreatedDate(),
                review.getVisitType(),
                Optional.ofNullable(profile)
        );
    }
    public static Review dtoToReview(ReviewDto reviewDto, Profile patient, Physician physician) {
        return new Review(
                reviewDto.getId().orElse(null),
                patient.getCognitoId(),
                physician.getPhysicianId(),
                reviewDto.getGroupId().orElse(null),
                reviewDto.getCreatedDate(),
                reviewDto.getContent().orElse(null),
                reviewDto.getRating(),
                reviewDto.getVisitType(),
                reviewDto.getIsHelpful(),
                reviewDto.getHelpfulTotal(),
                patient,
                physician,
                reviewDto.getGroupProvider().orElse(null)
        );
    }

    public static Review dtoToReview(ReviewDto reviewDto, Profile patient, GroupProvider group) {
        return new Review(
                reviewDto.getId().orElse(null),
                patient.getCognitoId(),
                reviewDto.getPhysicianId().orElse(null),
                reviewDto.getGroupId().orElse(null),
                reviewDto.getCreatedDate(),
                reviewDto.getContent().orElse(null),
                reviewDto.getRating(),
                reviewDto.getVisitType(),
                reviewDto.getIsHelpful(),
                reviewDto.getHelpfulTotal(),
                patient,
                null,
                group
        );
    }
}
