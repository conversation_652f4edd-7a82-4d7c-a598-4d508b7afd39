package com.covet.profile.persistence.compositeKey;

import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

import javax.persistence.Column;
import javax.persistence.Embeddable;

import lombok.Data;
import lombok.NoArgsConstructor;

@Embeddable
@Data
@NoArgsConstructor
public class HealthCheckID implements Serializable {
    @Column(name = "claim_code")
    private String claimCode;

    @Column(name = "method_id")
    private UUID methodId;

    public HealthCheckID(String claimCode, UUID methodId) {
        this.claimCode = claimCode;
        this.methodId = methodId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        HealthCheckID healthCheck = (HealthCheckID) o;
        return claimCode.equals(healthCheck.claimCode) &&
                methodId.equals(healthCheck.methodId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(claimCode, methodId);
    }
}
