apiVersion: apps/v1
kind: Deployment
metadata:
  name: covetql-server
  labels:
    app: covetql-server
spec:
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  replicas: 1
  selector:
    matchLabels:
      app: covetql-server
  template:
    metadata:
      labels:
        app: covetql-server
    spec:
      containers:
      - name: covetql-server
        image: 123903216279.dkr.ecr.us-east-1.amazonaws.com/covetql:dev-19
        ports:
        - containerPort: 4000
        envFrom:
        - secretRef:
            name: covetql-secrets
        resources:
          requests:
            cpu: 500m 
            memory: 256Mi 
          limits:
            cpu: 600m
            memory: 256Mi 
---
apiVersion: v1
kind: Service
metadata:
  name: covetql-server
spec:
  selector:
    app: covetql-server
  ports:
    - protocol: TCP
      port: 4000
      targetPort: 4000
