package com.covet.profile.dto;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import com.covet.profile.systemEnum.EDayOfWeek;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@AllArgsConstructor
@Builder
public class SchedulerDto {
    private UUID schedulerId;
    private LocalDate startDate;
    private LocalDate endDate;
    private List<EDayOfWeek> dayOfWeeks;
}
