package com.covet.profile.dto.CapRxDto.plan;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CapRxPlanDto implements Serializable {
    @JsonProperty("absence_boundary")
    private String absenceBoundary;

    @JsonProperty("absence_method")
    private String absenceMethod;

    @JsonProperty("absence_method_group")
    private String absenceMethodGroup;

    @JsonProperty("absence_method_group_terminate_date_option")
    private String absenceMethodGroupTerminateDateOption;

    @JsonProperty("absence_method_terminate_date_option")
    private String absenceMethodTerminateDateOption;

    @JsonProperty("accumulation_load_failure_threshold")
    private double accumulationLoadFailureThreshold;

    @JsonProperty("address_line_1")
    private String addressLine1;

    @JsonProperty("address_line_2")
    private String addressLine2;

    @JsonProperty("automatic_enrollment_type")
    private String automaticEnrollmentType;

    @JsonProperty("bin_number")
    private String binNumber;

    @JsonProperty("city")
    private String city;

    @JsonProperty("claim_payment_cycle")
    private String claimPaymentCycle;

    @JsonProperty("client_id")
    private String clientId;

    @JsonProperty("created_at")
    private Date createdAt;

    @JsonProperty("customer_service_phone_number")
    private String customerServicePhoneNumber;

    @JsonProperty("effective_date")
    private Date effectiveDate;

    @JsonProperty("eligibility_change_failure_threshold")
    private double eligibilityChangeFailureThreshold;

    @JsonProperty("eligibility_reject_failure_threshold")
    private double eligibilityRejectFailureThreshold;

    @JsonProperty("eligibility_status")
    private String eligibilityStatus;

    @JsonProperty("external_member_id_length")
    private String externalMemberIdLength;

    @JsonProperty("external_member_id_pattern")
    private String externalMemberIdPattern;

    @JsonProperty("group_number")
    private String groupNumber;

    @JsonProperty("groups_status")
    private String groupsStatus;

    @JsonProperty("has_automatic_enrollment")
    private boolean hasAutomaticEnrollment;

    @JsonProperty("help_desk_phone")
    private String helpDeskPhone;

    @JsonProperty("id")
    private int id;

    @JsonProperty("is_address_required")
    private boolean isAddressRequired;

    @JsonProperty("member_documents_status")
    private String memberDocumentsStatus;

    @JsonProperty("name")
    private String name;

    @JsonProperty("person_code_length")
    private int personCodeLength;

    @JsonProperty("pharmacy_service_fee_enabled")
    private boolean pharmacyServiceFeeEnabled;

    @JsonProperty("plan_additional_covered_dollar_amount")
    private String planAdditionalCoveredDollarAmount;

    @JsonProperty("plan_additional_covered_percentage_amount")
    private String planAdditionalCoveredPercentageAmount;

    @JsonProperty("plan_additional_covered_standard_category_plan_max")
    private String planAdditionalCoveredStandardCategoryPlanMax;

    @JsonProperty("plan_additional_covered_standard_deductible")
    private String planAdditionalCoveredStandardDeductible;

    @JsonProperty("plan_additional_covered_standard_plan_max")
    private String planAdditionalCoveredStandardPlanMax;

    @JsonProperty("plan_additional_covered_type")
    private String planAdditionalCoveredType;

    @JsonProperty("plan_type")
    private String planType;

    @JsonProperty("pricing_status")
    private String pricingStatus;

    @JsonProperty("print_system")
    private String printSystem;

    @JsonProperty("processor_control_number")
    private String processorControlNumber;

    @JsonProperty("rn")
    private String rn;

    @JsonProperty("state")
    private String state;

    @JsonProperty("status")
    private String status;

    @JsonProperty("strategic_partner_fee")
    private String strategicPartnerFee;

    @JsonProperty("updated_at")
    private Date updatedAt;

    @JsonProperty("zip_code")
    private String zipCode;

}
