apiVersion: eksctl.io/v1alpha5
availabilityZones:
- us-east-1b
- us-east-1a
cloudWatch:
  clusterLogging:
    enableTypes: ["audit", "authenticator", "controllerManager"] 
iam:
  vpcResourceControllerPolicy: true
  withOIDC: false
kind: ClusterConfig
kubernetesNetworkConfig:
  ipFamily: IPv4
managedNodeGroups:
- amiFamily: AmazonLinux2
  desiredCapacity: 4
  disableIMDSv1: true
  disablePodIMDS: false
  iam:
    withAddonPolicies:
      albIngress: false
      appMesh: false
      appMeshPreview: false
      autoScaler: false
      awsLoadBalancerController: false
      certManager: false
      cloudWatch: false
      ebs: false
      efs: false
      externalDNS: false
      fsx: false
      imageBuilder: false
      xRay: false
  instanceSelector: {}
  instanceType: t3.micro
  labels:
    alpha.eksctl.io/cluster-name: MemberAppBackend
    alpha.eksctl.io/nodegroup-name: MAB-nodes
  maxSize: 5
  minSize: 2
  name: MAB-nodes
  privateNetworking: false
  releaseVersion: ""
  securityGroups:
    withLocal: null
    withShared: null
  ssh:
    allow: true
    publicKeyPath: clusterKeyPair
  tags:
    alpha.eksctl.io/nodegroup-name: MAB-nodes
    alpha.eksctl.io/nodegroup-type: managed
  volumeIOPS: 3000
  volumeSize: 80
  volumeThroughput: 125
  volumeType: gp3
metadata:
  name: MemberAppBackend
  region: us-east-1
  version: "1.27"
privateCluster:
  enabled: false
  skipEndpointCreation: false
vpc:
  autoAllocateIPv6: false
  cidr: ***********/16
  clusterEndpoints:
    privateAccess: false
    publicAccess: true
  manageSharedNodeSecurityGroupRules: true
  nat:
    gateway: Single
