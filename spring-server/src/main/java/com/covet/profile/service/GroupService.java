package com.covet.profile.service;

import com.covet.profile.converter.GroupPhysicianConverter;
import com.covet.profile.converter.GroupProviderConverter;
import com.covet.profile.converter.GroupProviderDetailConverter;
import com.covet.profile.dto.ProviderDto.LocationDto;
import com.covet.profile.dto.covet.provider.GroupPhysicianDto;
import com.covet.profile.dto.covet.provider.GroupProviderDetailDto;
import com.covet.profile.dto.covet.provider.GroupProviderDto;
import com.covet.profile.dto.covet.provider.GroupProviderFilterOptionsDto;
import com.covet.profile.exception.ResourceNotFoundException;
import com.covet.profile.persistence.model.*;
import com.covet.profile.persistence.repository.*;
import com.covet.profile.searchCriteria.GroupProviderSearchCriteria;
import com.covet.profile.service.google.GoogleMapService;
import com.covet.profile.systemEnum.EFavoriteType;
import com.covet.profile.systemEnum.SysEnum;
import com.covet.profile.utils.AuthUtils;
import com.covet.profile.utils.CalculateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.EntityNotFoundException;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.*;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class GroupService {

    @PersistenceContext
    EntityManager em;

    private final GroupProviderRepository groupProviderRepository;
    private final GroupPhysicianRepository groupPhysicianRepository;
    private final PhysicianRepository physicianRepository;
    private final ReviewRepository reviewRepository;
    private final PatientFavoriteRepository patientFavoriteRepository;
    private final GroupProviderAddressRepository groupProviderAddressRepository;
    private final InsertBatchStatusRepository insertBatchStatusRepository;

    private final NpiService npiService;
    private final ProfileService profileService;
    private final GoogleMapService googleMapService;
    @Autowired
    DataSource dataSource;


    public GroupPhysicianDto createOrUpdateGroupPhysician(GroupPhysicianDto newGroup) throws EntityNotFoundException {
        return GroupPhysicianConverter.groupPhysicianToDto(
                createOrUpdateGroupPhysician(GroupPhysicianConverter.dtoToGroupPhysician(newGroup)));
    }

    public GroupPhysician createOrUpdateGroupPhysician(GroupPhysician updateGroup) throws EntityNotFoundException {
        GroupProvider group = groupProviderRepository.findByGroupId(updateGroup.getId().getGroupId())
                .orElseThrow(() -> new ResourceNotFoundException("Group provider not found!"));
        Physician physician = physicianRepository.findByPhysicianId(updateGroup.getId().getPhysicianId())
                .orElseThrow(() -> new ResourceNotFoundException("Physician not found!"));
        updateGroup.setGroup(group);
        updateGroup.setPhysician(physician);
        return groupPhysicianRepository.save(updateGroup);
    }

    public GroupProviderDto createOrUpdateGroupProvider(GroupProviderDto newGroup) throws EntityNotFoundException {
        boolean isFavorite = false;
        var newGroupId = newGroup.getGroupId();
        if (newGroupId.isPresent()) {
            UUID groupId = newGroupId.get();
            isFavorite = patientFavoriteRepository.existPatientFavorite(AuthUtils.getCognitoId(), groupId) > 0;
        }
        return GroupProviderConverter.groupProviderToDto(
                createOrUpdateGroupProvider(
                        GroupProviderConverter.dtoToGroupProvider(newGroup)),
                Optional.empty(),
                0.0,
                isFavorite
        );
    }

    public GroupProvider createOrUpdateGroupProvider(GroupProvider updateGroup) throws EntityNotFoundException {
        if (updateGroup.getGroupId() == null) {
            GroupRating groupRating = new GroupRating();
            groupRating.setGroupProvider(updateGroup);
            updateGroup.setGroupRating(groupRating);
        }

        return groupProviderRepository.save(updateGroup);
    }

    @Transactional(readOnly = true)
    public GroupProviderDetailDto getGroupProviderDetail(UUID groupId, String npi, LocationDto location) {

        Double lat;
        Double lng;
        var me = profileService.fetchMe();
        if (Objects.nonNull(location.getLatitude()) && Objects.nonNull(location.getLongitude())) {
            lat = Double.valueOf(location.getLatitude().trim());
            lng = Double.valueOf(location.getLongitude().trim());
        } else {
            lat = me.getLatitude();
            lng = me.getLongitude();
        }
        var npiInfo = npiService.getNpiInfo(npi);
        GroupProvider group = groupProviderRepository.findByGroupId(groupId)
                .orElseThrow(() -> new ResourceNotFoundException("Group provider not found " + groupId));
        boolean isFavorite = patientFavoriteRepository.existPatientFavorite(AuthUtils.getCognitoId(), groupId) > 0;
        var groupProviderDto = GroupProviderConverter.groupProviderToDto(group,
                Optional.ofNullable(npiInfo),
                CalculateUtils.calculateDistance(group.getLatitude(), group.getLongitude(), lat, lng),
                isFavorite);
        Review topReview = reviewRepository.findFirstByGroupOrderByCreatedDate(groupId);
        long totalReviews = reviewRepository.countByGroupId(groupId);
        return GroupProviderDetailConverter.modelsToDto(groupProviderDto, topReview, totalReviews);
    }

    void buildGroupProviderPredicates(CriteriaBuilder cb,
            CriteriaQuery<?> cq,
            Root<GroupProvider> root,
            GroupProviderSearchCriteria criteria,
            Double lat,
            Double lng,
            boolean isCount) {
        List<Predicate> targetPredicates = new LinkedList<>();
        List<Predicate> likePredicates = new LinkedList<>();
        List<Order> sorters = new LinkedList<>();

        List<String> columnNames = new LinkedList<>();
        columnNames.add(GroupProvider.Fields.groupName);
        columnNames.add(GroupProvider.Fields.groupDescription);

        criteria.getGroupSpecialty().ifPresentOrElse(specialtySet -> {
            Set<String> values = Arrays.stream(SysEnum.EGroupSpecialty.values())
                    .map(SysEnum.EGroupSpecialty::getValue)
                    .collect(Collectors.toSet());
            boolean isGroupSpecialtyContained = specialtySet
                    .stream()
                    .map(String::toLowerCase)
                    .anyMatch(values::contains);
            if (isGroupSpecialtyContained) {
                likePredicates.add(cb.lower(root.get(GroupProvider.Fields.groupSpecialty)).in(specialtySet));
            }
        }, () -> columnNames.add(GroupProvider.Fields.groupSpecialty));

        criteria.getKeyword().ifPresent(keyword -> {
            var keywordPattern = "%" + keyword.trim() + "%";
            for (var col : columnNames) {
                likePredicates.add(cb.like(cb.lower(root.get(col)), keywordPattern));
            }
        });

        if (!likePredicates.isEmpty()) {
            Predicate orPredicate = cb.or(likePredicates.toArray(new Predicate[0]));
            targetPredicates.add(orPredicate);
        }

        // rating predicate
        criteria.getRating().ifPresent(rating -> {
            var groupRatingJoin = root.join(GroupProvider.Fields.groupRating);
            Predicate ratingPredicate = cb.between(
                    groupRatingJoin.get(GroupRating.Fields.averageRating), rating, rating + 0.99
            );
            targetPredicates.add(ratingPredicate);
        });

        // filter value
        if (Objects.nonNull(criteria.getValue())) {
            targetPredicates.add(
                    cb.greaterThanOrEqualTo(root.get(GroupProvider.Fields.value), criteria.getValue())
            );

            if (!isCount) {
                sorters.add(cb.desc(root.get(GroupProvider.Fields.value)));
            }
        }
        // filter distance
        if (Objects.nonNull(criteria.getDistance())) {
            var distance = criteria.getDistance();

            Expression<Double> x_2 = cb.prod(
                    cb.diff(root.get(GroupProvider.Fields.latitude), lat),
                    cb.diff(root.get(GroupProvider.Fields.latitude), lat)
            );

            Expression<Double> y_2 = cb.prod(
                    cb.diff(root.get(GroupProvider.Fields.longitude), lng),
                    cb.diff(root.get(GroupProvider.Fields.longitude), lng)
            );

            Predicate distancePredicate = cb.lessThanOrEqualTo(
                    cb.sum(x_2, y_2),
                    Math.pow(distance, 2)
            );

            Predicate coordinatesPredicate = cb.and(
                    cb.notEqual(root.get(GroupProvider.Fields.latitude), 0),
                    cb.notEqual(root.get(GroupProvider.Fields.longitude), 0)
            );
            targetPredicates.add(distancePredicate);
            targetPredicates.add(coordinatesPredicate);

            if (!isCount) {
                sorters.add(cb.asc(cb.sum(x_2, y_2)));
            }
        }

        if (!isCount && !sorters.isEmpty()) {
            cq.orderBy(sorters);
        }

        cq.where(targetPredicates.toArray(new Predicate[0]));
    }

    private List<GroupProvider> getListGroupProviderByConditions(GroupProviderSearchCriteria criteria,
            Pageable pageable, Double lat, Double lng) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<GroupProvider> cq = cb.createQuery(GroupProvider.class);
        Root<GroupProvider> root = cq.from(GroupProvider.class);

        root.fetch(GroupProvider.Fields.groupRating);

        buildGroupProviderPredicates(cb, cq, root, criteria, lat, lng, false);

        return em.createQuery(cq.select(root))
                .setFirstResult((int) pageable.getOffset())
                .setMaxResults(pageable.getPageSize())
                .getResultList();
    }

    private Long countGroupProviderByConditions(GroupProviderSearchCriteria criteria,
            Double lat, Double lng) {

        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        Root<GroupProvider> root = cq.from(GroupProvider.class);

        buildGroupProviderPredicates(cb, cq, root, criteria, lat, lng, true);
        var countExpression = cq.select(cb.count(root.get(GroupProvider.Fields.groupId)));
        return em.createQuery(countExpression).getSingleResult();

    }

    @Transactional(readOnly = true)
    public Page<GroupProviderDto> getGroupProvidersWithFilter(Integer page, Integer pageSize,
            GroupProviderSearchCriteria criteria, LocationDto location) {

        Double lat;
        Double lng;
        if (Objects.nonNull(location.getLatitude()) && Objects.nonNull(location.getLongitude())) {
            lat = Double.valueOf(location.getLatitude().trim());
            lng = Double.valueOf(location.getLongitude().trim());
        } else {
            var meCoordinate = profileService.fetchMeCoordinate();
            lat = meCoordinate.getLatitude();
            lng = meCoordinate.getLongitude();
        }

        var pageRequest = PageRequest.of(Optional.ofNullable(page).orElse(SysEnum.EPaging.PageNumber.getValue()),
                Optional.ofNullable(pageSize).orElse(SysEnum.EPaging.PageSize.getValue()));
        var groupProviderList = getListGroupProviderByConditions(criteria, pageRequest, lat, lng);
        var physicianIds = groupProviderList.stream().map(GroupProvider::getGroupId).toList();

        var favoriteIds = patientFavoriteRepository.findOwnPatientFavorites(AuthUtils.getCognitoId(),
                EFavoriteType.GROUP_PROVIDER.getValue(), physicianIds);

        // npi providers
        var npiList = groupProviderList.stream().map(GroupProvider::getNpi).toList();
        var npiInfoList = npiService.getNpiInfoList(npiList);

        List<GroupProviderDto> contents = new ArrayList<>();
        for (int i = 0; i < groupProviderList.size(); i++) {
            boolean isFavorite = favoriteIds.contains(groupProviderList.get(i).getGroupId());
            contents.add(GroupProviderConverter.groupProviderToDto(
                    groupProviderList.get(i),
                    Optional.ofNullable(npiInfoList.get(i)),
                    CalculateUtils.calculateDistance(
                            groupProviderList.get(i).getLatitude(),
                            groupProviderList.get(i).getLongitude(),
                            lat,
                            lng
                    ),
                    isFavorite)
            );
        }

        var count = countGroupProviderByConditions(criteria, lat, lng);
        return new PageImpl<>(contents, pageRequest, count);
    }


    @Transactional(readOnly = true)
    public Optional<GroupProviderDto> getGroupById(UUID groupId) {
        var meCoordinate = profileService.fetchMeCoordinate();
        var lat = meCoordinate.getLatitude();
        var lng = meCoordinate.getLongitude();
        var groupProvider = groupProviderRepository.getReferenceById(groupId);
        boolean isFavorite = patientFavoriteRepository.existPatientFavorite(AuthUtils.getCognitoId(), groupId) > 0;
        String npi = groupProvider.getNpi();
        var npiInfo = npiService.getNpiInfo(npi);
        return Optional.ofNullable(groupProviderRepository.findByGroupId(groupId)
                .map(group -> GroupProviderConverter.groupProviderToDto(
                        group,
                        Optional.ofNullable(npiInfo),
                        CalculateUtils.calculateDistance(
                                lat, lng, groupProvider.getLatitude(), groupProvider.getLongitude()
                        ),
                        isFavorite))
                .orElseThrow(() -> new ResourceNotFoundException("Group provider not found " + groupId)));
    }

    public GroupProviderFilterOptionsDto getGroupProviderFilterOptions() {
        Set<String> specialties = Arrays.stream(SysEnum.EGroupSpecialty.values())
                .map(SysEnum.EGroupSpecialty::getValue).collect(Collectors.toSet());

        return GroupProviderFilterOptionsDto.builder()
                .specialtyList(specialties)
                .minValue(SysEnum.EFilterOptions.MinValue.getValue())
                .maxValue(SysEnum.EFilterOptions.MaxValue.getValue())
                .minDistance(SysEnum.EFilterOptions.MinDistance.getValue())
                .maxDistance(SysEnum.EFilterOptions.MaxDistance.getValue())
                .defaultRating(SysEnum.EFilterOptions.DefaultRating.getValue())
                .build();
    }

    @Transactional
    public void getLatLngGroupProvider() {
        var startTime = System.currentTimeMillis();

        var source = "GROUP_PROVIDER_ADDRESS";
        int page = 0;
        var maxPage = insertBatchStatusRepository.getMaxSuccessInsertBatch(source);
        if (maxPage.isPresent()) {
            page = maxPage.get() + 1;
        }

        log.info("Start page: {}", page);
        var pageSize = 1000;

        var pageable = PageRequest.of(page, pageSize);
        var IdAndNpiList = groupProviderRepository.selectIdAndNpi(pageable);

        if (!IdAndNpiList.isEmpty()) {
            Map<String, CompletableFuture<String>> groupMap = new ConcurrentHashMap<>();

            IdAndNpiList.stream().parallel()
                    .forEach(item -> groupMap.computeIfAbsent(item.get(0), __ ->
                            CompletableFuture.supplyAsync(() ->
                                    npiService.buildNpiAddressTemplate(npiService.getNpiInfo(item.get(1)))
                            )));
            CompletableFuture.allOf(groupMap.values().toArray(new CompletableFuture[0])).join();

            Map<String, String> groupProviderAddress = new ConcurrentHashMap<>();

            groupMap.keySet().stream().parallel().forEach(
                    item -> groupProviderAddress.computeIfAbsent(item, __ -> groupMap.get(item).join())
            );
            var midTime = System.currentTimeMillis();
            log.info("size {} at {}", groupProviderAddress.size(), midTime - startTime);


            String groupProviderSqlStatementTemplate = "INSERT INTO group_provider_address (id, address)" +
                    " VALUES (?::uuid, ?);";
            try (Connection connection = dataSource.getConnection()) {
                connection.setAutoCommit(false);
                PreparedStatement groupProviderStatement = connection
                        .prepareStatement(groupProviderSqlStatementTemplate);
                groupProviderAddress.forEach(
                        (k, v) -> {
                            try {
                                groupProviderStatement.clearParameters();
                                groupProviderStatement.setObject(1, k);
                                groupProviderStatement.setObject(2, v);
                                groupProviderStatement.addBatch();
                            } catch (SQLException e) {
                                throw new RuntimeException(e);
                            }
                        }
                );
                groupProviderStatement.executeBatch();
                connection.commit();
                groupProviderStatement.clearBatch();
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
            insertBatchStatusRepository.save(new InsertBatchStatus(UUID.randomUUID(), page, true, source));

            var endTime = System.currentTimeMillis();
            log.info("done with duration {}", endTime - startTime);
        } else {
            log.info("Get all address");
        }
    }


    @Transactional
    public void updateCoordinatesGroupProvider() {

        var startTime = System.currentTimeMillis();
        int page = 0;
        String source = "GROUP_PHYSICIAN_COORDINATES";
        var maxPage = insertBatchStatusRepository.getMaxSuccessInsertBatch(source);
        if (maxPage.isPresent()) {
            page = maxPage.get() + 1;
        }
        var pageable = PageRequest.of(page, 1000);

        var groupProviderAddressList = groupProviderAddressRepository.getGroupProviderNonNullAddress(pageable);

        if (groupProviderAddressList.isEmpty()) {
            log.info("All for insert");
            return;
        }

        log.info("Page number with address: {}", page);

        var groupProviderCoordinate = googleMapService.getCoordinatesForGroupProvider(groupProviderAddressList);
        log.info("groupProviderCoordinate size {}", groupProviderCoordinate.size());

        String groupProviderSqlStatementTemplate = "UPDATE group_provider set latitude = ?, longitude = ? " +
                "WHERE group_id = ?::uuid;";
        try (Connection connection = dataSource.getConnection()) {
            connection.setAutoCommit(false);
            PreparedStatement groupProviderStatement = connection
                    .prepareStatement(groupProviderSqlStatementTemplate);
            groupProviderCoordinate.forEach(
                    (k, v) -> {
                        try {
                            groupProviderStatement.clearParameters();
                            groupProviderStatement.setObject(1, v.lat);
                            groupProviderStatement.setObject(2, v.lng);
                            groupProviderStatement.setObject(3, k);
                            groupProviderStatement.addBatch();
                        } catch (SQLException e) {
                            throw new RuntimeException(e);
                        }
                    }
            );
            groupProviderStatement.executeBatch();
            connection.commit();
            groupProviderStatement.clearBatch();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        insertBatchStatusRepository.save(new InsertBatchStatus(UUID.randomUUID(), page, true, source));

        var endTime = System.currentTimeMillis();
        log.info("Done at: {}", endTime - startTime);
    }
}
