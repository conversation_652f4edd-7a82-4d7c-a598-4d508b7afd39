BEGIN;

--Step 1: Drop fk to pharmacy table
ALTER TABLE sale_prescription
DROP CONSTRAINT fk_sale_prescription_pharmacy;

ALTER TABLE preferred_pharmacy
DROP CONSTRAINT fk_preferred_pharmacy;
--End step 1

--Step 2: Cast column pharmacy id to new data type

ALTER TABLE pharmacy
    ADD COLUMN pharmacy_id_new VARCHAR;

UPDATE pharmacy
SET pharmacy_id_new = pharmacy_id::VARCHAR;

ALTER TABLE pharmacy
DROP COLUMN pharmacy_id;

ALTER TABLE pharmacy
RENAME COLUMN pharmacy_id_new TO pharmacy_id;

ALTER TABLE pharmacy
ADD CONSTRAINT pharmacy_pkey PRIMARY KEY (pharmacy_id);
--End Step 2

--Step 3: Alter table sale_prescription

-- 1: Create new mapping table
CREATE TABLE prescription_pharmacy (
                                       prescription_name VARCHAR(256) NOT NULL,
                                       prescription_code VARCHAR(256) NOT NULL,
                                       pharmacy_id VARCHAR NOT NULL, --use provider_id from cap-rx pharmacies as id
                                       patient_id uuid NOT NULL,
                                       cost DECIMAL,
                                       PRIMARY KEY (prescription_name, prescription_code, pharmacy_id, patient_id),
                                       CONSTRAINT fk_prescription_pharmacy_prescription
                                           FOREIGN KEY (prescription_name, prescription_code, patient_id)
                                               REFERENCES prescription (prescription_name, prescription_code, patient_id)
);

-- 2: Insert data from old table to new table
INSERT INTO prescription_pharmacy (prescription_name, prescription_code, pharmacy_id, patient_id, cost)
SELECT prescription_name, prescription_code, pharmacy_id::VARCHAR, 'c5290591-b336-4a6b-b654-38d7092963f6', cost
FROM sale_prescription;

-- 3: Drop old table
DROP TABLE sale_prescription CASCADE;

--END Step 3

--Step 4: Alter preferred_prescription

CREATE TABLE preferred_pharmacy_new (
                                        pharmacy_id VARCHAR NOT NULL,
                                        cognito_id UUID NOT NULL,
                                        PRIMARY KEY (cognito_id, pharmacy_id),
                                        --CONSTRAINT fk_preferred_pharmacy_new FOREIGN KEY (pharmacy_id) REFERENCES pharmacy,
                                        CONSTRAINT fk_preferred_profile_new FOREIGN KEY (cognito_id) REFERENCES profile
);

INSERT INTO preferred_pharmacy_new (pharmacy_id, cognito_id)
SELECT pharmacy_id::VARCHAR, cognito_id
FROM preferred_pharmacy;

DROP TABLE preferred_pharmacy;

ALTER TABLE preferred_pharmacy_new RENAME TO preferred_pharmacy;

--END //

COMMIT;