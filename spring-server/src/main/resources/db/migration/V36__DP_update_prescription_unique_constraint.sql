BEGIN;

DO $$
BEGIN
        IF EXISTS (
                SELECT conname
                FROM pg_constraint
                WHERE conrelid = 'claim_prescription'::regclass
                  AND contype = 'f' and conname = 'fk_for_prescription'
            ) THEN
                ALTER TABLE claim_prescription
                DROP CONSTRAINT fk_for_prescription;
        END IF;
END $$;

DO $$
BEGIN
        IF EXISTS (
                SELECT conname
                FROM pg_constraint
                WHERE conrelid = 'prescription'::regclass
                  AND contype = 'u'
            ) THEN
ALTER TABLE prescription
DROP CONSTRAINT prescription_unique_constraint;

ALTER TABLE prescription
    ADD CONSTRAINT prescription_unique_constraint UNIQUE (prescription_name, prescription_code, patient_id);
END IF;
END $$;

DO $$
BEGIN
        IF NOT EXISTS (
                SELECT conname
                FROM pg_constraint
                WHERE conrelid = 'claim_prescription'::regclass
                  AND contype = 'f' and conname = 'fk_for_prescription'
            ) THEN
                ALTER TABLE claim_prescription
                ADD CONSTRAINT fk_for_prescription UNIQUE (prescription_name, prescription_code, patient_id);
END IF;
END $$;

COMMIT;
