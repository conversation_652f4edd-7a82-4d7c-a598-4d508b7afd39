package com.covet.profile.persistence.repository;

import com.covet.profile.persistence.model.Review;
import lombok.NonNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;
import java.util.UUID;

public interface ReviewRepository extends JpaRepository<Review, UUID>{

    Page<Review> findAllByPhysicianId(UUID physicianId, Pageable pageable);

    Page<Review> findAllByGroupId(UUID groupId, Pageable pageable);

    Page<Review> findAll(Specification<Review> spec, Pageable pageable);

    @Query(value = "select v.* from review v " +
                   "join physician gp on v.physician_id = gp.physician_id " +
                   "where v.rating = (select MAX(rating) from review where physician_id = :physicianId) " +
                   "order by v.created_date desc " +
                   "fetch first 1 rows only;", nativeQuery = true)
    Review findFirstByCreatedDate(@NonNull @Param("physicianId") UUID physicianId);

    @Query(value = "select v.* from review v " +
                   "join group_provider gp on v.group_id = gp.group_id " +
                   "where v.rating = (select MAX(rating) from review where group_id = :groupId) " +
                   "order by v.created_date desc " +
                   "fetch first 1 rows only;", nativeQuery = true)
    Review findFirstByGroupOrderByCreatedDate(@NonNull @Param("groupId") UUID groupId);

    long countByPhysicianId(UUID physicianId);
    long countByGroupId(UUID groupId);

    Optional<Review> findReviewById(UUID reviewId);

    @Modifying
    @Query(value = "update review " +
            "set helpful_total = helpful_total + CASE :isHelpful WHEN true THEN 1 ELSE -1 END WHERE review_id = :reviewId", nativeQuery = true)
    void updateHelpfulStatus(@Param("reviewId") UUID reviewId, @Param("isHelpful") boolean isHelpful);

}

