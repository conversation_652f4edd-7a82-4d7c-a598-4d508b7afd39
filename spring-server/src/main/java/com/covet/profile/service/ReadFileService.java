package com.covet.profile.service;

import com.covet.profile.persistence.model.InsertBatchStatus;
import com.covet.profile.persistence.repository.DrugRepository;
import com.covet.profile.persistence.repository.InsertBatchStatusRepository;
import com.opencsv.CSVReader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import javax.sql.DataSource;
import java.io.*;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.locks.ReentrantLock;

@Service
@Slf4j
@RequiredArgsConstructor
public class ReadFileService {

    private final String dataPath = "/Users/<USER>/Documents/covet/NPI/";
    private final String prepareInsertPath = "/Users/<USER>/Documents/covet/prepare/";
    private final String batchStatusPath = "/Users/<USER>/Documents/covet/batch/";
    private final String groupPhysicianPath = "/Users/<USER>/Documents/covet/group_physician/";
    private final String suffixPrepare = "_prepare";
    private final String NYfacilityFileName = "NY_Facility";
    private final String NYpractitionerFileName = "NY_Practitioner";
    private final String NYGroupPhysician = "NY_GroupPhysician";
    private final String extension = ".csv";
    private final String suffixBatchStatus = "_batch_status";
    private final String drugDataPath = "/Users/<USER>/Documents/covet/drug/drug_data.csv";

    private final DrugRepository drugRepository;

    private final InsertBatchStatusRepository insertBatchStatusRepository;
    @Autowired
    DataSource dataSource;


    private final int chunkSize = 100;
    Executor numOfExecutors = Executors.newFixedThreadPool(36);

    private List<CompletableFuture<Void>> readFacilityFileCompletableFutureBuilder(String filename, String filepath)
            throws IOException {

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        try (CSVReader csvReader = new CSVReader(
                new FileReader(filepath + filename + extension))) {
            String batchStatusFileName = filename + suffixBatchStatus + extension;
            String batchStatusFilePath = batchStatusPath + batchStatusFileName;
            File batchStatusFile = new File(batchStatusFilePath);
            Set<Integer> failBatchStatus = readFailBatch(batchStatusFilePath);
            boolean isExistFile = batchStatusFile.exists();
            if (isExistFile && !failBatchStatus.isEmpty()) {
                var waitForDeleteFile = CompletableFuture.supplyAsync(batchStatusFile::delete);
                waitForDeleteFile.join();
            }
            if (isExistFile && failBatchStatus.isEmpty()) {
                throw new ResponseStatusException(HttpStatus.OK, "Insert done");
            }

            String[] values;
            // next to header
            csvReader.readNext();
            Map<String, Integer> index = new HashMap<>();
            index.put("Facility Name", 0);
            index.put("NPI", 1);
            index.put("City", 2);
            index.put("State", 3);
            index.put("Value", 4);
            index.put("Quality", 5);
            index.put("Efficiency", 6);
            int count = 0;
            List<List<Object>> elementsChunk = new ArrayList<>();
            List<String> batchReadyExecute = new ArrayList<>();
            int batchNumber = 0;
            // group_id, group_name, address, email
            // phone_number, is_virtual_only, description, created_date,
            // updated_date,group_specialty
            while ((values = csvReader.readNext()) != null) {
                count++;
                if (!failBatchStatus.isEmpty() && !failBatchStatus.contains(batchNumber)) {
                    if (count == chunkSize) {

                        count = 0;
                        String statusPath = batchStatusPath + filename + suffixBatchStatus + extension;
                        String header = "Batch Number,Insert Status";
                        createFile(statusPath, header);
                        String newRowData = String.format("%d,%d", batchNumber, 1);

                        try (BufferedWriter writer = new BufferedWriter(new FileWriter(statusPath, true))) {
                            writer.write(newRowData);
                            writer.newLine();
                            batchNumber += 1;
                        }
                    }

                } else {

                    UUID id = UUID.randomUUID();
                    String row = String.format("%s,%s", id, values[index.get("NPI")]);
                    batchReadyExecute.add(row);
                    List<Object> elements = new ArrayList<>();
                    elements.add(id); // group_id
                    elements.add(values[index.get("Facility Name")]); // group_name
                    elements.add(""); // address
                    elements.add(""); // email
                    elements.add("**********"); // phone_number
                    elements.add(false); // is_virtual_only
                    elements.add(""); // description
                    elements.add(null); // created_date
                    elements.add(null); // updated_date
                    elements.add("Dental"); // group_specialty
                    elements.add(values[index.get("NPI")]); // npi
                    elements.add(values[index.get("City")]); // city
                    elements.add(values[index.get("State")]); // state
                    elements.add(Double.valueOf(values[index.get("Value")])); // value
                    elements.add(Double.valueOf(values[index.get("Quality")])); // quality
                    elements.add(Double.valueOf(values[index.get("Efficiency")])); // efficiency
                    elements.add("npi"); // source
                    elementsChunk.add(elements);

                    if (count == chunkSize) {
                        CompletableFuture<Void> future = CompletableFuture.runAsync(
                                insertGroupProviderChunk(elementsChunk, batchNumber, batchReadyExecute, filename),
                                numOfExecutors);
                        futures.add(future);
                        elementsChunk = new ArrayList<>();
                        batchReadyExecute = new ArrayList<>();
                        count = 0;
                        batchNumber += 1;

                    }

                }
            }
            if (!elementsChunk.isEmpty()) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(
                        insertGroupProviderChunk(elementsChunk, batchNumber, batchReadyExecute, filename),
                        numOfExecutors);
                futures.add(future);
            }
        }
        return futures;
    }

    private List<CompletableFuture<Void>> readPractitionerFileCompletableFutureBuilder(String filename, String filepath)
            throws IOException {

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        try (CSVReader csvReader = new CSVReader(
                new FileReader(filepath + filename + extension))) {

            String batchStatusFileName = filename + suffixBatchStatus + extension;
            String batchStatusFilePath = batchStatusPath + batchStatusFileName;
            File batchStatusFile = new File(batchStatusFilePath);
            Set<Integer> failBatchStatus = readFailBatch(batchStatusFilePath);
            boolean isExistFile = batchStatusFile.exists();
            if (isExistFile && !failBatchStatus.isEmpty()) {
                var waitForDeleteFile = CompletableFuture.supplyAsync(batchStatusFile::delete);
                waitForDeleteFile.join();
            }
            if (isExistFile && failBatchStatus.isEmpty()) {
                throw new ResponseStatusException(HttpStatus.OK, "Insert done");
            }

            String[] values;
            // next to header
            csvReader.readNext();
            Map<String, Integer> index = new HashMap<>();
            index.put("Provider Name", 0);
            index.put("NPI", 1);
            index.put("Primary Specialty", 2);
            index.put("Primary Facility Name", 3);
            index.put("Facility NPI", 4);
            index.put("City", 5);
            index.put("State", 6);
            index.put("Value", 7);
            index.put("Quality", 8);
            index.put("Efficiency", 9);
            int count = 0;
            List<List<Object>> elementsChunk = new ArrayList<>();
            List<String> batchReadyExecute = new ArrayList<>();
            int batchNumber = 0;

            while ((values = csvReader.readNext()) != null) {
                count++;
                if (!failBatchStatus.isEmpty() && !failBatchStatus.contains(batchNumber)) {
                    if (count == chunkSize) {
                        count = 0;
                        String statusPath = batchStatusPath + filename + suffixBatchStatus + extension;
                        String header = "Batch Number,Insert Status";
                        createFile(statusPath, header);
                        String newRowData = String.format("%d,%d", batchNumber, 1);

                        try (BufferedWriter writer = new BufferedWriter(new FileWriter(statusPath, true))) {
                            writer.write(newRowData);
                            writer.newLine();
                            batchNumber += 1;
                        }
                    }

                } else {
                    ReentrantLock lock = new ReentrantLock();
                    lock.lock();
                    UUID id = UUID.randomUUID();
                    String row = String.format("%s,%s", id, values[index.get("Facility NPI")]);
                    batchReadyExecute.add(row);
                    lock.unlock();
                    // create array store value: 28 element
                    // for profile
                    List<Object> elements = new ArrayList<>();
                    elements.add(id); // cognito_id
                    elements.add(values[index.get("Provider Name")]); // first_name
                    elements.add(""); // middle_name
                    elements.add(""); // last_name
                    elements.add(null); // created_date
                    elements.add(null); // updated_date
                    elements.add("***********"); // social_security_number
                    elements.add(null); // date_of_birth
                    elements.add(""); // address
                    elements.add(""); // address_line
                    elements.add(values[index.get("City")]); // city
                    elements.add(values[index.get("State")]); // administrative_area
                    elements.add("00000"); // zip_code
                    elements.add(0.0); // latitude
                    elements.add(0.0); // longitude

                    // for physician
                    elements.add(id); // physician_id
                    elements.add(true); // accept_new_patient
                    elements.add(""); // address
                    elements.add(""); // address_line
                    elements.add(values[index.get("City")]); // city
                    elements.add(values[index.get("State")]); // administrative_area
                    elements.add("00000"); // zip_code
                    elements.add(true); // is_virtual
                    elements.add(values[index.get("Primary Specialty")]); // specialty
                    elements.add(Double.valueOf(values[index.get("Value")])); // value
                    elements.add(Double.valueOf(values[index.get("Quality")])); // quality
                    elements.add(Double.valueOf(values[index.get("Efficiency")])); // efficiency
                    elements.add(values[index.get("NPI")]); // npi
                    elements.add("npi"); // source
                    elements.add(values[index.get("Primary Facility Name")]); // facility_name

                    elementsChunk.add(elements);

                    if (count == chunkSize) {
                        CompletableFuture<Void> future = CompletableFuture.runAsync(
                                insertProviderChunk(elementsChunk, batchNumber, batchReadyExecute, filename),
                                numOfExecutors);
                        futures.add(future);
                        elementsChunk = new ArrayList<>();
                        batchReadyExecute = new ArrayList<>();
                        count = 0;
                        batchNumber += 1;

                    }
                }
            }
            if (!elementsChunk.isEmpty()) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(
                        insertProviderChunk(elementsChunk, batchNumber, batchReadyExecute, filename),
                        numOfExecutors);
                    futures.add(future);
                }
        }
        return futures;
    }

    private Runnable insertProviderChunk(List<List<Object>> elementsChunk, int batchNumber,
            List<String> batchReadyExecute, String filename) {
        return () -> {
            ArrayList<String> profileColumns = new ArrayList<>();
            profileColumns.add("cognito_id");
            profileColumns.add("first_name");
            profileColumns.add("middle_name");
            profileColumns.add("last_name");
            profileColumns.add("created_date");
            profileColumns.add("updated_date");
            profileColumns.add("social_security_number");
            profileColumns.add("date_of_birth");
            profileColumns.add("address");
            profileColumns.add("address_line");
            profileColumns.add("city");
            profileColumns.add("administrative_area");
            profileColumns.add("zip_code");
            profileColumns.add("latitude");
            profileColumns.add("longitude");
            String joinProfileColumns = String.join(", ", profileColumns);

            ArrayList<String> physicianColumns = new ArrayList<>();
            physicianColumns.add("physician_id");
            physicianColumns.add("accept_new_patient");
            physicianColumns.add("address");
            physicianColumns.add("address_line");
            physicianColumns.add("city");
            physicianColumns.add("administrative_area");
            physicianColumns.add("zip_code");
            physicianColumns.add("is_virtual");
            physicianColumns.add("specialty");
            physicianColumns.add("value");
            physicianColumns.add("quality");
            physicianColumns.add("efficiency");
            physicianColumns.add("npi");
            physicianColumns.add("source");
            physicianColumns.add("facility_name");
            String joinPhysicianColumns = String.join(", ", physicianColumns);

            String profileSqlStatementTemplate = "INSERT INTO profile (" + joinProfileColumns + ") "
                    + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";
            String physicianSqlStatementTemplate = "INSERT INTO physician (" + joinPhysicianColumns + ") "
                    + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?::source, ?);";
            try (Connection connection = dataSource.getConnection()) {
                // handle save file
                connection.setNetworkTimeout(numOfExecutors, 1000 * 30);
                connection.setAutoCommit(false);
                PreparedStatement profileStatement = connection.prepareStatement(profileSqlStatementTemplate);
                PreparedStatement physicianStatement = connection.prepareStatement(physicianSqlStatementTemplate);
                for (List<Object> elements : elementsChunk) {
                    profileStatement.clearParameters();
                    physicianStatement.clearParameters();
                    for (int i = 0; i < elements.size(); i++) {
                        if (i < profileColumns.size()) {
                            profileStatement.setObject(i + 1, elements.get(i));
                        } else {
                            physicianStatement.setObject(i - profileColumns.size() + 1, elements.get(i));
                        }
                    }
                    profileStatement.addBatch();
                    physicianStatement.addBatch();

                }
                profileStatement.executeBatch();
                physicianStatement.executeBatch();
                connection.commit();
                profileStatement.clearBatch();
                physicianStatement.clearBatch();
                connection.close();
                handleSaveResultBatch(batchNumber, 1, filename);

                String filepath = prepareInsertPath + filename + suffixPrepare + extension;
                String header = "provider_uuid,facility_npi";
                createFile(filepath, header);
                BufferedWriter writer = new BufferedWriter(new FileWriter(filepath, true));
                for (String row : batchReadyExecute) {
                    writer.write(row);
                    writer.newLine();
                }
                writer.close();
            } catch (SQLException e) {
                handleSaveResultBatch(batchNumber, 0, filename);
                e.printStackTrace();

            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }

        };
    }

    void createFile(String filepath, String header) {
        ReentrantLock lock = new ReentrantLock();
        try {
            lock.lock();
            File file = new File(filepath);
            if (!file.exists()) {
                boolean fileCreated = file.createNewFile();
                // Validate that file actually got created
                if (!fileCreated) {
                    throw new IOException("Unable to create file at specified path.");
                }

                try (BufferedWriter writer = new BufferedWriter(new FileWriter(filepath, true))) {
                    writer.write(header);
                    writer.newLine();
                } catch (Exception e) {
                    throw new Exception(e.getMessage());
                }
            }
        } catch (Exception e) {
            // handle exception
        } finally {
            lock.unlock();
        }
    }

    private Runnable insertGroupProviderChunk(List<List<Object>> elementsChunk, int batchNumber,
            List<String> batchReadyExecute, String filename) {
        return () -> {
            ArrayList<String> groupProviderColumns = new ArrayList<>();
            groupProviderColumns.add("group_id");
            groupProviderColumns.add("group_name");
            groupProviderColumns.add("address");
            groupProviderColumns.add("email");
            groupProviderColumns.add("phone_number");
            groupProviderColumns.add("is_virtual_only");
            groupProviderColumns.add("description");
            groupProviderColumns.add("created_date");
            groupProviderColumns.add("updated_date");
            groupProviderColumns.add("group_specialty");
            groupProviderColumns.add("npi");
            groupProviderColumns.add("city");
            groupProviderColumns.add("state");
            groupProviderColumns.add("value");
            groupProviderColumns.add("quality");
            groupProviderColumns.add("efficiency");
            groupProviderColumns.add("source");
            String joinGroupProviderColumns = String.join(", ", groupProviderColumns);
            String[] array = new String[groupProviderColumns.size()];
            Arrays.fill(array, "?");
            array[groupProviderColumns.size() - 1] = "?::source";
            String joinPrepareValue = String.join(", ", array);
            String groupProviderSqlStatementTemplate = "INSERT INTO group_provider (" + joinGroupProviderColumns + ") "
                    + "VALUES (" + joinPrepareValue + ");";
            try (Connection connection = dataSource.getConnection()) {
                connection.setNetworkTimeout(numOfExecutors, 1000 * 30);
                connection.setAutoCommit(false);
                PreparedStatement groupProviderStatement = connection
                        .prepareStatement(groupProviderSqlStatementTemplate);
                for (List<Object> elements : elementsChunk) {
                    groupProviderStatement.clearParameters();
                    groupProviderStatement.clearParameters();
                    for (int i = 0; i < elements.size(); i++) {
                        groupProviderStatement.setObject(i + 1, elements.get(i));
                    }
                    groupProviderStatement.addBatch();
                }
                groupProviderStatement.executeBatch();
                connection.commit();
                groupProviderStatement.clearBatch();
                connection.close();
                handleSaveResultBatch(batchNumber, 1, filename);
                String filepath = prepareInsertPath + filename + suffixPrepare + extension;
                String header = "group_provider_uuid,npi";
                createFile(filepath, header);
                BufferedWriter writer = new BufferedWriter(new FileWriter(filepath, true));
                for (String row : batchReadyExecute) {
                    writer.write(row);
                    writer.newLine();
                }
                writer.close();
            } catch (SQLException e) {
                handleSaveResultBatch(batchNumber, 0, filename);
                e.printStackTrace();

            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        };
    }

    private void handleSaveResultBatch(int batchNumber, int isSuccess, String filename) {

        String filepath = batchStatusPath + filename + suffixBatchStatus + extension;
        String header = "Batch Number,Insert Status";
        createFile(filepath, header);
        String newRowData = String.format("%d,%d", batchNumber, isSuccess);

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filepath, true))) {
            writer.write(newRowData);
            writer.newLine();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void insertProvider() throws IOException {
        long begin = System.currentTimeMillis();
        insertPractitioner(NYpractitionerFileName, dataPath);
        long end = System.currentTimeMillis();
        long time = end - begin;
        System.out.println();
        System.out.println("Elapsed Time: " + time + " milli seconds");

    }

    public void insertGroupProvider() throws IOException {
        long begin = System.currentTimeMillis();
        insertFacility(NYfacilityFileName, dataPath);
        long end = System.currentTimeMillis();
        long time = end - begin;
        System.out.println();
        System.out.println("Elapsed Time: " + time + " milli seconds");
    }

    public void insertGroupPhysician() throws IOException {
        long begin = System.currentTimeMillis();
        insertPrepareData(NYGroupPhysician, groupPhysicianPath);
        long end = System.currentTimeMillis();
        long time = end - begin;
        System.out.println();
        System.out.println("Elapsed Time: " + time + " milli seconds");
    }

    public void insertFacility(String filename, String filepath) throws IOException {
        List<CompletableFuture<Void>> futures = readFacilityFileCompletableFutureBuilder(filename, filepath);
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    public void insertPractitioner(String filename, String filepath) throws IOException {
        List<CompletableFuture<Void>> futures = readPractitionerFileCompletableFutureBuilder(filename, filepath);
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    public Set<Integer> readFailBatch(String filepath) throws IOException {
        File f = new File(filepath);
        Set<Integer> treeSet = new TreeSet<>();
        if (f.exists()) {
            try (CSVReader csvReader = new CSVReader(
                    new FileReader(filepath))) {
                csvReader.readNext();
                String[] values;
                while ((values = csvReader.readNext()) != null) {
                    // 0: batchNumber
                    // 1: insert status
                    if (Integer.parseInt(values[1].trim()) == 0) {
                        treeSet.add(Integer.parseInt(values[0].trim()));
                    }
                }
            }
        }
        return treeSet;
    }

    public void insertPrepareData(String filename, String filepath) throws IOException {
        List<CompletableFuture<Void>> futures = readGroupPhysicianFileCompletableFutureBuilder(filename, filepath);
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }


    private List<CompletableFuture<Void>> readGroupPhysicianFileCompletableFutureBuilder(String filename, String filepath)
            throws IOException {

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        try (CSVReader csvReader = new CSVReader(new FileReader(filepath + filename + extension))) {
            String batchStatusFileName = filename + suffixBatchStatus + extension;
            String batchStatusFilePath = batchStatusPath + batchStatusFileName;
            File batchStatusFile = new File(batchStatusFilePath);
            Set<Integer> failBatchStatus = readFailBatch(batchStatusFilePath);
            boolean isExistFile = batchStatusFile.exists();
            if (isExistFile && !failBatchStatus.isEmpty()) {
                var waitForDeleteFile = CompletableFuture.supplyAsync(batchStatusFile::delete);
                waitForDeleteFile.join();
            }
            if (isExistFile && failBatchStatus.isEmpty()) {
                throw new ResponseStatusException(HttpStatus.OK, "Insert done");
            }

            String[] values;
            // next to header
            csvReader.readNext();
            Map<String, Integer> index = new HashMap<>();
            index.put("group_provider_uuid", 0);
            index.put("npi", 1);
            index.put("provider_uuid", 2);
            int count = 0;
            List<List<Object>> elementsChunk = new ArrayList<>();
            int batchNumber = 0;

            while ((values = csvReader.readNext()) != null) {
                count++;
                if (!failBatchStatus.isEmpty() && !failBatchStatus.contains(batchNumber)) {
                    if (count == chunkSize) {
                        count = 0;
                        String statusPath = batchStatusPath + filename + suffixBatchStatus + extension;
                        String header = "Batch Number,Insert Status";
                        createFile(statusPath, header);
                        String newRowData = String.format("%d,%d", batchNumber, 1);

                        try (BufferedWriter writer = new BufferedWriter(new FileWriter(statusPath, true))) {
                            writer.write(newRowData);
                            writer.newLine();
                            batchNumber += 1;
                        }
                    }

                } else {
                    if (Objects.equals(values[index.get("provider_uuid")], "")) {
                        count--;
                        continue;
                    }
                    List<Object> elements = new ArrayList<>();
                    elements.add(values[index.get("group_provider_uuid")]); // group_id
                    elements.add(values[index.get("provider_uuid")]); // physician_id
                    DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
                    String currentTimeStamp = LocalDateTime.now(ZoneId.of("UTC")).format(inputFormatter);
                    elements.add(true); // in_group
                    elements.add(currentTimeStamp); // created_at
                    elements.add(currentTimeStamp); // updated_at

                    elementsChunk.add(elements);

                    if (count == chunkSize) {
                        CompletableFuture<Void> future = CompletableFuture.runAsync(
                                insertGroupPhysicianChunk(elementsChunk, batchNumber, filename), numOfExecutors);
                        futures.add(future);
                        elementsChunk = new ArrayList<>();
                        count = 0;
                        batchNumber += 1;

                    }
                }
            }
            if (!elementsChunk.isEmpty()) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(insertGroupPhysicianChunk(elementsChunk, batchNumber, filename),
                        numOfExecutors);
                futures.add(future);
            }
        }
        return futures;
    }

    public Runnable insertGroupPhysicianChunk(List<List<Object>> elementsChunk, int batchNumber, String filename) {
        return () -> {
            ArrayList<String> groupPhysicianColumns = new ArrayList<>();
            groupPhysicianColumns.add("group_id");
            groupPhysicianColumns.add("physician_id");
            groupPhysicianColumns.add("in_group");
            groupPhysicianColumns.add("created_date");
            groupPhysicianColumns.add("updated_date");
            String joinGroupPhysicianColumns = String.join(", ", groupPhysicianColumns);
            String[] array = new String[groupPhysicianColumns.size()];
            Arrays.fill(array, "?");
            String groupPhysicianSqlStatementTemplate = "INSERT INTO group_physician (" + joinGroupPhysicianColumns + ") "
                    + "VALUES (?::uuid, ?::uuid, ?, ?::date, ?::date);";
            try (Connection connection = dataSource.getConnection()) {
                // handle save file
                connection.setNetworkTimeout(numOfExecutors, 1000 * 30);
                connection.setAutoCommit(false);
                PreparedStatement groupPhysicianStatement = connection.prepareStatement(groupPhysicianSqlStatementTemplate);
                for (List<Object> elements : elementsChunk) {
                    groupPhysicianStatement.clearParameters();
                    for (int i = 0; i < elements.size(); i++) {
                        groupPhysicianStatement.setObject(i + 1, elements.get(i));
                    }
                    groupPhysicianStatement.addBatch();

                }
                groupPhysicianStatement.executeBatch();
                connection.commit();
                groupPhysicianStatement.clearBatch();
                handleSaveResultBatch(batchNumber, 1, filename);

            } catch (SQLException e) {
                handleSaveResultBatch(batchNumber, 0, filename);
                e.printStackTrace();
            }
        };
    }

    public List<List<String>> buildChunksDrug(String path) {
        try (CSVReader csvReader = new CSVReader(
                new FileReader(path))) {

            String[] values;
            // next to header
            csvReader.readNext();

            int count = 0;
            List<String> drugStatement = new LinkedList<>();
            List<List<String>> saveFutures = new LinkedList<>();

            while ((values = csvReader.readNext()) != null) {
                count++;
                drugStatement.add(buildDrug(values));
                if (count == chunkSize) {
                    count = 0;
                    saveFutures.add(new LinkedList<>(drugStatement));
                    drugStatement.clear();
                }
            }
            if (!drugStatement.isEmpty()) {
                saveFutures.add(drugStatement);
            }
            return saveFutures;
        } catch (Exception e) {
            log.error("buildChunksData get error with message {}", e.getMessage());
        }
        return Collections.emptyList();
    }

    public void saveDrugsData(List<String> drugStatements, int patchNumber) {

        var source = "DRUG";
        try {
            var startTime = System.currentTimeMillis();
            try (Connection connection = dataSource.getConnection()) {
                connection.setNetworkTimeout(numOfExecutors, 1000 * 30);
                connection.setAutoCommit(false);
                Statement statementContext = connection.createStatement();
                for (String statement : drugStatements) {
                    statementContext.addBatch(statement);
                }
                statementContext.executeBatch();
                connection.commit();
                statementContext.clearBatch();
            } catch (SQLException e) {
                log.error("error insert drug {}", e.getMessage());
            }
            insertBatchStatusRepository.save(new InsertBatchStatus(
                    UUID.randomUUID(),
                    patchNumber,
                    true,
                    source)
            );
            log.info("end batch: {}", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            insertBatchStatusRepository.save(new InsertBatchStatus(
                    UUID.randomUUID(),
                    patchNumber,
                    false,
                    source)
            );
        }

    }

    private CompletableFuture<Void> futureSaveDrugs(List<String> drugStatements, int patchNumber) {
        return CompletableFuture.runAsync(() -> {
            saveDrugsData(drugStatements, patchNumber);
        }, numOfExecutors);
    }

    private String buildDrug(String[] values) {

        var convertValues = new LinkedList<>(Arrays.stream(values).toList());
        convertValues.addFirst(UUID.randomUUID().toString());
        convertValues.add(LocalDateTime.now().toString());
        convertValues.add(LocalDateTime.now().toString());

        var mapColumnName = buildStringToInsertDrug();
        var columnJoining = String.join(",", mapColumnName.values());
        var valuesHaveDateType = List.of("add_date"
                , "effective_date"
                , "updated_at"
                , "created_at");

        List<String> markValues = new LinkedList<>();
        mapColumnName.forEach((k, v) -> {
            if (v.equals("id")) {
                markValues.add(String.format("'%s'::uuid", convertValues.get(k)));
            } else if (valuesHaveDateType.contains(v)) {
                if (convertValues.get(k).isBlank()) {
                    markValues.add("NULL");
                } else {
                    markValues.add(String.format("'%s'", convertValues.get(k)));
                }
            } else {
                if (convertValues.get(k).contains("'")) {
                    var chunkStrings = convertValues.get(k).split("'");
                    var newData = String.join("''", chunkStrings);
                    markValues.add(String.format("'%s'", newData));
                } else if (convertValues.get(k).isBlank()) {
                    markValues.add("NULL");
                } else {
                    markValues.add(String.format("'%s'", convertValues.get(k)));
                }
            }
        });

        var markValuesJoining = String.join(",", markValues);
        return String.format("INSERT INTO drug (%s) VALUES (%s);", columnJoining, markValuesJoining);
    }

    private Map<Integer, String> buildStringToInsertDrug() {
        List<String> drugColumns = List.of("id"
                , "ndc"
                , "label_name"
                , "brand_name"
                , "generic_name"
                , "tier"
                , "tier_original"
                , "prior_authorization"
                , "quantity_limit"
                , "quantity_limit_over_time"
                , "step_therapy"
                , "step_therapy_number"
                , "minimum_age_limit"
                , "maximum_age_limit"
                , "gender"
                , "specialty"
                , "preferred_specialty"
                , "low_cost_generic"
                , "high_cost_generic"
                , "high_cost_brand"
                , "affordable_care_act"
                , "preventive"
                , "package_size"
                , "package_size_unit_of_measure"
                , "package_quantity"
                , "unit_dose_code"
                , "package_description_code"
                , "maintenance"
                , "compound_kit"
                , "rx_cap_plus"
                , "opioid"
                , "strength_per_unit"
                , "mme_conversion_factor"
                , "medical_drug"
                , "rx4less"
                , "drug_application_type"
                , "cms_labeler_code"
                , "manufacturer_name"
                , "add_date"
                , "effective_date"
                , "formulary_name"
                , "updated_at"
                , "created_at");
        Map<Integer, String> mapColumnName = new HashMap<>();
        for (int i = 0; i < drugColumns.size(); i++) {
            mapColumnName.put(i, drugColumns.get(i));
        }

        return mapColumnName;
    }

    public void insertDrugData() {
        var startTime = System.currentTimeMillis();
        var dataChunks = buildChunksDrug(drugDataPath);
        log.info("Done build chunks {}", System.currentTimeMillis() - startTime);
        List<CompletableFuture<Void>> futures = new LinkedList<>();
        for (int i = 0; i < dataChunks.size(); i++) {
            futures.add(futureSaveDrugs(dataChunks.get(i), i));
        }

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        var endTime = System.currentTimeMillis();
        log.info("Done at {}", endTime - startTime);
    }
}
