package com.covet.profile.dto.covet.pharmacy;

import com.covet.profile.dto.CapRxDto.pharmacy.CapPharmacyDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.net.URL;
import java.util.Optional;

@JsonSerialize(as = ImmutablePharmacyDetailDto.class)
@JsonDeserialize(as = ImmutablePharmacyDetailDto.class, builder = ImmutablePharmacyDetailDto.Builder.class)
@Value.Immutable
public interface PharmacyDetailDto {
    @Value.Parameter
    @JsonProperty
    CapPharmacyDto getPharmacy();

    @Value.Parameter
    @JsonProperty
    Double getCost();

    @Value.Parameter
    @JsonProperty
    Optional<URL> getPharmacyImgUrl();

    @Value.Parameter
    @JsonProperty
    Double getDistance();
}
