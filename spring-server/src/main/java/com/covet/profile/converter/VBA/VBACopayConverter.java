package com.covet.profile.converter.VBA;

import com.covet.profile.clients.vba.VBADtos.IDcard.Copays.ImmutableVBACopayDto;
import com.covet.profile.utils.RoundingUtils;
import com.covet.profile.clients.vba.VBADtos.IDcard.Copays.VBACopayDto;

import java.math.BigDecimal;

public class VBACopayConverter {
    public static VBACopayDto vbaCopayToDto(BigDecimal pcpAmount, BigDecimal urgentCareAmount,
                                            BigDecimal specialListAmount, BigDecimal emergencyRoomAmount) {
        return ImmutableVBACopayDto.of(
                RoundingUtils.customRoundValue(pcpAmount),
                RoundingUtils.customRoundValue(urgentCareAmount),
                RoundingUtils.customRoundValue(specialListAmount),
                RoundingUtils.customRoundValue(emergencyRoomAmount)
        );
    }
}
