#!/bin/bash

# Default values
CLUSTER_FILE=""
CLUSTER_NAME=""
HOST_URL=""

# Parse command line arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        -f|--file)
            CLUSTER_FILE="$2"
            shift ;;  # Shift to skip the argument value
        -c|--cluster)
            CLUSTER_NAME="$2"
            shift ;;  # Shift to skip the argument value
        -u|--url)
            HOST_URL="$2"
            shift ;;  # Shift to skip the argument value
        *)
            echo "Unknown parameter passed: $1"
            exit 1 ;;
    esac
    shift  # Shift to process the next parameter
done

# Check if CLUSTER_FILE was set
if [ -z "$CLUSTER_FILE" ]; then
    echo "Cluster file not specified!"
    exit 1
fi

# Check if CLUSTER_NAME was set
if [ -z "$CLUSTER_NAME" ]; then
    echo "Cluster name not specified!"
    exit 1
fi

# Check if HOST_URL was set (assuming you want to)
if [ -z "$HOST_URL" ]; then
    echo "Host URL not specified!"
    exit 1
fi

# Export HOST_URL as an environment variable so that it's accessible to envsubst
export HOST_URL


#create cluster
eksctl create cluster -f "$CLUSTER_FILE"

#make OIDC for cluster
eksctl utils associate-iam-oidc-provider --region us-east-1 --cluster $CLUSTER_NAME --approve
sleep 1m

#only needed for first cluster in aws account
#curl -O https://raw.githubusercontent.com/kubernetes-sigs/aws-load-balancer-controller/v2.5.4/docs/install/iam_policy.json

#aws iam create-policy --policy-name AWSLoadBalancerControllerIAMPolicy --policy-document file://iam_policy.json



#create service account 
eksctl create iamserviceaccount --cluster=$CLUSTER_NAME --namespace=kube-system --name=aws-load-balancer-controller --role-name "${CLUSTER_NAME}EKSLoadBalancerControllerRole" --attach-policy-arn=arn:aws:iam::************:policy/AWSLoadBalancerControllerIAMPolicy --approve 

sleep 1m
#might need to override
#--override-existing-serviceaccounts



#helm repo add eks https://aws.github.io/eks-charts
helm repo update eks
#install load balancer controller
helm install aws-load-balancer-controller eks/aws-load-balancer-controller -n kube-system --set clusterName=$CLUSTER_NAME --set serviceAccount.create=false --set serviceAccount.name=aws-load-balancer-controller

kubectl wait --namespace=kube-system --for=condition=Ready pod -l app.kubernetes.io/name=aws-load-balancer-controller --timeout=300s



#add cluster to kube config file
eksctl utils write-kubeconfig --cluster=$CLUSTER_NAME



envsubst < ingress_config.yaml > ingress_config.tmp.yaml
#create ingress
kubectl apply -f ingress_config.tmp.yaml

# Check if the 'spring-secrets' secret exists and create it if not
if ! kubectl get secret spring-secrets > /dev/null 2>&1; then
  kubectl create secret generic spring-secrets --from-env-file=../../spring-server/.env
  echo "spring-secrets created"
else
  echo "spring-secrets already exists"
fi

# Check if the 'covetql-secrets' secret exists and create it if not
if ! kubectl get secret covetql-secrets > /dev/null 2>&1; then
  kubectl create secret generic covetql-secrets --from-env-file=../../covetql/.env
  echo "covetql-secrets created"
else
  echo "covetql-secrets already exists"
fi