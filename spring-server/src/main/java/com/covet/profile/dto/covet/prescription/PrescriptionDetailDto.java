package com.covet.profile.dto.covet.prescription;

import com.covet.profile.persistence.model.Prescription;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.net.URL;
import java.util.Optional;

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@JsonSerialize(as = ImmutablePrescriptionDetailDto.class)
@JsonDeserialize(as = ImmutablePrescriptionDetailDto.class, builder = ImmutablePrescriptionDetailDto.Builder.class)
@Value.Immutable
public interface PrescriptionDetailDto {
    @Value.Parameter
    @JsonProperty
    Prescription getPrescription();

    @Value.Parameter
    @JsonProperty
    Boolean getIsVerified();

    @Value.Parameter
    @JsonProperty
    Boolean getIsPArequired();

    @Value.Parameter
    @JsonProperty
    Boolean getIsNoPrescription();

    @Value.Parameter
    @JsonProperty
    Optional<URL> getPrescriptionImgUrl();

}

