package com.covet.profile.dto.covet.claim;

import com.covet.profile.persistence.model.Profile;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import java.util.Date;
import java.util.Optional;
import java.util.UUID;

@JsonSerialize(as = ImmutableClaimDto.class)
@JsonDeserialize(as = ImmutableClaimDto.class, builder = ImmutableClaimDto.Builder.class)
@Value.Immutable
public interface ClaimDto {
    @Value.Parameter
    @JsonProperty
    String getClaimCode();

    @Value.Parameter
    @JsonProperty
    UUID getPatientId();

    @Value.Parameter
    @JsonProperty
    UUID getPhysicianId();

    @Value.Parameter
    @JsonProperty
    Boolean getIsPaid();

    @Value.Parameter
    @JsonProperty
    Date getCreatedDate();

    @Value.Parameter
    @JsonProperty
    Double getTotalClaim();

    @Value.Parameter
    @JsonProperty
    int getClaimType();

    @Value.Parameter
    @JsonProperty
    Optional<Profile> getPhysicianProfile();

}