package com.covet.profile.service;

import com.covet.profile.dto.ConfirmSignUpDto;
import com.covet.profile.exception.ResourceNotFoundException;
import com.covet.profile.persistence.model.Profile;
import com.covet.profile.persistence.repository.ProfileRepository;
import com.covet.profile.service.interfaces.ICognitoService;
import com.covet.profile.utils.AuthUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.cognitoidentityprovider.CognitoIdentityProviderAsyncClient;
import software.amazon.awssdk.services.cognitoidentityprovider.CognitoIdentityProviderClient;
import software.amazon.awssdk.services.cognitoidentityprovider.model.*;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Service
public class CognitoService implements ICognitoService {

    private CognitoIdentityProviderClient cognitoClient;
    private CognitoIdentityProviderAsyncClient cognitoAsyncClient;

    @Value("${springdoc.swagger-ui.oauth.client-id}")
    private String clientId;

    @Value("${springdoc.swagger-ui.oauth.client-secret}")
    private String clientSecret;

    @Value("${spring.cloud.aws.s3.user-pool-id}")
    private String USER_POOL_ID;

    private String temporaryPassword = "Password@321";

    private Region region = Region.of("us-east-1");

    private static final String ATTRIBUTE_TYPE_EMAIL = "email";


    private final ProfileRepository profileRepository;

    public CognitoService(CognitoIdentityProviderClient cognitoClient,
            CognitoIdentityProviderAsyncClient cognitoAsyncClient, ProfileRepository profileRepository) {
        this.cognitoClient = cognitoClient;
        this.cognitoAsyncClient = cognitoAsyncClient;
        this.profileRepository = profileRepository;
    }

    public AdminCreateUserRequest createUserWithTemporaryPassword(String email) {
        return AdminCreateUserRequest.builder()
                .userPoolId(USER_POOL_ID)
                .username(email)
                .temporaryPassword(temporaryPassword)
                .userAttributes(
                        AttributeType.builder().name(ATTRIBUTE_TYPE_EMAIL).value(email).build())
                .messageAction(MessageActionType.SUPPRESS)
                .build();
    }

    public List<AdminCreateUserResponse> asyncCreateUsers(List<HashMap<String, String>> users) {

        List<CompletableFuture<AdminCreateUserResponse>> futures = new ArrayList<>();
        for (HashMap<String, String> user : users) {
            String email = user.get(ATTRIBUTE_TYPE_EMAIL);
            futures.add(cognitoAsyncClient.adminCreateUser(createUserWithTemporaryPassword(email)));
        }

        // Wait for all futures to be completed successfully
        List<AdminCreateUserResponse> responses = new ArrayList<>();
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenAcceptAsync(_void -> {
                    for (CompletableFuture<AdminCreateUserResponse> future : futures) {
                        try {
                            responses.add(future.get());
                        } catch (InterruptedException | ExecutionException e) {
                            // Handle any exceptions that occurred during the request
                            System.out.println("Exception occurred: " + e.getMessage());
                            Thread.currentThread().interrupt();
                        }
                    }
                })
                .join();

        // Print the successful responses
        for (AdminCreateUserResponse response : responses) {
            System.out.println("User created: " + response.user().username());
        }
        return responses;
    }

    public void asyncDeleteUsers(List<String> emails) {
        try {
            List<CompletableFuture<AdminDeleteUserResponse>> futures = new ArrayList<>();
            for (String email : emails) {
                AdminDeleteUserRequest request = AdminDeleteUserRequest.builder()
                        .userPoolId(USER_POOL_ID)
                        .username(email)
                        .build();
                futures.add(cognitoAsyncClient.adminDeleteUser(request));
            }
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (CognitoIdentityProviderException e) {
            System.err.println(e.awsErrorDetails().errorMessage());
        } finally {
            cognitoClient.close();
        }
    }

    public String calculateSecretHash(String clientId, String clientSecret, String username) {
        final String HMAC_SHA256_ALGORITHM = "HmacSHA256";

        SecretKeySpec signingKey = new SecretKeySpec(
                clientSecret.getBytes(StandardCharsets.UTF_8),
                HMAC_SHA256_ALGORITHM);
        try {
            Mac mac = Mac.getInstance(HMAC_SHA256_ALGORITHM);
            mac.init(signingKey);
            mac.update(username.getBytes(StandardCharsets.UTF_8));
            byte[] rawHmac = mac.doFinal(clientId.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(rawHmac);
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "can't calculate the secret hash!");
        }
    }

    public void confirmSignUp(ConfirmSignUpDto confirmSignUpDto) {
        String username = confirmSignUpDto.getUsername();
        String newPassword = confirmSignUpDto.getNewPassword();
        String confirmPassword = confirmSignUpDto.getConfirmPassword();
        if (!newPassword.equals(confirmPassword)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "new password and confirm password don't match!");
        }

        UUID cognitoId = UUID.nameUUIDFromBytes(username.getBytes());
        Optional<Profile> profile = profileRepository.findById(cognitoId);
        if (!profile.isPresent()) {
            throw new ResourceNotFoundException("The email is not pre-made before");
        }

        String secretHash = calculateSecretHash(clientId, clientSecret, username);
        Map<String, String> authParameters = new HashMap<>();
        authParameters.put("USERNAME", username);
        authParameters.put("PASSWORD", temporaryPassword);
        authParameters.put("SECRET_HASH", secretHash);
        AdminInitiateAuthRequest initiateAuthRequest = AdminInitiateAuthRequest.builder()
                .clientId(clientId)
                .userPoolId(USER_POOL_ID)
                .authFlow(AuthFlowType.ADMIN_NO_SRP_AUTH)
                .authParameters(authParameters)
                .build();

        AdminInitiateAuthResponse initiateAuthResponse = cognitoClient.adminInitiateAuth(initiateAuthRequest);

        String session = initiateAuthResponse.session();

        Map<String, String> challengeResponses = new HashMap<>();
        challengeResponses.put("USERNAME", username);
        challengeResponses.put("NEW_PASSWORD", newPassword);
        challengeResponses.put("SECRET_HASH", secretHash);
        AdminRespondToAuthChallengeRequest respondToAuthChallengeRequest = AdminRespondToAuthChallengeRequest.builder()
                .clientId(clientId)
                .userPoolId(USER_POOL_ID)
                .challengeName(initiateAuthResponse.challengeName())
                .challengeResponses(challengeResponses)
                .session(session)
                .build();
        cognitoClient.adminRespondToAuthChallenge(respondToAuthChallengeRequest);
        cognitoClient.close();
    }

    public String getUserEmailFromCognito() {
        UUID cognitoId = AuthUtils.getCognitoId();
        CognitoIdentityProviderClient cognitoIdentityProviderClient = CognitoIdentityProviderClient.builder()
                .region(region)
                .build();

        AdminGetUserRequest adminGetUserRequest = AdminGetUserRequest.builder()
                .userPoolId(USER_POOL_ID)
                .username(cognitoId.toString())
                .build();

        AdminGetUserResponse adminGetUserResponse = cognitoIdentityProviderClient.adminGetUser(adminGetUserRequest);
        List<AttributeType> userAttributes = adminGetUserResponse.userAttributes();

        List<AttributeType> emailAttributes = userAttributes.stream()
                .filter(attribute -> ATTRIBUTE_TYPE_EMAIL.equals(attribute.name()))
                .toList();

        if (!emailAttributes.isEmpty()) {
            return emailAttributes.get(0).value();
        }

        return null;
    }
}
