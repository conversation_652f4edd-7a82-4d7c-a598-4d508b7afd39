package com.covet.profile.dto.covet.appointment;

import com.covet.profile.persistence.model.Physician;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

import javax.validation.constraints.FutureOrPresent;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Optional;
import java.util.UUID;

@JsonSerialize(as = ImmutableAppointmentDto.class)
@JsonDeserialize(as = ImmutableAppointmentDto.class, builder = ImmutableAppointmentDto.Builder.class)
@Value.Immutable
public interface AppointmentDto {

    @Value.Parameter
    @JsonProperty
    Optional<UUID> getId();

    @Value.Parameter
    @JsonProperty
    Optional<UUID> getPatientId();

    @Value.Parameter
    @JsonProperty
    UUID getPhysicianId();

    @JsonInclude()
    @Value.Parameter
    @JsonProperty
    @FutureOrPresent
    Date getStartTime();

    @JsonInclude()
    @Value.Parameter
    @JsonProperty
    @FutureOrPresent
    Date getEndTime();

    @Value.Parameter
    @JsonProperty
    Boolean getIsVirtual();

    @Value.Parameter
    @JsonProperty
    Optional<Integer> getAppointmentType();

    @Value.Parameter
    @JsonProperty
    Optional<String> getDescription();

    @Value.Parameter
    @JsonProperty
    Optional<String> getStatus();

    @Value.Parameter
    @JsonProperty
    String getDaysOfWeek();

    @Value.Parameter
    @JsonProperty
    Optional<LocalDateTime> getAppointmentDate();

    @Value.Parameter
    @JsonProperty
    Optional<Physician> getPhysician();

}
