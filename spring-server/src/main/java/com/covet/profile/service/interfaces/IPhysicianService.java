package com.covet.profile.service.interfaces;

import com.covet.profile.dto.covet.appointment.AppointmentDto;
import com.covet.profile.dto.covet.provider.PhysicianDto;

import java.util.Optional;
import java.util.UUID;

public interface IPhysicianService {
    AppointmentDto createOrUpdateAppointment(AppointmentDto newAppointmentDto);

    Optional<PhysicianDto> getPhysicianById(UUID physicianId);

    Optional<PhysicianDto> findPhysicianByNameAndNpi(String providerName, String npi);
}
