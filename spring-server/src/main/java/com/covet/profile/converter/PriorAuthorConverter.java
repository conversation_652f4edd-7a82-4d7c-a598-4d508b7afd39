package com.covet.profile.converter;

import java.util.Optional;
import java.util.UUID;

import com.covet.profile.dto.ImmutablePriorAuthorDto;
import com.covet.profile.dto.PriorAuthorDto;
import com.covet.profile.persistence.model.Claim;
import com.covet.profile.persistence.model.PriorAuthor;
import com.covet.profile.persistence.model.Profile;

public class PriorAuthorConverter {
    private PriorAuthorConverter() {
    }

    public static PriorAuthorDto priorAuthorToPriorAuthorDto(PriorAuthor priorAuthor) {
        return ImmutablePriorAuthorDto.of(
                Optional.ofNullable(priorAuthor.getId()),
                priorAuthor.getPatientId(),
                priorAuthor.getReqMed(),
                priorAuthor.getReqOp(),
                priorAuthor.getCreatedDate(),
                priorAuthor.getAuthorStatus(),
                priorAuthor.getIsHistorical(),
                priorAuthor.getIsCurrent(),
                priorAuthor.getClaim().getClaimCode());
    }

    public static PriorAuthor priorAuthorDtoToPriorAuthor(PriorAuthorDto priorAuthorDto, UUID physicianId,
            UUID patientId, Profile profile, Claim claim) {
        return new PriorAuthor(
                priorAuthorDto.getId().orElse(null),
                patientId,
                priorAuthorDto.getRequestMedication(),
                priorAuthorDto.getRequestOperation(),
                priorAuthorDto.getCreatedDate(),
                physicianId,
                priorAuthorDto.getAuthorStatus(),
                priorAuthorDto.getIsHistorical(),
                priorAuthorDto.getIsCurrent(), profile, claim);
    }
}
