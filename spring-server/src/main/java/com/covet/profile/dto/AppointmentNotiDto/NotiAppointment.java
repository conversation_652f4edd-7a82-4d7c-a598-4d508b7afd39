package com.covet.profile.dto.AppointmentNotiDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class NotiAppointment implements Serializable {
    String providerName;
    String providerNpi;
    String appointmentId;
    String appointmentDate;
    String appointmentTime;
    String appointmentType;

    @JsonProperty("subscriberID_memberSeq")
    String subscriberIDMemberSeq;
}
