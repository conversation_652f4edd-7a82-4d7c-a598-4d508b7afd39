package com.covet.profile.dto.PrescriptionSchedulerDto;

import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class NotificationDto {
    private UUID notificationId;
    private UUID cognitoId;
    private UUID templateId;
    private UUID schedulerId;
    private String startTime;
    private Boolean isPushed;
    private Boolean isRead;
    private Boolean isActive;
}
