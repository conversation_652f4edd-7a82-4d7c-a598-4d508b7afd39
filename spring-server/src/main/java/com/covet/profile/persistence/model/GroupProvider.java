package com.covet.profile.persistence.model;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import com.sun.istack.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;
import java.util.Set;
import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "group_provider")
@JsonIdentityInfo(
        generator = ObjectIdGenerators.PropertyGenerator.class,
        property = "groupId")
@FieldNameConstants
public class GroupProvider {
    @Id
    @Column(name = "group_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID groupId;

    @Column(name = "group_name")
    @NotNull
    private String groupName;

    @Column(name = "description")
    @NotNull
    private String groupDescription;

    @Column(name = "address")
    @NotNull
    private String groupAddress;

    @Column(name = "email")
    @NotNull
    private String groupEmail;

    @Column(name = "phone_number")
    @NotNull
    private String groupPhoneNumber;

    @Column(name = "is_virtual_only")
    @NotNull
    private Boolean isVirtualOnly;

    @Column(name = "group_specialty")
    @NotNull
    private String groupSpecialty;

    @Column(name = "npi")
    private String npi;

    @Column(name = "city")
    private String city;

    @Column(name = "state")
    private String state;

    @Column(name = "value")
    private Double value;

    @Column(name = "quality")
    private Double quality;

    @Column(name = "efficiency")
    private Double efficiency;

    @Column(name = "source")
    private String source;

    @Column(name = "latitude")
    private Double latitude;

    @Column(name = "longitude")
    private Double longitude;

    @JsonIgnore
    @OneToMany(mappedBy = "group", fetch = FetchType.LAZY)
    Set<GroupPhysician> physicians;

    @JsonIgnore
    @OneToOne(mappedBy = "groupProvider", cascade = CascadeType.REMOVE, fetch = FetchType.LAZY)
    @PrimaryKeyJoinColumn
    private GroupRating groupRating;
}
