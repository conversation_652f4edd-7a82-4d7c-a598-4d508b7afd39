create table health_care_method (
                        method_id uuid not null,
                        method_name varchar (256) not null,
                        cost decimal,
                        primary key (method_id)
                      );

create table discount (
                        id uuid not null,
                        method_id uuid not null,
                        discount_type varchar (256) not null,
                        discount_code varchar (20) not null,
                        created_date timestamp not null,
                        expired_date timestamp not null,
                        primary key (id),
                        constraint fk_discount_health_care_method foreign key (method_id) references health_care_method
                      );

create table visit_cost (
                        id uuid not null,
                        physician_id uuid not null,
                        virtual_visit_cost decimal,
                        visit_cost decimal,
                        primary key (id),
                        constraint fk_virtual_visit_physician foreign key (physician_id) references physician
                      );

create table health_check (
                        claim_id uuid not null,
                        method_id uuid not null,
                        primary key (claim_id, method_id)
                      );