package com.covet.profile.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@UtilityClass
public class JsonUtils {
    public static final TypeReference<Map<String, Object>> JSON_OBJ_REF = new TypeReference<>() {
    };

    static final ObjectMapper om = new ObjectMapper();

    static {
        om.registerModule(new JavaTimeModule());
        om.setDefaultPropertyInclusion(JsonInclude.Include.NON_NULL);
        om.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        om.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        om.registerModule(new Jdk8Module());
    }

    public static ObjectMapper objectMapper() {
        return om;
    }

    public static String toJson(Object o) {
        if (o == null) return "";
        try {
            return om.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            log.error("JsonUtil.toJson: {}", e.getMessage());
            return "";
        }
    }

    public static <T> T toObj(Object json, Class<T> tClass) {
        try {
            if (json instanceof String s) {
                return om.readValue(s, tClass);
            }
            return om.readValue(om.writeValueAsString(json), tClass);
        } catch (JsonProcessingException e) {
            log.error("JsonUtil.toObj: {}", e.getMessage());
            return null;
        }
    }

    public static <T> T toObj(Object json, TypeReference<T> tClass) {
        try {
            return om.readValue(json.toString(), tClass);
        } catch (JsonProcessingException e) {
            log.error("JsonUtil.toObj: {}", e.getMessage());
            return null;
        }
    }

    public static <T> Map<Integer, String> mapFieldObject(Class<T> tClass) {
        Map<Integer, String> map = new HashMap<>();
        List<Field> drugFields = Arrays.stream(tClass.getDeclaredFields())
                .filter(field -> !field.getName().equals("id"))
                .toList();
        for (int i = 0; i < drugFields.size(); i++) {
            map.put(i, drugFields.get(i).getName());
        }
        return map;
    }
}
