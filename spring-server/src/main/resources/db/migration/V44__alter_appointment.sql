DO $$
BEGIN
  IF EXISTS(SELECT *
    FROM information_schema.columns
    WHERE table_name='appointment')
  THEN
    BEGIN
            ALTER TABLE appointment
            ALTER COLUMN appointment_type TYPE integer USING (trim(appointment_type)::integer);

            ALTER TABLE "public"."appointment" ADD column daysOfWeek varchar(70);
            ALTER TABLE "public"."appointment" ADD column appointmentDate timestamp;
    END;
END IF;
END $$;