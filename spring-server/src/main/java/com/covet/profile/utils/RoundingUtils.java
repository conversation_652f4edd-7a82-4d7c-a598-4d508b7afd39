package com.covet.profile.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class RoundingUtils {
    public static BigDecimal customRoundValue(BigDecimal value) {
        if (value != null) {
            // Check if the value is greater than or equal to x.5
            if (value.remainder(BigDecimal.ONE).compareTo(new BigDecimal("0.5")) >= 0) {
                // Round half up
                return value.setScale(0, RoundingMode.HALF_UP);
            } else {
                // Round half down
                return value.setScale(0, RoundingMode.HALF_DOWN);
            }
        }
        return BigDecimal.ZERO;
    }

    public static double roundDoubleValue(double value) {
        return (double) Math.round(value);
    }
}
