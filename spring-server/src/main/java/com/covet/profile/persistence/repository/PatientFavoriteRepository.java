package com.covet.profile.persistence.repository;

import com.covet.profile.persistence.model.PatientFavorite;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface PatientFavoriteRepository extends JpaRepository<PatientFavorite, UUID> {

    @Query("""
            select count(*) from PatientFavorite pf
            where pf.patientId = :patientId and pf.favoriteId = :favoriteId and pf.isActive = true
            """)
    Long existPatientFavorite(@Param("patientId") UUID patientId, @Param("favoriteId") UUID favoriteId);

    @Query("""
            select pf from PatientFavorite pf
            where pf.patientId = :patientId and pf.favoriteId = :favoriteId
            """)
    Optional<PatientFavorite> findPatientFavorite(@Param("patientId") UUID patientId, @Param("favoriteId") UUID favoriteId);

    @Query(value = """
            select pf from PatientFavorite pf
            where pf.patientId = :patientId and pf.favoriteType = :favoriteType and pf.isActive = true
            """,
            countQuery = """
                    select count(*) from PatientFavorite pf
                    where pf.patientId = :patientId and pf.favoriteType = :favoriteType and pf.isActive = true
                    """)
    Page<PatientFavorite> findAllPatientFavorite(@Param("patientId") UUID patientId,
            @Param("favoriteType") String favoriteType,
            PageRequest pageRequest);

    @Query(value = """
            select pf.favoriteId from PatientFavorite pf
            where pf.patientId = :patientId
            and pf.favoriteType = :favoriteType
            and pf.isActive = true
            and pf.favoriteId in (:inputFavoriteIds)
            """)
    List<UUID> findOwnPatientFavorites(@Param("patientId") UUID patientId,
            @Param("favoriteType") String favoriteType, @Param("inputFavoriteIds") List<UUID> inputFavoriteIds);

    @Query(value = """
            select pf.favoriteId from PatientFavorite pf
            where pf.patientId = :patientId and pf.favoriteType = :favoriteType and pf.isActive = true
            """)
    Page<UUID> findPatientFavoritesPagination(@Param("patientId") UUID patientId,
            @Param("favoriteType") String favoriteType, Pageable pageable);
}
