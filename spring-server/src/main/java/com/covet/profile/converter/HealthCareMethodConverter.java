package com.covet.profile.converter;

import java.util.Optional;

import com.covet.profile.dto.covet.health_check.HealthCareMethodDto;
import com.covet.profile.dto.covet.health_check.ImmutableHealthCareMethodDto;
import com.covet.profile.persistence.model.HealthCareMethod;

public class HealthCareMethodConverter {
    private HealthCareMethodConverter() {
    }

    public static HealthCareMethodDto healthCareMethodToDto(HealthCareMethod healthCareMethod) {
        return ImmutableHealthCareMethodDto.of(Optional.ofNullable(healthCareMethod.getMethodId()),
                healthCareMethod.getMethodName(), healthCareMethod.getCost());
    }

    public static HealthCareMethod dtoToHealthCareMethod(HealthCareMethodDto healthCareMethodDto) {
        return new HealthCareMethod(healthCareMethodDto.getMethodId().or<PERSON>lse(null), healthCareMethodDto.getMethodName(),
                healthCareMethodDto.getCost());
    }
}
