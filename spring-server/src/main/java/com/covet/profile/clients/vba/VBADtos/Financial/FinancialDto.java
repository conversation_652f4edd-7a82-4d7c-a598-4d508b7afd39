package com.covet.profile.clients.vba.VBADtos.Financial;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.immutables.value.Value;

@JsonSerialize(as = ImmutableFinancialDto.class)
@JsonDeserialize(as = ImmutableFinancialDto.class, builder = ImmutableFinancialDto.Builder.class)
@Value.Immutable
public interface FinancialDto {

    @Value.Parameter
    @JsonProperty
    String getSubscriberID();

    @Value.Parameter
    @JsonProperty
    String getMemberSeq();

    @Value.Parameter
    @JsonProperty
    FinancialItemDto data();

}
